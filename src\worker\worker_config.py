# app/worker/worker_config.py

# General settings
task_serializer = "json"
accept_content = ["json"]
result_serializer = "json"
enable_utc = True
timezone = "UTC"
worker_hijack_root_logger = False

# Task execution settings
task_acks_late = True  # Tasks are acknowledged after execution (not when received)
task_reject_on_worker_lost = True  # Tasks will be requeued if worker crashes
task_time_limit = 600  # Hard time limit in seconds for tasks
task_soft_time_limit = 500  # Soft time limit

# Result settings
result_expires = 3600  # Results expire after 1 hour
result_backend_always_retry = True  # Always retry connecting to the result backend

# Worker concurrency and prefetch settings - can be overridden in command line
worker_concurrency = 4  # Default number of worker processes
worker_prefetch_multiplier = 1  # One task per worker process at a time

# Retry settings
broker_connection_retry = True
broker_connection_retry_on_startup = True
broker_connection_max_retries = 10

# Optional: Configure task routes
task_routes = {
    "src.worker.tasks.lima.*": {"queue": "enrichment"},
    "src.worker.tasks.proxycurl.*": {"queue": "enrichment"},
    "src.worker.tasks.leadmagic.*": {"queue": "enrichment"},
    "src.worker.tasks.prospeo.*": {"queue": "enrichment"},
    "src.worker.tasks.findymail.*": {"queue": "enrichment"},
    "src.worker.tasks.millionverifier.*": {"queue": "enrichment"},
    "src.worker.tasks.http.*": {"queue": "enrichment"},
    "src.worker.tasks.openai.*": {"queue": "llm"},
    "src.worker.tasks.perplexity.*": {"queue": "llm"},
    "src.worker.tasks.llm.*": {"queue": "llm"},
    "src.worker.tasks.chaining.*": {"queue": "default"},
    "src.worker.tasks.db.*": {"queue": "default"},
    "src.worker.tasks.csv.*": {"queue": "default"},
}

# Task queues with their priorities
task_queues = {}

# Optional: Set default queue
task_default_queue = "default"
