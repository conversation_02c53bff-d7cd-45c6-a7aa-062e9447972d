name: ECS GHA Workflow
# CICD

on:
  push:
    branches:
      - 'c303-multidocker-dev'
      - 'dev'
      - 'main'


jobs:
  deploy:
    runs-on: ubuntu-latest
    
    environment: >-
      ${{ 
        github.ref == 'refs/heads/dev' && 'Development' ||
        github.ref == 'refs/heads/c303-multidocker-dev' && 'Development' ||
        github.ref == 'refs/heads/main' && 'Production' || ''
      }}

    
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Set Development Environment Variables
        if: github.ref == 'refs/heads/dev' || github.ref == 'refs/heads/c303-multidocker-dev'
        run: |
          echo "ECS_CLUSTER=outbond-dev-ecs-cluster" >> $GITHUB_ENV
          echo "AWS_REGION=us-east-1" >> $GITHUB_ENV
          echo "ECR_REPOS=outbond-ecr-fastapi" >> $GITHUB_ENV
          echo "ECR_REPOS_WORKERS=outbond-ecr-workers" >> $GITHUB_ENV
          echo "ECS_SERVICES_API=fastapi" >> $GITHUB_ENV
          echo "ECS_TASK_DEF_API=fastapi-service" >> $GITHUB_ENV
          echo "ECS_SERVICES_WORKERS=worker-default,worker-enrichment,worker-llm" >> $GITHUB_ENV
          echo "ECS_TASK_DEF_WORKERS=worker-default-service,worker-enrichment-service,worker-llm-service" >> $GITHUB_ENV
          echo "ECS_SERVICE_FLOWER=flower" >> $GITHUB_ENV
          echo "ECS_TASK_DEF_FLOWER=flower-service" >> $GITHUB_ENV
          echo "DESIRED_TASKS=1" >> $GITHUB_ENV
          echo "DESIRED_TASKS_CELERY=1" >> $GITHUB_ENV
          echo "DESIRED_TASKS_FLOWER=1" >> $GITHUB_ENV
          echo "AWS_ACCOUNT_ID=************" >> $GITHUB_ENV

      - name: Set Production Environment Variables
        if: github.ref == 'refs/heads/main'
        run: |
          echo "ECS_CLUSTER=outbond-prod-ecs-cluster" >> $GITHUB_ENV
          echo "AWS_REGION=us-east-1" >> $GITHUB_ENV
          echo "ECR_REPOS=outbond-ecr-fastapi" >> $GITHUB_ENV
          echo "ECR_REPOS_WORKERS=outbond-ecr-workers" >> $GITHUB_ENV
          echo "ECS_SERVICES_API=fastapi" >> $GITHUB_ENV
          echo "ECS_TASK_DEF_API=fastapi-service" >> $GITHUB_ENV
          echo "ECS_SERVICES_WORKERS=worker-default,worker-enrichment,worker-llm" >> $GITHUB_ENV
          echo "ECS_TASK_DEF_WORKERS=worker-default-service,worker-enrichment-service,worker-llm-service" >> $GITHUB_ENV
          echo "ECS_SERVICE_FLOWER=flower" >> $GITHUB_ENV
          echo "ECS_TASK_DEF_FLOWER=flower-service" >> $GITHUB_ENV
          echo "DESIRED_TASKS=1" >> $GITHUB_ENV
          echo "DESIRED_TASKS_CELERY=1" >> $GITHUB_ENV
          echo "DESIRED_TASKS_FLOWER=1" >> $GITHUB_ENV
          echo "AWS_ACCOUNT_ID=************" >> $GITHUB_ENV

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
            
      - name: Log in to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build Docker Image | FastAPI
        run: |
          docker build -f Dockerfile.API -t base-image .
      
      - name: Push Docker Image to ECR | FastAPI
        run: |
          IMAGE_TAG="latest"
          COMMIT_TAG="release-$(git rev-parse --short HEAD)"
          echo "COMMIT TAG ${COMMIT_TAG}"
          echo "IMAGE TAG ${IMAGE_TAG}"
          echo "AWS Account ID ${{ env.AWS_ACCOUNT_ID }}"
          echo "AWS Region ${{ env.AWS_REGION }}"
          echo "ECR Repository ${{ env.ECR_REPOS }}"
          
          ECR_URI_LATEST="${{ env.AWS_ACCOUNT_ID }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com/${{ env.ECR_REPOS }}:$IMAGE_TAG"
          ECR_URI_COMMIT="${{ env.AWS_ACCOUNT_ID }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com/${{ env.ECR_REPOS }}:$COMMIT_TAG"
          echo "URI Latest $ECR_URI_LATEST"
          echo "URI COMMIT $ECR_URI_COMMIT"
          docker tag base-image $ECR_URI_LATEST
          docker tag base-image $ECR_URI_COMMIT
          docker push $ECR_URI_LATEST
          docker push $ECR_URI_COMMIT
          
      - name: Update ECS Task Definition and Service | FastAPI
        run: |
          COMMIT_TAG="release-$(git rev-parse --short HEAD)"
          
          # Fetching existing task definition
          TASK_DEF=$(aws ecs describe-task-definition --task-definition ${{ env.ECS_TASK_DEF_API }})

          # Updating task definition container image
          NEW_TASK_DEF=$(echo $TASK_DEF | jq --arg IMAGE_URI "${{ env.AWS_ACCOUNT_ID }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com/${{ env.ECR_REPOS }}:$COMMIT_TAG" \
            '.taskDefinition | 
              del(.taskDefinitionArn, .revision, .status, .registeredAt, .registeredBy, .requiresAttributes, .compatibilities) | 
              .containerDefinitions[0].image = $IMAGE_URI')

          # Registering updated task definition
          REGISTERED_TASK_DEF=$(aws ecs register-task-definition --cli-input-json "$NEW_TASK_DEF")

          # Extracting task definition ARN
          NEW_TASK_DEF_ARN=$(echo $REGISTERED_TASK_DEF | jq -r '.taskDefinition.taskDefinitionArn')

          # Updating ECS service with new task definition
          aws ecs update-service \
            --cluster ${{ env.ECS_CLUSTER }} \
            --service ${{ env.ECS_SERVICES_API }} \
            --task-definition $NEW_TASK_DEF_ARN \
            --desired-count ${{ env.DESIRED_TASKS }}
            
      - name: Waiting for FastAPI Service to Stabilize
        run: |
          aws ecs wait services-stable \
            --cluster ${{ env.ECS_CLUSTER }} \
            --services ${{ env.ECS_SERVICES_API }}
          echo "FastAPI service is now stable."

      - name: Build Docker Image | Workers
        run: |
          docker build -f Dockerfile.WORKERS -t worker-image .
      
      - name: Push Docker Image to ECR | Workers
        run: |
          IMAGE_TAG="latest"
          COMMIT_TAG="release-$(git rev-parse --short HEAD)"
          echo "COMMIT TAG ${COMMIT_TAG}"
          echo "IMAGE TAG ${IMAGE_TAG}"
          echo "AWS Account ID ${{ env.AWS_ACCOUNT_ID }}"
          echo "AWS Region ${{ env.AWS_REGION }}"
          echo "ECR Repository ${{ env.ECR_REPOS_WORKERS }}"
          
          ECR_URI_LATEST="${{ env.AWS_ACCOUNT_ID }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com/${{ env.ECR_REPOS_WORKERS }}:$IMAGE_TAG"
          ECR_URI_COMMIT="${{ env.AWS_ACCOUNT_ID }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com/${{ env.ECR_REPOS_WORKERS }}:$COMMIT_TAG"
          echo "URI Latest $ECR_URI_LATEST"
          echo "URI COMMIT $ECR_URI_COMMIT"
          docker tag worker-image $ECR_URI_LATEST
          docker tag worker-image $ECR_URI_COMMIT
          docker push $ECR_URI_LATEST
          docker push $ECR_URI_COMMIT

      - name: Update ECS Task Definition and Service | Celery Workers
        run: |
          COMMIT_TAG="release-$(git rev-parse --short HEAD)"
          IFS=',' read -r -a SERVICE_ARRAY <<< "${{ env.ECS_SERVICES_WORKERS }}"
          IFS=',' read -r -a TASK_DEF_ARRAY <<< "${{ env.ECS_TASK_DEF_WORKERS }}"

          if [ ${#SERVICE_ARRAY[@]} -ne ${#TASK_DEF_ARRAY[@]} ]; then
            echo "Error: Mismatch between services and task definitions count"
            exit 1
          fi

          for i in "${!SERVICE_ARRAY[@]}"; do
            SERVICE=${SERVICE_ARRAY[$i]}
            TASK_DEF_NAME=${TASK_DEF_ARRAY[$i]}

            # Fetching existing task definition
            TASK_DEF=$(aws ecs describe-task-definition --task-definition $TASK_DEF_NAME)

            # Updating task definition container image
            NEW_TASK_DEF=$(echo $TASK_DEF | jq --arg IMAGE_URI "${{ env.AWS_ACCOUNT_ID }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com/${{ env.ECR_REPOS_WORKERS }}:$COMMIT_TAG" \
              '.taskDefinition | 
                del(.taskDefinitionArn, .revision, .status, .registeredAt, .registeredBy, .requiresAttributes, .compatibilities) | 
                .containerDefinitions[0].image = $IMAGE_URI')

            # Registering updated task definition
            REGISTERED_TASK_DEF=$(aws ecs register-task-definition --cli-input-json "$NEW_TASK_DEF")

            # Extracting task definition ARN
            NEW_TASK_DEF_ARN=$(echo $REGISTERED_TASK_DEF | jq -r '.taskDefinition.taskDefinitionArn')

            # Updating ECS service with new task definition
            aws ecs update-service \
              --cluster ${{ env.ECS_CLUSTER }} \
              --service $SERVICE \
              --task-definition $NEW_TASK_DEF_ARN \
              --desired-count ${{ env.DESIRED_TASKS_CELERY }}
          done
          
      - name: Waiting for Celery Workers to Stabilize
        run: |
          IFS=',' read -r -a SERVICE_ARRAY <<< "${{ env.ECS_SERVICES_WORKERS }}"
          
          for SERVICE in "${SERVICE_ARRAY[@]}"; do
            echo "Waiting for $SERVICE to stabilize..."
            aws ecs wait services-stable \
              --cluster ${{ env.ECS_CLUSTER }} \
              --services $SERVICE
            echo "$SERVICE service is now stable."
          done
          
      - name: Update ECS Task Definition and Service | Flower Dashboard
        run: |
          COMMIT_TAG="release-$(git rev-parse --short HEAD)"
          
          # Fetching existing task definition
          TASK_DEF=$(aws ecs describe-task-definition --task-definition ${{ env.ECS_TASK_DEF_FLOWER }})

          # Updating task definition container image
          NEW_TASK_DEF=$(echo $TASK_DEF | jq --arg IMAGE_URI "${{ env.AWS_ACCOUNT_ID }}.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com/${{ env.ECR_REPOS_WORKERS }}:$COMMIT_TAG" \
            '.taskDefinition | 
              del(.taskDefinitionArn, .revision, .status, .registeredAt, .registeredBy, .requiresAttributes, .compatibilities) | 
              .containerDefinitions[0].image = $IMAGE_URI')

          # Registering updated task definition
          REGISTERED_TASK_DEF=$(aws ecs register-task-definition --cli-input-json "$NEW_TASK_DEF")

          # Extracting task definition ARN
          NEW_TASK_DEF_ARN=$(echo $REGISTERED_TASK_DEF | jq -r '.taskDefinition.taskDefinitionArn')

          # Updating ECS service with new task definition
          aws ecs update-service \
            --cluster ${{ env.ECS_CLUSTER }} \
            --service ${{ env.ECS_SERVICE_FLOWER }} \
            --task-definition $NEW_TASK_DEF_ARN \
            --desired-count ${{ env.DESIRED_TASKS_FLOWER }}
            
      - name: Waiting for Flower Dashboard to Stabilize
        run: |
          aws ecs wait services-stable \
            --cluster ${{ env.ECS_CLUSTER }} \
            --services ${{ env.ECS_SERVICE_FLOWER }}
          echo "Flower dashboard service is now stable."