"""Define tools for the ReAct agent."""

from typing import Optional, Any, cast, List, Tuple, Dict, Union, ForwardRef, Annotated as TypingAnnotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from langchain_community.tools.tavily_search import TavilySearchResults
from typing import Annotated
from langchain_core.tools import InjectedToolArg, Tool, InjectedToolCallId
from agent.configuration import Configuration
from langchain_community.document_loaders.firecrawl import FireCrawlLoader
from pydantic import BaseModel, Field
from enum import Enum
import os
from dotenv import load_dotenv
from src.outbond_ai_assistant.configuration import Configuration
import re
from .agent_db import get_table_data, upsert_smart_column, db_run_column, get_table_filters, update_table_filters, db_run_sql_query, check_column_type_exists, create_linkedin_search_column, get_table_row_count, create_table_row, get_or_create_linkedin_search_column, save_profiles_to_cells, get_table_organization_id, poll_cell_until_complete, create_linkedin_profile_columns, save_linkedin_profiles_to_separate_columns, update_column_description
from .utils import remove_keys_from_cell_details
from .prompts import AI_FORMULA_PROMPT_GENERATOR, BOND_AI_RESEARCHER_COLUMN_PROMPT, AI_MESSAGE_COPYWRITER_PROMPT
from .utils import load_chat_model
from langchain_core.messages import SystemMessage, HumanMessage, ToolMessage
from datetime import datetime
from .filter_models import FilterType as LinkedInFilterType, PersonSearchRequest
from firecrawl import FirecrawlApp
import json
import requests
from .prompts import TABLE_SUMMARY_PROMPT
from langgraph.types import Command
import time
from .agent_db import poll_cell_until_complete
from langgraph.config import get_stream_writer
from src.outbond_ai_assistant_v2.supabase.validate_injection_sequance.validate_injection_sequance import validate_injection_sequence
from src.outbond_ai_assistant_v2.supabase.validate_injection_sequance.models import ValidateInjectionSequenceRequest, TargetDataType
from .utils import load_chat_model_non_thinking

load_dotenv()

FIRECRAWL_API_KEY = os.getenv("FIRECRAWL_API_KEY")
CRUSTDATA_API_KEY = os.getenv("CRUSTDATA_API_KEY")


# Define models for the table data tool
class Operator(str, Enum):
    """Filter operators for table data queries."""
    EQ = "eq"
    """Equal to"""
    
    NEQ = "neq"
    """Not equal to"""
    
    LT = "lt"
    """Less than"""
    
    LTE = "lte"
    """Less than or equal to"""
    
    GT = "gt"
    """Greater than"""
    
    GTE = "gte"
    """Greater than or equal to"""
    
    CONTAINS = "contains"
    """Contains substring"""
    
    NCONTAINS = "ncontains"
    """Does not contain substring"""
    
    EMPTY = "empty"
    """Is empty"""
    
    NEMPTY = "nempty"
    """Is not empty"""
    
    ERROR = "error"
    """Has error from a runnable column"""
    
    NERROR = "nerror"
    """Does not have error from a runnable column"""
    
    RESULT = "result"
    """Has result from a runnable column"""
    
    NRESULT = "nresult"
    """Does not have result from a runnable column"""
    
    RUN = "run"
    """Is running"""
    
    NRUN = "nrun"
    """Is not running"""
    
    AWAITING_INPUT = "awaiting_input"
    """Awaiting input"""
    
    QUEUED = "queued"
    """Is queued"""
    
    FAILED = "failed"
    """Has failed"""


class GroupOperator(str, Enum):
    """Operators for combining filter rules."""
    AND = "AND"
    """All conditions must be true"""
    
    OR = "OR"
    """At least one condition must be true"""


class Filter(BaseModel):
    """Filter model for table data queries."""
    column_id: int = Field(
        ..., 
        description="ID of the column to filter. The IDs are 1-indexed, not 0-indexed."
    )
    operator: Operator = Field(
        ..., 
        description="Filter operator such as 'eq', 'neq', 'lt', 'lte', etc."
    )
    value: Optional[str] = Field(
        None, 
        description="Value to compare against. Not required for some operators like 'empty'."
    )


class FilterGroup(BaseModel):
    """Group of filters that can be combined with AND/OR operators.
    
    Supports nested filter groups for complex query conditions.
    """
    operator: GroupOperator = Field(
        ..., 
        description="Logical operator ('AND' or 'OR') to combine the rules in this group."
    )
    rules: List[Union['Filter', 'FilterGroup']] = Field(
        ..., 
        description="List of filters or nested filter groups to apply. For nested groups, each can have its own operator."
    )
    
    class Config:
        use_enum_values = True


class Sort(BaseModel):
    """Sort model for table data queries."""
    column_id: int = Field(
        ..., 
        description="ID of the column to sort by. The IDs are 1-indexed, not 0-indexed."
    )
    direction: str = Field(
        ..., 
        description="Sort direction, either 'asc' (ascending) or 'desc' (descending)."
    )

    class Config:
        use_enum_values = True


@tool
def search(
    query: str,
    max_results: Optional[int] = 5,
) -> Optional[list[dict[str, Any]]]:
    """Search for general web results.

    This function performs a search using the Tavily search engine, which is designed
    to provide comprehensive, accurate, and trusted results. It's particularly useful
    for answering questions about current events.

    Args:
        query: The search query string
        max_results: Optional maximum number of results to return. 
        RECOMMENDED to start with 5 and then increase if needed.
    """
    stream_writer = get_stream_writer()
    stream_writer({"custom_tool_call": f"Searching the web for {query}"})    
    wrapped = TavilySearchResults(max_results=max_results)
    
    # Use invoke instead of ainvoke for synchronous operation
    result = wrapped.invoke({"query": query})
    return cast(list[dict[str, Any]], result)


@tool
def scrape_website(url: str) -> str:
    """Scrape a website and return its content in clean, LLM-ready markdown format.
    
    This tool uses FireCrawl to extract content from a single webpage. It's useful when you need
    detailed information from a specific page. The content is returned as clean markdown,
    making it ideal for analysis.
    
    Args:
        url: The URL of the website to scrape (e.g., "https://example.com")
        
    Returns:
        The content of the website in markdown format
    """
    if not FIRECRAWL_API_KEY:
        return "Error: FIRECRAWL_API_KEY environment variable is not set."

    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Browsing website: {url}"})
        app = FirecrawlApp(api_key=FIRECRAWL_API_KEY)
        
        # Use the exact syntax from FireCrawl docs
        scrape_result = app.scrape_url(url, formats=['markdown'])
        
        # Check if we have the expected structure
        if not scrape_result:
            return "No content was found on the provided URL."
        
        # Try to access the markdown content
        try:
            # The response should be a dict with 'data' containing 'markdown'
            if isinstance(scrape_result, dict):
                markdown_content = scrape_result.get('data', {}).get('markdown', '')
            else:
                # If it's an object, try attribute access
                markdown_content = getattr(scrape_result, 'markdown', '') or getattr(scrape_result.data, 'markdown', '') if hasattr(scrape_result, 'data') else ''
            
            if not markdown_content:
                return f"No markdown content found. Response: {scrape_result}"
            
            return f"Content from {url}:\n\n{markdown_content}"
            
        except Exception as parse_error:
            return f"Error parsing response: {parse_error}. Raw response: {scrape_result}"
    
    except Exception as e:
        return f"Error scraping website: {str(e)}"

@tool
def read_table_data(
    config: Annotated[RunnableConfig, InjectedToolArg],
    max_rows: int,
    filters: Optional[FilterGroup] = None,
    search: Optional[str] = None, 
    sorts: Optional[List[Sort]] = None,
    column_ids: Optional[List[int]] = None,
    summarize: bool = True,
) -> Tuple[Optional[Any], Optional[str]]:
    """Fetch user table data from a specified table.
    
    Args:
        table_id: The ID of the table to get data from
        max_rows: Maximum number of rows to fetch
        filters: Filtering conditions using the FilterGroup model. Always use the same filters as the user view filters.
        search: Search text to filter results
        sorts: Sort criteria using the Sort model
        column_ids: Specific column IDs to filter returned columns
        summarize: Whether to summarize the table data using LLM for token efficiency (default: True)
        
    Returns:
        Tuple containing (table_data, error) where table_data is the structured 
        data if successful, None if failed, and error is the error message 
        if failed, None if successful. When summarize=True, table_data will be
        an LLM-generated summary instead of raw data.
        
    """
    try:
        stream_writer = get_stream_writer()

        if summarize == False:
            stream_writer({"custom_tool_call": f"Reading table"})
        
        # Get the table_id from the configuration
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        
        # Convert pydantic models to dictionaries for the function call
        # Handle filters - could be FilterGroup object or already a dictionary
        if filters is not None:
            if hasattr(filters, 'model_dump'):
                filters_dict = filters.model_dump(mode='json')  # Use mode='json' to convert enums to values
            else:
                filters_dict = filters  # Already a dictionary
        else:
            filters_dict = None
            
        # Handle sorts - could be list of Sort objects or already a list of dictionaries  
        if sorts is not None:
            if all(hasattr(sort, 'model_dump') for sort in sorts):
                sorts_dict = [sort.model_dump(mode='json') for sort in sorts]  # Use mode='json' to convert enums to values
            else:
                sorts_dict = sorts  # Already a list of dictionaries
        else:
            sorts_dict = None
        
        table_data, error = get_table_data(
            table_id=table_id, 
            max_rows=max_rows,
            filters=filters_dict,
            search=search,
            sorts=sorts_dict,
            column_ids=column_ids
        )
        
        if error:
            print(f"Error from get_table_data: {error}")
            return None, error
            
        if not table_data:
            print("No table data available")
            return None, "No table data available"
        
        print(f"Got table data, summarize={summarize}")
        print(f"Table data type: {type(table_data)}")
        
        # If summarize is enabled, use LLM to create a token-efficient summary
        if summarize:
            stream_writer({"custom_tool_call": f"Indexing the table"})
            print("Starting summarization...")
            try:
                model = load_chat_model_non_thinking("openai/gpt-4o-mini").with_structured_output(TableSummaryOutput, method="function_calling")
                
                # Convert table data to JSON string for the prompt
                table_data_str = json.dumps(table_data, indent=2, default=str)
                print(f"Table data JSON length: {len(table_data_str)}")
                
                # Create two separate messages - one for the prompt, one for the data
                prompt_message = SystemMessage(TABLE_SUMMARY_PROMPT)
                data_message = HumanMessage(table_data_str)
                
                # Get the structured summary from the model using both messages
                print("Calling model for summarization...")
                summary_response = model.invoke([prompt_message, data_message], config)
                
                print("Model response received, returning structured summary")
                # Return the structured TableSummaryOutput object
                return summary_response, None
                    
            except Exception as summary_error:
                print(f"Summarization failed: {summary_error}")
                # If summarization fails, return raw data with a warning
                return table_data, f"Warning: Summarization failed ({str(summary_error)}), returning raw data"
        
        print("Summarize is False, returning raw data")
        # Return raw data when summarize is False
        return table_data, None
            
    except Exception as e:
        print(f"Function error: {e}")
        return None, str(e)
    
    

@tool
def upsert_linkedin_person_profile_column_from_url(
    config: Annotated[RunnableConfig, InjectedToolArg],
    column_name: str,
    linkedin_profile_url: str,
    column_id: Optional[int] = None,
) :
    """Create a new LinkedIn profile column or update/edit an existing one using profile URL.

    IMPORTANT: ALWAYS provide the column_id if you want to update/edit an existing column. Do NOT duplicate the column by NOT providing the column_id.
    
    Parameters:
        config: Configuration injected by the system
        column_name: A short descriptive name of the column. Must be unique per table.
        linkedin_profile_url: A SINGLE injection path of the LinkedIn profile URL data point.
        column_id: Optional ID of the existing column to update/edit. If not provided, a new column will be created.
        
    Returns:
        Tuple[Optional[str], Optional[str]]: Tuple containing (success_message, error_message)
        where success_message is a confirmation if successful, None if failed
        and error_message is the error description if failed, None if successful
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Working on {column_name} column"})

        
        # Set up metadata
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        service_id = 7

                # Step 1: Validate injection sequence
        validation_request = ValidateInjectionSequenceRequest(
            injection_sequence=linkedin_profile_url,
            target_data_type=TargetDataType.LINKEDIN_PROFILE_URL
        )

        validation_result = validate_injection_sequence(config=config, request=validation_request)
        
        # If validation failed, return the validation response directly
        if not validation_result.success:
            return validation_result
        
        inputs = [
            {"linkedin_profile_url": linkedin_profile_url}
        ]
        
        # Set up providers as specified in the payload
        providers = [
            {"providers": ["outbond"]}
        ]
        
        # Set up parameters (empty dict as in the payload)
        parameters = [{}]
        
        # Call the create_smart_column function
        response, error = upsert_smart_column(
            table_id=table_id,
            column_name=column_name,
            service_id=service_id,
            inputs=inputs,
            parameters=parameters,
            providers=providers,
            column_id=column_id
        )
        
        if error:
            return None, error
            
        # Extract column details from response
        column = response.get('column', {})
        column_id = column.get('id')
        column_type = column.get('type') 
        name = column.get('name')
        
        action = "updated" if column_id is not None else "created"
        
        # Create JSON response
        response_json = {
            "action": action,
            "column_name": name,
            "message": f"The LinkedIn profile column was successfully {action}. Column name: '{name}' Column ID: {column_id}, Column Type: {column_type}"
        }
        
        return json.dumps(response_json), None
        
    except Exception as e:
        return None, str(e)


@tool
def upsert_linkedin_company_profile_column_from_url(
    config: Annotated[RunnableConfig, InjectedToolArg],
    column_name: str,
    linkedin_company_url: str,
    column_id: Optional[int] = None,
) :
    """Create a new LinkedIn company column or update an existing one using company URL.

    IMPORTANT: ALWAYS provide the column_id if you want to update/edit an existing column. Do NOT duplicate the column by NOT providing the column_id.
    Parameters:
        config: Configuration injected by the system
        column_name: A short descriptive name of the column. Must be unique per table.
        linkedin_company_url: A SINGLE injection path of the LinkedIn company URL data point.
        column_id: Optional ID of the existing column to update/edit. If not provided, a new column will be created.
        
    Returns:
        Tuple[Optional[str], Optional[str]]: Tuple containing (success_message, error_message)
        where success_message is a confirmation if successful, None if failed
        and error_message is the error description if failed, None if successful
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Working on {column_name} column"})
        # Set up metadata
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        service_id = 8

                        # Step 1: Validate injection sequence
        validation_request = ValidateInjectionSequenceRequest(
            injection_sequence=linkedin_company_url,
            target_data_type=TargetDataType.LINKEDIN_COMPANY_URL
        )

        validation_result = validate_injection_sequence(config=config, request=validation_request)
        
        # If validation failed, return the validation response directly
        if not validation_result.success:
            return validation_result
        
        
        inputs = [
            {"linkedin_company_url": linkedin_company_url}
        ]
        
        # Set up providers as specified in the payload
        providers = [
            {"providers": ["outbond"]}
        ]
        
        # Set up parameters (empty dict as in the payload)
        parameters = [{}]
        
        # Call the create_smart_column function
        response, error = upsert_smart_column(
            table_id=table_id,
            column_name=column_name,
            service_id=service_id,
            inputs=inputs,
            parameters=parameters,
            providers=providers,
            column_id=column_id
        )
        
        if error:
            return None, error
            
        # Extract column details from response
        column = response.get('column', {})
        column_id = column.get('id')
        column_type = column.get('type') 
        name = column.get('name')
        
        action = "updated" if column_id is not None else "created"
        
        # Create JSON response
        response_json = {
            "action": action,
            "column_name": name,
            "message": f"The LinkedIn company column was successfully {action}. Column name: '{name}' Column ID: {column_id}, Column Type: {column_type}"
        }
        
        return json.dumps(response_json), None
        
    except Exception as e:
        return None, str(e)


@tool
def upsert_phone_number_column(
    config: Annotated[RunnableConfig, InjectedToolArg],
    column_name: str,
    linkedin_profile_url: str,
    column_id: Optional[int] = None,
) :
    """Create a new phone number column or update an existing one using LinkedIn profile URL.
    
    IMPORTANT: ALWAYS provide the column_id if you want to update/edit an existing column. Do NOT duplicate the column by NOT providing the column_id.
    Parameters:
        config: Configuration injected by the system
        column_name: A short descriptive name of the column. Must be unique per table.
        linkedin_profile_url: A SINGLE injection path of the LinkedIn profile URL data point.
        column_id: Optional ID of the existing column to update/edit. If not provided, a new column will be created.
        
    Returns:
        Tuple[Optional[str], Optional[str]]: Tuple containing (success_message, error_message)
        where success_message is a confirmation if successful, None if failed
        and error_message is the error description if failed, None if successful
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Working on {column_name} column"})
        # Set up metadata
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        service_id = 11

                        # Step 1: Validate injection sequence
        validation_request = ValidateInjectionSequenceRequest(
            injection_sequence=linkedin_profile_url,
            target_data_type=TargetDataType.LINKEDIN_PROFILE_URL
        )

        validation_result = validate_injection_sequence(config=config, request=validation_request)
        
        # If validation failed, return the validation response directly
        if not validation_result.success:
            return validation_result
        
        
        inputs = [
            {"linkedin_profile_url": linkedin_profile_url}
        ]
        
        # Set up providers as specified in the payload
        providers = [
            {"providers": ["leadmagic", "prospeo"]}
        ]
        
        # Set up parameters (empty dict as in the payload)
        parameters = [{}]
        
        # Call the create_smart_column function
        response, error = upsert_smart_column(
            table_id=table_id,
            column_name=column_name,
            service_id=service_id,
            inputs=inputs,
            parameters=parameters,
            providers=providers,
            column_id=column_id
        )
        
        if error:
            return None, error
            
        # Extract column details from response
        column = response.get('column', {})
        column_id = column.get('id')
        column_type = column.get('type') 
        name = column.get('name')
        
        action = "updated" if column_id is not None else "created"
        
        # Create JSON response
        response_json = {
            "action": action,
            "column_name": name,
            "message": f"The phone column was successfully {action}. Column name: '{name}' Column ID: {column_id}, Column Type: {column_type}"
        }
        
        return json.dumps(response_json), None
        
    except Exception as e:
        return None, str(e)


@tool
def upsert_work_email_column(
    config: Annotated[RunnableConfig, InjectedToolArg],
    column_name: str,
    full_name: str,
    company_domain: str,
    column_id: Optional[int] = None,
) -> Tuple[Optional[str], Optional[str]]:
    """Create a new work email column or update an existing one using name and company domain.
    
    IMPORTANT: ALWAYS provide the column_id if you want to update/edit an existing column. Do NOT duplicate the column by NOT providing the column_id.
    Parameters:
        config: Configuration injected by the system
        column_name: A short descriptive name of the column. Must be unique per table.
        full_name: A SINGLE injection path of the full name data point.
        company_domain: A SINGLE injection path of the company domain data point.
        column_id: Optional ID of the existing column to update/edit. If not provided, a new column will be created.
        
    Returns:
        Tuple[Optional[str], Optional[str]]: Tuple containing (success_message, error_message)
        where success_message is a confirmation if successful, None if failed
        and error_message is the error description if failed, None if successful
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Working on {column_name} column"})
        # Set up metadata
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        service_id = 10
        
        inputs = [
            {"full_name": full_name},
            {"company_domain": company_domain}
        ]
        
        # Set up providers
        providers = [
            {"email_providers": ["leadmagic", "findymail", "prospeo"]},
            {"verify_providers": ["millionverifier"]}
        ]
        
        # Set up parameters (empty dict as in the payload)
        parameters = [{}]
        
        # Call the create_smart_column function
        response, error = upsert_smart_column(
            table_id=table_id,
            column_name=column_name,
            service_id=service_id,
            inputs=inputs,
            parameters=parameters,
            providers=providers,
            column_id=column_id
        )
        
        if error:
            return None, error
            
        # Extract column details from response
        column = response.get('column', {})
        column_id = column.get('id')
        column_type = column.get('type') 
        name = column.get('name')
        
        action = "updated" if column_id is not None else "created"
        
        # Create JSON response
        response_json = {
            "action": action,
            "column_name": name,
            "message": f"The work email column was successfully {action}. Column name: '{name}' Column ID: {column_id}, Column Type: {column_type}"
        }
        
        return json.dumps(response_json), None
        
    except Exception as e:
        return None, str(e)


@tool
def run_column(
    config: RunnableConfig,
    column_id: str,
    column_name: str,
    count: int = 1,
    row_id: int = 1,
    wait_results: bool = False
) -> Tuple[Optional[str], Optional[str]]:
    """Run a specific column in a table to process data and wait for completion.
    
    This tool triggers the execution of a smart column for the specified table.
    It will process the number of rows specified by count and optionally wait for completion.
    
    Parameters:
        config: Configuration injected by the system
        column_id: The ID of the column to run
        column_name: The name of the column to run
        count: Optional number of rows to process. If not provided, 1 row will be processed.
        row_id: The row ID to monitor for completion status (defaults to 1, only used when count=1)
        wait_results: If True, wait for all requested cells to complete regardless of count (defaults to False)
        
    Returns:
        Tuple[Optional[str], Optional[str]]: When wait_results=False, returns tuple containing 
        (success_message_with_data, error_message). When wait_results=True, returns tuple containing 
        (execution_summary, error_message).
    """
    try:
        # All logic with side effects comes AFTER the interrupt
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Running {column_name} column"})
        
        # Get configuration
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        
        # First, check if the column is runnable
        print(f"🔍 Checking if column {column_id} is runnable...")
        table_data, read_error = read_table_data.invoke({
            "config": config,
            "max_rows": 1,
            "summarize": False,
            "column_ids": [int(column_id)]
        })
        
        if read_error:
            return None, f"Error checking column runnability: {read_error}"
        
        if not table_data:
            return None, f"Could not retrieve column information for column {column_id}"
        
        
        # Check if column is runnable - handle raw table data structure
        # The table_data is the direct response from get_table_data which should contain 'columns' key
        columns = table_data.get('columns', [])
        
        target_column = None
        for col in columns:
            if col.get('column_id') == int(column_id):
                target_column = col
                break
        
        if not target_column:
            return None, f"Column {column_id} not found in table"
        
        if not target_column.get('is_runnable', False):
            return None, f"Column '{target_column.get('column_name', column_id)}' (ID: {column_id}) is not runnable. Only smart columns like AI, Research, Email finder, Phone finder, etc. can be run."
        
        # Call the db_run_column function from agent_db.py
        success, error = db_run_column(
            table_id=table_id,
            column_id=column_id,
            count=count
        )
        
        if error:
            return None, error
        
        # Wait for completion only if wait_results is True
        if wait_results:
            stream_writer = get_stream_writer()
            stream_writer({"custom_tool_call": f"Waiting for column enrichment to complete"})
            # Start polling the cell(s) until completion
            max_attempts = 50  # Maximum 50 seconds
            attempt = 0
            
            # Determine which rows to poll
            if count == 1:
                rows_to_poll = [row_id]
            else:
                # When count > 1, poll rows 1 through count
                rows_to_poll = list(range(1, count + 1))
            
            while attempt < max_attempts:
                completed_cells = []
                failed_cells = []
                awaiting_input_cells = []
                still_processing = []
                
                # Check status of all rows
                for current_row_id in rows_to_poll:
                    cell_data, poll_error = poll_cell_until_complete(table_id, current_row_id, int(column_id))
                    
                    if poll_error:
                        print(f"❌ Error polling row {current_row_id}: {poll_error}")
                        return None, f"Error polling cell status for row {current_row_id}: {poll_error}"
                    
                    if cell_data:
                        run_status = cell_data.get('run')
                        print(f"📊 Row {current_row_id}: status = '{run_status}', cell_data = {cell_data}")
                        
                        if run_status == 'completed':
                            completed_cells.append((current_row_id, cell_data))
                            print(f"✅ Row {current_row_id}: COMPLETED")
                        elif run_status == 'failed':
                            failed_cells.append((current_row_id, cell_data))
                            print(f"❌ Row {current_row_id}: FAILED")
                        elif run_status == 'awaiting_input':
                            awaiting_input_cells.append((current_row_id, cell_data))
                            print(f"⏳ Row {current_row_id}: AWAITING_INPUT")
                        else:
                            still_processing.append(current_row_id)
                            print(f"🔄 Row {current_row_id}: STILL_PROCESSING (status: '{run_status}')")
                    else:
                        still_processing.append(current_row_id)
                        print(f"❓ Row {current_row_id}: NO_DATA - added to still_processing")
                
                
                # Check if all cells have reached an end status
                if not still_processing:
                    # All cells have completed processing
                    total_cells = len(rows_to_poll)
                    completed_count = len(completed_cells)
                    failed_count = len(failed_cells)
                    awaiting_input_count = len(awaiting_input_cells)
                    
                    # Prepare execution summary
                    if count == 1:
                        # For single cell, prepare detailed result
                        if completed_cells:
                            execution_summary = f"Successfully ran column {column_id} in table {table_id} for 1 row. Final data: {completed_cells[0][1]}"
                        elif failed_cells:
                            # Extract status message from failed cell
                            failed_cell_data = failed_cells[0][1]
                            status_message = "No status message available"
                            if isinstance(failed_cell_data, dict):
                                # Look for message in different possible locations
                                if 'run_status' in failed_cell_data and isinstance(failed_cell_data['run_status'], dict):
                                    status_message = failed_cell_data['run_status'].get('message', status_message)
                                elif 'message' in failed_cell_data:
                                    status_message = failed_cell_data['message']
                            return None, f"Column execution failed. Status message: {status_message}. Full cell data: {failed_cell_data}"
                        elif awaiting_input_cells:
                            # Extract status message from awaiting input cell
                            awaiting_cell_data = awaiting_input_cells[0][1]
                            status_message = "No status message available"
                            if isinstance(awaiting_cell_data, dict):
                                # Look for message in different possible locations
                                if 'run_status' in awaiting_cell_data and isinstance(awaiting_cell_data['run_status'], dict):
                                    status_message = awaiting_cell_data['run_status'].get('message', status_message)
                                elif 'message' in awaiting_cell_data:
                                    status_message = awaiting_cell_data['message']
                            return None, f"Column execution is awaiting input. Status message: {status_message}. Full cell data: {awaiting_cell_data}"
                    else:
                        # For multiple cells, prepare summary
                        execution_summary = f"Successfully ran column {column_id} in table {table_id} for {count} rows. "
                        execution_summary += f"Results: {completed_count} completed, {failed_count} failed, {awaiting_input_count} awaiting input."
                        
                        if failed_count > 0 or awaiting_input_count > 0:
                            # Include details about non-completed cells
                            details = []
                            if failed_count > 0:
                                failed_details = []
                                for row_id, cell_data in failed_cells:
                                    status_message = "No message"
                                    if isinstance(cell_data, dict):
                                        if 'run_status' in cell_data and isinstance(cell_data['run_status'], dict):
                                            status_message = cell_data['run_status'].get('message', status_message)
                                        elif 'message' in cell_data:
                                            status_message = cell_data['message']
                                    failed_details.append(f"row {row_id} ({status_message})")
                                details.append(f"Failed: {', '.join(failed_details)}")
                            if awaiting_input_count > 0:
                                awaiting_details = []
                                for row_id, cell_data in awaiting_input_cells:
                                    status_message = "No message"
                                    if isinstance(cell_data, dict):
                                        if 'run_status' in cell_data and isinstance(cell_data['run_status'], dict):
                                            status_message = cell_data['run_status'].get('message', status_message)
                                        elif 'message' in cell_data:
                                            status_message = cell_data['message']
                                    awaiting_details.append(f"row {row_id} ({status_message})")
                                details.append(f"Awaiting input: {', '.join(awaiting_details)}")
                            execution_summary += f" Details: {'; '.join(details)}"
                    
                    # Create JSON response
                    response_json = {
                        "count": count,
                        "column_name": column_name,
                        "message": execution_summary
                    }
                    
                    # Return tuple containing execution summary and error message
                    return json.dumps(response_json), None
                
                # Wait 1 second before next poll
                time.sleep(1)
                attempt += 1
            
            # If we reach here, we've exceeded max attempts
            return None, f"Timeout: Column execution did not complete within {max_attempts} seconds"
        else:
            # Return success without polling when wait_results is False
            count_msg = f"for {count} rows" if count > 1 else "for 1 row"
            
            # Create JSON response
            response_json = {
                "count": count,
                "column_name": column_name,
                "message": f"Successfully triggered column {column_id} execution in table {table_id} {count_msg}. The data will be populated in the table automatically."
            }
            
            return json.dumps(response_json), None
        
    except Exception as e:
        return None, str(e)


@tool
def upsert_text_column(
    config: Annotated[RunnableConfig, InjectedToolArg],
    column_name: str,
    text_formula: str,
    column_id: Optional[int] = None,
) -> Tuple[Optional[str], Optional[str]]:
    """Create a new text column or update an existing one with text or combination of other column values.
    
    IMPORTANT: ALWAYS provide the column_id if you want to update/edit an existing column. Do NOT duplicate the column by NOT providing the column_id.
    
    This tool can be used in two main ways:
    1. Create OR update/edit a column with the same static text for all rows (useful for ICP descriptions for example)
    2. Create OR update/edit a formula column that combines values from other columns cells or cell details (e.g., concatenate first name and last name)
    
    Parameters:
        config: Configuration injected by the system
        column_name: A short descriptive name of the column. Must be unique per table.
        text_formula: The text content or formula to use. Can ONLY include column references as {{injection path}}. NO other formulas are allowed.
                      Example: "{{linkedin profile.cell_details.first_name}} {{linkedin profile.cell_details.last_name}}" would combine the first name and last name columns with a space between.
        column_id: Optional ID of the existing column to update/edit. If not provided, a new column will be created.
        
    Returns:
        Tuple[Optional[str], Optional[str]]: Tuple containing (success_message, error_message)
        where success_message is a confirmation if successful, None if failed
        and error_message is the error description if failed, None if successful
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Working on {column_name} column"})
        # Set up metadata
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        service_id = 15  # Formula/Text service ID
        
        # Convert any injection sequences ({{N}}) to proper HTML span tags
        def replace_injection(match):
            injection_id = match.group(1)
            return f'<span data-type="column-embed" data-id="{{{{%s}}}}"></span>' % injection_id
            
        # Replace all {{N}} patterns with the HTML tag format
        processed_formula = re.sub(r'\{\{([^}]+)\}\}', replace_injection, text_formula)
        
        
        # Wrap in paragraph tags for the final user_prompt
        user_prompt = f"<p>{processed_formula}</p>"
        
        # Set up empty inputs and providers as specified in the payload
        inputs = []
        providers = []
        
        # Set up parameters with the user prompt
        parameters = [{"user_prompt": user_prompt}]
        
        # Call the upsert_smart_column function
        response, error = upsert_smart_column(
            table_id=table_id,
            column_name=column_name,
            service_id=service_id,
            inputs=inputs,
            parameters=parameters,
            providers=providers,
            column_id=column_id
        )
        
        if error:
            return None, error
            
        # Extract column details from response
        column = response.get('column', {})
        column_id = column.get('id')
        column_type = column.get('type') 
        name = column.get('name')
        
        action = "updated" if column_id is not None else "created"
        
        # Create JSON response
        response_json = {
            "action": action,
            "column_name": name,
            "message": f"The text column was successfully {action}. Column name: '{name}' Column ID: {column_id}, Column Type: {column_type}"
        }
        
        return json.dumps(response_json), None
        
    except Exception as e:
        return None, str(e)


@tool
def read_user_view_table_filters(
    config: Annotated[RunnableConfig, InjectedToolArg]
) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
    """Read the filters field from the current table.
    
    This tool retrieves only the filters field from the current table,
    which contains filter configurations that determine how data is filtered
    in table views.
    
    Parameters:
        config: Configuration injected by the system which contains table_id
        
    Returns:
        Tuple[Optional[Dict[str, Any]], Optional[str]]: Tuple containing (filters_data, error)
        where filters_data is the filters field if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Reading table filters"})
        # Get the table_id from the configuration
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        
        # Call the database function
        filters, error = get_table_filters(table_id)
        
        if error:
            return None, error
            
        # Return the filters data
        return filters, None
            
    except Exception as e:
        error_msg = f"Error reading table filters: {str(e)}"
        return None, error_msg


@tool
def update_user_view_table_filters_tool(
    config: Annotated[RunnableConfig, InjectedToolArg],
    filters: Optional[FilterGroup] = None
) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
    """Update the filters field for the current table.
    
    This tool updates only the filters field in the current table,
    which controls how data is filtered in table views.
    
    Parameters:
        config: Configuration injected by the system which contains table_id
        filters: Filtering conditions using the FilterGroup model, same structure
                as used in read_table_data tool
        
    Returns:
        Tuple[Optional[Dict[str, Any]], Optional[str]]: Tuple containing (updated_data, error)
        where updated_data is the updated table data if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Updating table filters"})
        # Get the table_id from the configuration
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        
        # Convert filters to dict without complex formatting
        if filters:
            filters_dict = filters.model_dump(mode='json')
        else:
            filters_dict = {}
        
        # Call the database function with the filters as they are
        updated_data, error = update_table_filters(table_id, filters_dict)
        
        if error:
            return None, error
            
        # Return the updated data
        return updated_data, None
            
    except Exception as e:
        error_msg = f"Error updating table filters: {str(e)}"
        return None, error_msg


@tool
def upsert_ai_text_column(
    config: Annotated[RunnableConfig, InjectedToolArg],
    column_name: str,
    prompt: str,
    required_fields: List[str] = [],
    run_condition: Optional[str] = None,
    column_id: Optional[int] = None,
) -> Tuple[Optional[str], Optional[str]]:
    """Create a new AI text column or update/edit an existing one with AI-generated content.
    
    IMPORTANT: ALWAYS provide the column_id if you want to update/edit an existing column. Do NOT duplicate the column by NOT providing the column_id.
    
    This tool creates or updates/edits an AI-powered text column that can generate content based on 
    other column values and a provided prompt.
    
    Parameters:
        config: Configuration injected by the system
        column_name: A short descriptive name of the column. Must be unique per table.
        prompt: The prompt template for AI generation. Can include column references as {{injection path}}.
                Example: "Write a personalized intro for first name: {{linkedin profile.cell_details.first_name}} who works at company: {{linkedin profile.cell_details.company_name}}."
        required_fields: List of column references that are required for the AI generation to run.
                         Example: ["linkedin profile.cell_details.first_name", "linkedin profile.cell_details.company_name"] would mark both values as required fields and will not run the column if they don't exist.
        run_condition: Optional condition that determines when the AI generation should run.
                      Can include column references as {{injection path}}.
                      Example: "The date is earlier than 2024. Date: {{linkedin profile.cell_details.date}}"
        column_id: Optional ID of the existing column to update/edit. If not provided, a new column will be created.
        
    Returns:
        Tuple[Optional[str], Optional[str]]: Tuple containing (success_message, error_message)
        where success_message is a confirmation if successful, None if failed
        and error_message is the error description if failed, None if successful
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Working on {column_name} column"})
        # Set up metadata
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        service_id = 14  # AI Text service ID
        
        stream_writer({"custom_tool_call": f"Writing prompt to AI"})
        # Enhance the prompt using the model
        model = load_chat_model_non_thinking(configuration.model)
        # Create the messages
        messages = [
            SystemMessage(AI_FORMULA_PROMPT_GENERATOR),
            HumanMessage(prompt)
        ]
        # Get the model response and extract the content
        enhanced_prompt_message = model.invoke(messages, config)
        # Extract the string content from the AIMessage
        enhanced_prompt = enhanced_prompt_message.content
        
        # Ensure enhanced_prompt is a string
        if isinstance(enhanced_prompt, list):
            # If content is a list of structured content, extract only text parts
            text_parts = []
            for item in enhanced_prompt:
                if isinstance(item, dict) and item.get('type') == 'text':
                    text_parts.append(item.get('text', ''))
                elif isinstance(item, str):
                    text_parts.append(item)
                elif hasattr(item, 'text'):
                    text_parts.append(str(item.text))
                else:
                    # Fallback for other types, but skip thinking parts
                    if not (isinstance(item, dict) and item.get('type') == 'thinking'):
                        text_parts.append(str(item))
            enhanced_prompt = ''.join(text_parts)
        elif not isinstance(enhanced_prompt, str):
            # Convert to string if it's some other type
            enhanced_prompt = str(enhanced_prompt)
        
        # Helper function to process text with column injections
        def process_with_injections(text, required_fields_list):
            def replace_injection(match):
                injection_id = match.group(1)
                is_required = "true" if injection_id in required_fields_list else "false"
                return f'<span data-type="column-embed" data-id="{{{{%s}}}}" data-is-required="{is_required}"></span>' % injection_id
                
            # Replace all {{N}} patterns with the HTML tag format
            processed_text = re.sub(r'\{\{([^}]+)\}\}', replace_injection, text)
            return f"<p>{processed_text}</p>"
        
        # Process enhanced prompt with injections
        user_prompt = process_with_injections(enhanced_prompt, required_fields)
        
        # Process run condition if provided
        formula = None
        if run_condition:
            formula = process_with_injections(run_condition, [])
        
        # Set up empty inputs and providers as specified in the payload
        inputs = []
        providers = []
        
        # Set up parameters with the user prompt and optional formula
        parameters = [{"user_prompt": user_prompt}]
        
        # Add formula to parameters if run_condition was provided
        if formula:
            parameters.append({"formula": formula})
        else:
            parameters.append({})
        
        # Call the upsert_smart_column function
        response, error = upsert_smart_column(
            table_id=table_id,
            column_name=column_name,
            service_id=service_id,
            inputs=inputs,
            parameters=parameters,
            providers=providers,
            column_id=column_id
        )
        
        if error:
            return None, error
            
        # Extract column details from response
        column = response.get('column', {})
        column_id = column.get('id')
        column_type = column.get('type') 
        name = column.get('name')
        
        action = "updated" if column_id is not None else "created"
        
        # Create JSON response
        response_json = {
            "action": action,
            "column_name": name,
            "message": f"The AI text column was successfully {action}. Column name: '{name}' Column ID: {column_id}, Column Type: {column_type}"
        }
        
        return json.dumps(response_json), None
        
    except Exception as e:
        return None, str(e)


@tool
def upsert_bond_ai_researcher_column(
    config: Annotated[RunnableConfig, InjectedToolArg],
    column_name: str,
    prompt: str,
    required_fields: List[str] = [],
    run_condition: Optional[str] = None,
    column_id: Optional[int] = None,
) -> Tuple[Optional[str], Optional[str]]:
    """Create a new Bond AI researcher column OR update/edit an existing one for online research.
    
    IMPORTANT: ALWAYS provide the column_id if you want to update/edit an existing column. Do NOT duplicate the column by NOT providing the column_id.
    
    This tool creates OR updates/edits an AI-powered research column that can search for information online
    based on values from other columns in your table.
    
    Parameters:
        column_name: A short descriptive name of the column. Must be unique per table.
        prompt: The research prompt template. Can include column references as {{injection path}}.
                Example: "Research the company website for information. Company website: {{linkedin company.cell_details.company_website}}"
        required_fields: List of column references that are required for the research to run.
                         Example: ["linkedin company.cell_details.company_website"] would mark the company website column as required and will not run the column if it doesn't exist.
        run_condition: Optional condition that determines when the AI research should run.
                      Can include column references as {{injection path}}.
                      Example: "The company website is a valid URL: {{linkedin company.cell_details.company_website}}". RCOMMENDED to add when possible to ensure the research is only run when the input data are valid.
        column_id: Optional ID of the existing column to update/edit. If not provided, a new column will be created.
        
    Returns:
        Tuple[Optional[str], Optional[str]]: Tuple containing (success_message, error_message)
        where success_message is a confirmation if successful, None if failed
        and error_message is the error description if failed, None if successful
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Working on {column_name} column"})
        # Set up metadata
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        service_id = 22  # Bond AI Researcher service ID
        
        stream_writer({"custom_tool_call": f"Writing prompt to AI"})
        # Enhance the prompt using the model
        model = load_chat_model_non_thinking(configuration.model)
        # Create the messages
        messages = [
            SystemMessage(BOND_AI_RESEARCHER_COLUMN_PROMPT),
            HumanMessage(prompt)
        ]
        # Get the model response and extract the content
        enhanced_prompt_message = model.invoke(messages, config)
        # Extract the string content from the AIMessage
        enhanced_prompt = enhanced_prompt_message.content
        
        # Ensure enhanced_prompt is a string
        if isinstance(enhanced_prompt, list):
            # If content is a list of structured content, extract only text parts
            text_parts = []
            for item in enhanced_prompt:
                if isinstance(item, dict) and item.get('type') == 'text':
                    text_parts.append(item.get('text', ''))
                elif isinstance(item, str):
                    text_parts.append(item)
                elif hasattr(item, 'text'):
                    text_parts.append(str(item.text))
                else:
                    # Fallback for other types, but skip thinking parts
                    if not (isinstance(item, dict) and item.get('type') == 'thinking'):
                        text_parts.append(str(item))
            enhanced_prompt = ''.join(text_parts)
        elif not isinstance(enhanced_prompt, str):
            # Convert to string if it's some other type
            enhanced_prompt = str(enhanced_prompt)
        
        # Helper function to process text with column injections
        def process_with_injections(text, required_fields_list):
            def replace_injection(match):
                injection_id = match.group(1)
                is_required = "true" if injection_id in required_fields_list else "false"
                return f'<span data-type="column-embed" data-id="{{{{%s}}}}" data-is-required="{is_required}"></span>' % injection_id
                
            # Replace all {{N}} patterns with the HTML tag format
            processed_text = re.sub(r'\{\{([^}]+)\}\}', replace_injection, text)
            return f"<p>{processed_text}</p>"
        
        # Process enhanced prompt with injections
        user_prompt = process_with_injections(enhanced_prompt, required_fields)
        
        # Process run condition if provided
        formula = None
        if run_condition:
            formula = process_with_injections(run_condition, [])
        
        # Set up empty inputs and providers as specified in the payload
        inputs = []
        providers = []
        
        # Set up parameters with the user prompt and optional formula
        parameters = [{"user_prompt": user_prompt}]
        
        # Add formula to parameters if run_condition was provided
        if formula:
            parameters.append({"formula": formula})
        else:
            parameters.append({})
        
        # Call the upsert_smart_column function
        response, error = upsert_smart_column(
            table_id=table_id,
            column_name=column_name,
            service_id=service_id,
            inputs=inputs,
            parameters=parameters,
            providers=providers,
            column_id=column_id
        )
        
        if error:
            return None, error
            
        # Extract column details from response
        column = response.get('column', {})
        column_id = column.get('id')
        column_type = column.get('type') 
        name = column.get('name')
        
        action = "updated" if column_id is not None else "created"
        
        # Create JSON response
        response_json = {
            "action": action,
            "column_name": name,
            "message": f"The Bond AI researcher column was successfully {action}. Column name: '{name}' Column ID: {column_id}, Column Type: {column_type}"
        }
        
        return json.dumps(response_json), None
        
    except Exception as e:
        return None, str(e)


# Define Pydantic model for structured output
class OUTPUT_PROMPTS(BaseModel):
    user_prompt: str = Field(description="The user prompt for the AI message copywriter")
    system_prompt: str = Field(description="The system prompt that guides the AI behavior")


# Define Pydantic models for table summarization structured output
class RunStatus(BaseModel):
    """Model for run status in cell schema."""
    run: Optional[str] = Field(None, description="The run status (e.g., 'completed', 'failed', 'pending') or null")
    message: Optional[str] = Field(None, description="Optional message about the run status")


class CellDetails(BaseModel):
    """Model for complex cell details with nested schema."""
    # This will contain the nested schema fields dynamically
    class Config:
        extra = "allow"  # Allow additional fields for the nested schema


class ColumnSummary(BaseModel):
    """Model for individual column summary in table data."""
    column_id: int = Field(description="The unique identifier for the column")
    column_name: str = Field(description="The display name of the column")
    is_runnable: bool = Field(description="Whether this column can be executed/run")
    cell_value: Optional[str] = Field(None, description="Schema type for primitive values (e.g., 'string', 'integer', 'boolean', 'null', 'array of <type>')")
    cell_details: Optional[CellDetails] = Field(None, description="Nested schema structure for complex objects/arrays")
    run_status: Optional[RunStatus] = Field(None, description="Run status information for runnable columns")
    data_summary: str = Field(description="Human-readable summary of the data patterns and content")
    
    class Config:
        # Ensure exactly one of cell_value or cell_details is present
        validate_assignment = True


class TableSummaryOutput(BaseModel):
    """Model for the complete table summary structured output."""
    columns: List[ColumnSummary] = Field(description="Array of column summaries")


@tool
def upsert_ai_message_copywriter(
    config: Annotated[RunnableConfig, InjectedToolArg],
    column_name: str,
    prompt: str,
    required_fields: List[str] = [],
    run_condition: Optional[str] = None,
    column_id: Optional[int] = None,
) -> Tuple[Optional[str], Optional[str]]:
    """Create a new AI message copywriter column OR update/edit an existing one.
    
    IMPORTANT: ALWAYS provide the column_id if you want to update/edit an existing column. Do NOT duplicate the column by NOT providing the column_id.
    
    This tool creates OR updates/edits an AI-powered copywriting column that can generate 
    personalized outreach messages based on information in other columns.
    
    Parameters:
        column_name: A short descriptive name of the column. Must be unique per table.
        prompt: The user prompt template. Can include column references as {{injection path}}.
               Example: "Write a LinkedIn first message using First name: {{linkedin profile.cell_details.first_name}}, Company: {{linkedin company.cell_details.company_name}}"
        required_fields: List of column references that are required for the message generation to run.
                         Example: ["linkedin profile.cell_details.first_name", "linkedin company.cell_details.company_name"] would mark both values as required.
        run_condition: Optional condition that determines when the message should be generated.
                      Example: "If the lead tier is tier 1. Lead tier: {{Qualification.cell_details.response}}". RECOMMENDED to add 
                      when possible to ensure messages are only generated for appropriate leads.
        column_id: Optional ID of the existing column to update/edit. If not provided, a new column will be created.
        
    Returns:
        Tuple[Optional[str], Optional[str]]: Tuple containing (success_message, error_message)
        where success_message is a confirmation if successful, None if failed
        and error_message is the error description if failed, None if successful
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Working on {column_name} column"})
        # Set up metadata
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        service_id = 13  # AI Message Copywriter service ID
        
        stream_writer({"custom_tool_call": f"Writing prompt to AI"})
        # Enhance the prompts using the model with structured output
        model = load_chat_model_non_thinking(configuration.model).with_structured_output(OUTPUT_PROMPTS)
        
        # Create the messages for enhancing the prompt
        messages = [
            SystemMessage(AI_FORMULA_PROMPT_GENERATOR),
            HumanMessage(prompt)
        ]
        
        # Get structured output directly in our Pydantic model format
        enhanced_prompts = model.invoke(messages, config)
        
        # Ensure enhanced_prompts.user_prompt is a string
        if isinstance(enhanced_prompts.user_prompt, list):
            # If content is a list of structured content, extract only text parts
            text_parts = []
            for item in enhanced_prompts.user_prompt:
                if isinstance(item, dict) and item.get('type') == 'text':
                    text_parts.append(item.get('text', ''))
                elif isinstance(item, str):
                    text_parts.append(item)
                elif hasattr(item, 'text'):
                    text_parts.append(str(item.text))
                else:
                    # Fallback for other types, but skip thinking parts
                    if not (isinstance(item, dict) and item.get('type') == 'thinking'):
                        text_parts.append(str(item))
            enhanced_prompts.user_prompt = ''.join(text_parts)
        elif not isinstance(enhanced_prompts.user_prompt, str):
            # Convert to string if it's some other type
            enhanced_prompts.user_prompt = str(enhanced_prompts.user_prompt)
        
        # Also ensure enhanced_prompts.system_prompt is a string if it exists
        if hasattr(enhanced_prompts, 'system_prompt'):
            if isinstance(enhanced_prompts.system_prompt, list):
                # If content is a list of structured content, extract only text parts
                text_parts = []
                for item in enhanced_prompts.system_prompt:
                    if isinstance(item, dict) and item.get('type') == 'text':
                        text_parts.append(item.get('text', ''))
                    elif isinstance(item, str):
                        text_parts.append(item)
                    elif hasattr(item, 'text'):
                        text_parts.append(str(item.text))
                    else:
                        # Fallback for other types, but skip thinking parts
                        if not (isinstance(item, dict) and item.get('type') == 'thinking'):
                            text_parts.append(str(item))
                enhanced_prompts.system_prompt = ''.join(text_parts)
            elif not isinstance(enhanced_prompts.system_prompt, str):
                # Convert to string if it's some other type
                enhanced_prompts.system_prompt = str(enhanced_prompts.system_prompt)
        
        # Helper function to process text with column injections
        def process_with_injections(text, required_fields_list):
            def replace_injection(match):
                injection_id = match.group(1)
                is_required = "true" if injection_id in required_fields_list else "false"
                return f'<span data-type="column-embed" data-id="{{{{%s}}}}" data-is-required="{is_required}"></span>' % injection_id
                
            # Replace all {{N}} patterns with the HTML tag format
            processed_text = re.sub(r'\{\{([^}]+)\}\}', replace_injection, text)
            return f"<p>{processed_text}</p>"
        
        # Process enhanced user prompt with injections
        processed_user_prompt = process_with_injections(enhanced_prompts.user_prompt, required_fields)
        
        # Process system prompt (no injections in system prompt)
        processed_system_prompt = f"<p>{enhanced_prompts.system_prompt}</p>"
        
        # Process run condition if provided
        formula = None
        if run_condition:
            formula = process_with_injections(run_condition, [])
        
        # Set up empty inputs and providers as specified in the payload
        inputs = []
        providers = []
        
        # Set up parameters with the user prompt, formula, and system prompt
        parameters = [{"user_prompt": processed_user_prompt}]
        
        # Add formula to parameters if run_condition was provided
        if formula:
            parameters.append({"formula": formula})
        else:
            parameters.append({})
            
        # Add system prompt
        parameters.append({"system_prompt": processed_system_prompt})
        
        # Call the upsert_smart_column function
        response, error = upsert_smart_column(
            table_id=table_id,
            column_name=column_name,
            service_id=service_id,
            inputs=inputs,
            parameters=parameters,
            providers=providers,
            column_id=column_id
        )
        
        if error:
            return None, error
            
        # Extract column details from response
        column = response.get('column', {})
        column_id = column.get('id')
        column_type = column.get('type') 
        name = column.get('name')
        
        action = "updated" if column_id is not None else "created"
        
        # Create JSON response
        response_json = {
            "action": action,
            "column_name": name,
            "message": f"The AI message copywriter column was successfully {action}. Column name: '{name}' Column ID: {column_id}, Column Type: {column_type}"
        }
        
        return json.dumps(response_json), None
        
    except Exception as e:
        return None, str(e)


@tool
def run_sql_query(
    sql_query: str
) -> Tuple[Optional[List[Dict[str, Any]]], Optional[str]]:
    """Execute a SQL query through a secured RPC endpoint.
    
    This tool allows the agent to execute SQL queries against the USER table to retrieve data
    or perform analysis.

    EXAMPLE: SELECT * FROM tables_views.table_id LIMIT 1; to get the first row of the table.
    
    Parameters:
        sql_query: The SQL query to execute. Must be a valid SQL query string.
        
    Returns:
        Tuple[Optional[List[Dict[str, Any]]], Optional[str]]: Tuple containing (results, error)
        where results is a list of rows returned by the query if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        # Call the db_run_sql_query function from agent_db.py
        results, error = db_run_sql_query(sql_query)
        
        if error:
            return None, error
            
        return results, None
        
    except Exception as e:
        error_msg = f"Error executing SQL query: {str(e)}"
        return None, error_msg


@tool
def search_linkedin_profiles(
    config: Annotated[RunnableConfig, InjectedToolArg],
    filters: List[Union[LinkedInFilterType, Dict[str, Any]]],
    page: int = 1,
) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
    """Search for LinkedIn profiles using advanced filters and create separate columns for profile data.
    
    This tool searches for people on LinkedIn based on various criteria such as 
    current company, job title, location, industry, seniority, and more.
    It creates separate columns for Full Name, Job Title, Company Name, and LinkedIn URL,
    then saves the profile data to these respective columns.
    USE THIS TOOL TO SEARCH FOR PROFILES TO BUILD USER'S PROSPECT LIST AND SAVE THEM TO THE TABLE.
    
    Parameters:
        config: Configuration injected by the system
        filters: List of filters to apply. Each filter should use the LinkedInFilterType models
                 or raw dictionaries that will be converted to the proper filter types.
                 All filters are combined with AND logic.
                 Example filters:
                 - CurrentCompanyFilter(type="in", value=["Google", "Microsoft"])
                 - CurrentTitleFilter(type="not in", value=["Software Engineer"])
                 - RegionFilter(type="in", value=["California, United States"])
        page: Page number for pagination (default: 1)
        
    Returns:
        Tuple[Optional[Dict[str, Any]], Optional[str]]: Tuple containing (results, error)
        where results contains the search summary if successful, None if failed
        and error is the error message if failed, None if successful
        
    Note: Some filter values may need to be obtained from the Filters Autocomplete API
    for fields like COMPANY_HEADQUARTERS, REGION, and INDUSTRY to ensure valid values.
    """
    try:
        
        # Get the table_id from the configuration
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        
        # Check if API key is available
        if not CRUSTDATA_API_KEY:
            return None, "CRUSTDATA_API_KEY environment variable is not set. Please configure it to use this tool."
        
        # Convert raw dictionaries to proper filter objects if needed
        processed_filters = []
        for filter_item in filters:
            if isinstance(filter_item, dict):
                # Convert dictionary to proper filter object
                try:
                    from .filter_models import create_filter
                    filter_type = filter_item.get('filter_type')
                    if not filter_type:
                        return None, f"Missing 'filter_type' in filter: {filter_item}"
                    
                    # Extract other parameters
                    filter_params = {k: v for k, v in filter_item.items() if k != 'filter_type'}
                    
                    # Create the proper filter object
                    filter_obj = create_filter(filter_type, **filter_params)
                    processed_filters.append(filter_obj)
                except Exception as e:
                    return None, f"Error converting filter {filter_item}: {str(e)}"
            else:
                # Already a proper filter object
                processed_filters.append(filter_item)
        
        # Create LinkedIn profile columns (Full Name, Job Title, Company Name, LinkedIn URL)
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": "Creating the necessary columns and rows"})
        
        column_ids, column_error = create_linkedin_profile_columns(table_id)
        if column_error:
            return None, column_error
        
        print(f"Created LinkedIn profile columns: {column_ids}")
        
        # Check if table has at least 25 rows
        row_count, row_count_error = get_table_row_count(table_id)
        if row_count_error:
            return None, f"Error checking table row count: {row_count_error}"
        
        # If table has fewer than 25 rows, create the needed rows
        if row_count < 25:
            rows_to_create = 25 - row_count
            print(f"Table has {row_count} rows, creating {rows_to_create} additional rows to reach 25")
            
            for i in range(rows_to_create):
                # Index should be current row_count + i + 1 (1-based indexing)
                index = row_count + i + 1
                create_row_result, create_row_error = create_table_row(table_id, index)
                if create_row_error:
                    return None, f"Error creating row {index}: {create_row_error}"
            
            print(f"Successfully created {rows_to_create} rows")
        else:
            print(f"Table already has {row_count} rows (>= 25), no additional rows needed")
        
        stream_writer({"custom_tool_call": "Searching LinkedIn Profiles"})
        
        # Create the request model with processed filters and page
        search_request = PersonSearchRequest(filters=processed_filters, page=page)
        
        # Convert to dictionary for the API call
        payload = search_request.model_dump(mode='json')
        
        # Make the API request
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Authorization': f'Token {CRUSTDATA_API_KEY}'
        }

        response = requests.post(
            'https://api.crustdata.com/screener/person/search',
            headers=headers,
            json=payload,
        )
        
        # Check response status
        if response.status_code == 200:
            data = response.json()
            
            # Save profiles to separate columns if we have profiles in the response
            profiles_saved_count = 0
            if 'profiles' in data and data['profiles']:
                save_success, save_error = save_linkedin_profiles_to_separate_columns(table_id, data['profiles'], column_ids)
                if save_error:
                    print(f"Warning: Failed to save profiles to columns: {save_error}")
                else:
                    profiles_saved_count = len(data['profiles'])
                    print(f"Successfully saved {profiles_saved_count} profiles to separate columns")
            
            # Construct informative response for the agent (without returning actual profiles)
            total_display_count = data.get('total_display_count', 0)
            
            response_data = {
                "message": f"LinkedIn search completed successfully. Found {total_display_count} total profiles matching your criteria. Saved {profiles_saved_count} profiles to the table across 4 columns (Full Name, Job Title, Company Name, LinkedIn URL).",
                "total_display_count": total_display_count,
                "profiles_saved_to_table": profiles_saved_count,
                "columns_created": list(column_ids.keys()),
                "column_ids": column_ids
            }
            
            return response_data, None
            
        elif response.status_code == 401:
            return None, "Authentication failed. Please check your CRUSTDATA_API_KEY."
        elif response.status_code == 400:
            return None, response.json()
        elif response.status_code == 429:
            return None, "Rate limit exceeded. Please try again later."
        else:
            return None, f"API request failed with status {response.status_code}: {response.text}"
            
    except requests.exceptions.Timeout:
        return None, "Request timed out. Please try again."
    except requests.exceptions.ConnectionError:
        return None, "Connection error. Please check your internet connection."
    except Exception as e:
        return None, f"An error occurred while searching LinkedIn profiles: {str(e)}"


# Add all tools to the list
tools = [ search, scrape_website, upsert_linkedin_person_profile_column_from_url, 
         upsert_linkedin_company_profile_column_from_url, upsert_phone_number_column, 
         upsert_work_email_column, run_column, upsert_text_column, upsert_ai_text_column,
         upsert_bond_ai_researcher_column, upsert_ai_message_copywriter, 
         read_user_view_table_filters, update_user_view_table_filters_tool, read_table_data,
         search_linkedin_profiles]

# Create a lookup dictionary for tools by name
tools_by_name = {tool.name: tool for tool in tools}

