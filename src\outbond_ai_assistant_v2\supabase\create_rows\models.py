from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, ConfigDict


class CreateRowsRequest(BaseModel):
    """Request model for creating a new row.
    
    This model defines the structure for creating a new row with basic
    properties like column name, type, and optional index.
    """
    model_config = ConfigDict(use_enum_values=True)
    
    table_id: str = Field(
        ...,
        description="The unique identifier of the table to add the row to",
        min_length=1,
        examples=["tbl_c85117eb4106d464"]
    )

    
    data: Optional[List[Dict[str, Any]]] = Field(
        ...,
        description="The list of rows to add to the table, each row is a dictionary with column name as key and cell value as value",
        examples=[{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}]
    )


class CreateRowResponse(BaseModel):
    """Response model for rows creation operations."""
    model_config = ConfigDict(use_enum_values=True)

    id: Optional[int] = Field(
        default=None,
        description="The id of the created row when creation is successful"
    )

    table_id: Optional[str] = Field(
        default=None,
        description="The unique identifier of the table to add the row to",
        min_length=1,
        examples=["tbl_c85117eb4106d464"]
    )

    cells: Optional[Dict[str, Any]] = Field(
        default=None,
        description="The cells of the created row when creation is successful key is column name and value is cell value",
        examples=[{"name": "John Doe", "email": "<EMAIL>"}]
    )



class CreateRowsResponse(BaseModel):
    """Response model for rows creation operations."""
    model_config = ConfigDict(use_enum_values=True)
    
 


    rows: Optional[List[CreateRowResponse]] = Field(
        default=None,
        description="The list of rows that were created when creation is successful, each row is a dictionary with id, table_id and cells",
        examples=[{"id": 1, "table_id": "tbl_c85117eb4106d464", "cells": {"name": "John Doe", "email": "<EMAIL>"}}, {"id": 2, "table_id": "tbl_c85117eb4106d464", "cells": {"name": "Jane Doe", "email": "<EMAIL>"}}]
    )
    
    error_message: Optional[str] = Field(
        default=None,
        description="Error message when creation fails"
    )
    
    success: bool = Field(
        default=True,
        description="Indicates if the creation was successful"
    )


