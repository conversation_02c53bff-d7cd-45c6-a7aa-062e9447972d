# ./src/agent/webapp.py
from contextlib import asynccontextmanager
from fastapi import FastAPI, BackgroundTasks, Request
from typing import List, Optional, Any, Union
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from fastapi.exceptions import RequestValidationError
from redis import asyncio as aioredis
import asyncpg
import os
import logging
from pydantic import BaseModel
from enum import Enum

# Import our database functions
from src.backend_server.db.cells import (
    reimburse_tokens,
    update_cell,
)

# Import routers
from src.backend_server.routes.services import router as services_router

# # Import the handler functions
# from src.backend_server.table.get_table_row_ids import get_table_row_ids_handler
# from src.backend_server.table.get_table_data_by_row_ids import (
#     get_table_data_by_row_ids_handler,
# )
# from src.backend_server.table.get_table_rows import get_table_rows_handler

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("backend_api.server")


# Global connection objects will be stored in app.state
@asynccontextmanager
async def lifespan(app: FastAPI):
    # # Initialize connections on startup
    # app.state.redis = await aioredis.from_url(
    #     os.getenv("REDIS_URL"), decode_responses=True
    # )
    # app.state.pg_pool = await asyncpg.create_pool(os.getenv("POSTGRES_URL"))

    yield  # Server is running and handling requests

    # # Cleanup on shutdown
    # await app.state.redis.close()
    # await app.state.pg_pool.close()
    pass


app = FastAPI(lifespan=lifespan)
app.include_router(services_router)


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    user = request.state.user if hasattr(request.state, "user") else None
    body = exc.body or {}

    # Use pre-instantiated BackgroundTasks from app if possible, otherwise create
    background_tasks = getattr(request.app, "background_tasks", BackgroundTasks())

    # Log only if absolutely necessary - consider async logging if possible
    # logger.info(f"Request body: {body}")  # Comment out unless critical for debugging

    if user and {"column_id", "row_id", "table_id", "credits"} <= body.keys():
        supabase_client = user.get("supabase_client")

        # Add tasks directly without condition checks inside the task
        background_tasks.add_task(
            update_cell,
            supabase_client,
            body["column_id"],
            body["row_id"],
            body["table_id"],
            None,
            {"run": "failed", "error": str(exc)},
        )
        if body["credits"] > 0:
            background_tasks.add_task(
                reimburse_tokens,
                body["organization_id"],
                body["credits"],
                supabase_client,
            )

    # Precompute response to avoid redundant encoding
    return JSONResponse(
        status_code=422,  # Use literal instead of status.HTTP_422_UNPROCESSABLE_ENTITY
        content={"detail": exc.errors()},
        background=background_tasks,  # Attach tasks directly to response
    )


# Pydantic models for request validation
class RowIdsRequest(BaseModel):
    row_ids: List[int]


class FilterOperator(str, Enum):
    AND = "AND"
    OR = "OR"


class SortDirection(str, Enum):
    ASC = "asc"
    DESC = "desc"


class Operator(str, Enum):
    EQ = "eq"
    NEQ = "neq"
    LT = "lt"
    LTE = "lte"
    GT = "gt"
    GTE = "gte"
    CONTAINS = "contains"
    NCONTAINS = "ncontains"
    EMPTY = "empty"
    NEMPTY = "nempty"
    ERROR = "error"
    NERROR = "nerror"
    RESULT = "result"
    NRESULT = "nresult"
    RUN = "run"
    NRUN = "nrun"
    AWAITING_INPUT = "awaiting_input"
    QUEUED = "queued"
    FAILED = "failed"


class FilterRule(BaseModel):
    column_id: int
    operator: str
    value: Optional[Any] = None


class FilterCondition(BaseModel):
    operator: FilterOperator
    rules: List[Union[FilterRule, "FilterCondition"]]


# This is needed for the recursive type definition
FilterCondition.update_forward_refs()


class SortRule(BaseModel):
    column_id: int
    direction: SortDirection


class TableRowIdsRequest(BaseModel):
    p_table_id: str
    p_filters: Optional[FilterCondition] = None
    p_search: Optional[str] = None
    p_sorts: Optional[List[SortRule]] = None


class TableRowsRequest(BaseModel):
    target_table_id: str
    filter_json: Optional[FilterCondition] = None
    search_text: Optional[str] = None
    sort_array: Optional[List[SortRule]] = None
    p_limit: int = 100
    p_offset: int = 0


# API Routes
# @app.post("/table/{table_id}/rows")
# async def get_table_data_by_row_ids(
#     table_id: str, request: RowIdsRequest, background_tasks: BackgroundTasks
# ):
#     # Call the handler function with the necessary dependencies
#     return await get_table_data_by_row_ids_handler(
#         table_id=table_id,
#         row_ids=request.row_ids,
#         redis=app.state.redis,
#         pg_pool=app.state.pg_pool,
#         background_tasks=background_tasks,
#     )


@app.get("/hello")
def read_root():
    return {"Hello": "World"}


# @app.post("/table/row-ids")
# async def get_table_row_ids(
#     request: TableRowIdsRequest, background_tasks: BackgroundTasks
# ):
#     # Convert Pydantic model to dict for the handler
#     request_dict = request.dict()

#     # Call the handler function with the necessary dependencies
#     return await get_table_row_ids_handler(
#         request=request_dict,
#         redis=app.state.redis,
#         pg_pool=app.state.pg_pool,
#         background_tasks=background_tasks,
#     )


# @app.post("/table/rows")
# async def get_table_rows(request: TableRowsRequest, background_tasks: BackgroundTasks):
#     # Convert Pydantic model to dict for the handler
#     request_dict = request.dict()

#     # Call the handler function with the necessary dependencies
#     return await get_table_rows_handler(
#         request=request_dict,
#         redis=app.state.redis,
#         pg_pool=app.state.pg_pool,
#         background_tasks=background_tasks,
#     )


# Include routers
