# Outbound FastAPI Docker Setup

This document explains how to run the Outbound FastAPI application and Celery workers using Docker.

## Prerequisites

- <PERSON><PERSON> and <PERSON>er Compose installed
- `.env` file with necessary environment variables (see `.env.example`)
- External PostgreSQL and Redis services (connection details specified in `.env` file)

## Environment Configuration

All services are configured using a `.env` file. Ensure that your `.env` file contains these key variables:

```
# Database settings
POSTGRES_URL=**************************************************/dbname

# Redis settings
REDIS_URL=redis://your-redis-host:6379/0
CELERY_BROKER_URL=redis://your-redis-host:6379/0
CELERY_RESULT_BACKEND=redis://your-redis-host:6379/0

# Other required settings from config.py
OPENAI_API_KEY=your-api-key
# ...additional environment variables as needed
```

## Quick Start

The easiest way to run the complete application with all services:

```bash
docker-compose up -d
```

This will start:

- FastAPI application (accessible on port 8000)
- Celery workers for different queues
- Flower for monitoring Celery tasks (accessible on port 5555)

## Running Individual Services

### Building the Docker Image

Build the application image:

```bash
docker build -t outbound-app .
```

### Running the FastAPI Application

```bash
docker run -p 8000:8000 --env-file .env outbound-app api
```

### Running Celery Workers

For the default queue:

```bash
docker run --env-file .env outbound-app worker-default
```

For the enrichment queue:

```bash
docker run --env-file .env outbound-app worker-enrichment
```

For the LLM queue:

```bash
docker run --env-file .env outbound-app worker-llm
```

For all queues in one worker:

```bash
docker run --env-file .env outbound-app worker-all
```

### Running Celery Beat Scheduler

```bash
docker run --env-file .env outbound-app beat
```

### Running Flower Monitoring

```bash
docker run -p 5555:5555 --env-file .env outbound-app flower
```

## CI/CD Environment Variables

The Dockerfile supports passing environment variables directly instead of using a `.env` file. This allows for flexible configuration in CI/CD pipelines.

Example setting environment variables in a CI/CD pipeline:

```bash
docker run -p 8000:8000 \
  -e POSTGRES_URL=********************************/db \
  -e REDIS_URL=redis://redis-host:6379/0 \
  -e CELERY_BROKER_URL=redis://redis-host:6379/0 \
  -e CELERY_RESULT_BACKEND=redis://redis-host:6379/0 \
  -e OPENAI_API_KEY=sk-your-api-key \
  outbound-app api
```

## Production Deployment

For production deployment, you can run different services on separate machines:

1. **API Servers**: One or more instances of the FastAPI application (port 8000)
2. **Worker Servers**: Different Celery worker instances based on the task type:
   - `worker-default` for general tasks
   - `worker-enrichment` for data enrichment tasks
   - `worker-llm` for language model tasks

### Port Configuration

- Only the API service exposes port 8000
- Flower monitoring dashboard exposes port 5555
- Workers do not expose any ports

### Network Configuration

All services are connected to a shared network (`outbound-network`) to allow communication.
