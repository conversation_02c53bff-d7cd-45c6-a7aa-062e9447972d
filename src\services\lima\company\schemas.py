from pydantic import BaseModel, ConfigDict, Field
from typing import Optional, List, Union
from src.schemas.linkedin import LinkedinCompanyProfileUrl


# Headquarters/Location Model for Lima API
class LimaCompanyHeadquarters(BaseModel):
    model_config = ConfigDict(extra="ignore")
    city: Optional[str] = None
    state: Optional[str] = None
    country: Optional[str] = None


# Lima Company Profile Output Model - matches new API structure
class LimaCompanyProfileOutput(BaseModel):
    model_config = ConfigDict(extra="ignore")
    id: Optional[str] = None
    name: Optional[str] = None
    profile_url: Optional[str] = None
    website: Optional[str] = None
    industries: Optional[List[str]] = None
    tagline: Optional[str] = None
    description: Optional[str] = None
    employee_count: Optional[int] = None
    employee_count_range: Optional[str] = None
    follower_count: Optional[int] = None
    founded_year: Optional[str] = None
    headquarters: Optional[LimaCompanyHeadquarters] = None
    profile_image_url: Optional[str] = None
    cover_image_url: Optional[str] = None
    specialities: Optional[List[str]] = None


# Response model with credit cost for Lima API
class LimaCompanyProfileResponse(BaseModel):
    profile: LimaCompanyProfileOutput
    credit_cost: int = 0


# Input model for Lima API
class LimaCompanyProfileInput(BaseModel):
    linkedin_company_url: LinkedinCompanyProfileUrl
