import asyncio
import httpx
import json
import os
import time
from typing import Optional, List, Dict, Any

async def test_get_table_row_ids(
    table_id: str,
    filters: Optional[Dict] = None,
    search: Optional[str] = None,
    sorts: Optional[List[Dict]] = None
):
    """
    Test the table row IDs retrieval endpoint.
    
    Args:
        table_id: The ID of the table to query
        filters: Optional filter conditions
        search: Optional search term
        sorts: Optional sort rules
    """
    # API endpoint URL
    base_url = os.getenv("API_URL", "http://localhost:2024")
    url = f"{base_url}/table/row-ids"
    
    print(f"Testing endpoint: {url}")
    
    # Prepare request payload
    payload = {
        "p_table_id": table_id,
        "p_filters": filters,
        "p_search": search,
        "p_sorts": sorts
    }
    
    print("\n📝 Request Payload:")
    print(json.dumps(payload, indent=2))
    
    # Create client with increased timeout
    timeout = httpx.Timeout(30.0)  # 30 seconds timeout
    async with httpx.AsyncClient(timeout=timeout) as client:
        print("\n🔍 Sending request...")
        start_time = time.time()
        
        response = await client.post(url, json=payload)
        
        end_time = time.time()
        latency_s = end_time - start_time  # Seconds
        latency_ms = latency_s * 1000      # Milliseconds
        
        print(f"\n⏱️ Latency: {latency_s:.3f} s ({latency_ms:.2f} ms)")
        
        if response.status_code == 200:
            result = response.json()
            
            # Calculate and print response size
            response_text = response.text
            response_size_bytes = len(response_text)
            response_size_kb = response_size_bytes / 1024
            response_size_mb = response_size_kb / 1024
            
            print(f"\n📊 Response Size:")
            print(f"  Bytes: {response_size_bytes:,}")
            print(f"  KB: {response_size_kb:.2f}")
            if response_size_mb >= 1:
                print(f"  MB: {response_size_mb:.2f}")
            
            # Print cache status if available
            if "from_cache" in result:
                cache_status = "HIT ✅" if result["from_cache"] else "MISS ❌"
                print(f"\n🔄 Cache: {cache_status}")
            
            # Print row count
            row_count = len(result.get('row_ids', []))
            print(f"\n📋 Total rows: {row_count:,}")
            
            # Print average bytes per row if we have rows
            if row_count > 0:
                bytes_per_row = response_size_bytes / row_count
                print(f"  Avg bytes per row: {bytes_per_row:.2f}")
            
            # Uncomment to print full response
            # print("\n✅ Success Response:")
            # print(json.dumps(result, indent=2))
        else:
            print(f"\n❌ Error {response.status_code}:")
            print(response.text)

if __name__ == "__main__":
    # Example usage with all parameters
    TABLE_ID = "tbl_3a5bdcaff1591af3"
    
    # Example filter condition
    FILTERS = {
        "operator": "AND",
        "rules": [
            {
                "column_id": 1,
                "operator": "nempty", 
                "value": ""
            }
        ]
    }
    
    # Example search term
    SEARCH = None
    
    # Example sort rules 
    SORTS = None
    
    # Run the async test function
    asyncio.run(test_get_table_row_ids(
        table_id=TABLE_ID,
        #filters=FILTERS,
        search=SEARCH,
        sorts=SORTS
    ))

    # Or run without optional parameters
    # asyncio.run(test_get_table_row_ids(TABLE_ID))
