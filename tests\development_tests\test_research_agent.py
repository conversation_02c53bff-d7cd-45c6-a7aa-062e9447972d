"""
Test file for the Research Agent built with LangGraph.

This script demonstrates how to invoke the agent and process its responses.
"""

from dotenv import load_dotenv
import os
import sys
import asyncio
from langchain_core.messages import HumanMessage, ToolMessage, AIMessage
import json
from urllib.parse import urlparse

# Add the project root to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Import the agent graph
from src.agent.graph import graph

load_dotenv()


def extract_domain(url):
    """Extract just the domain from a URL."""
    try:
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
        return domain if domain else url
    except:
        return url


def extract_tool_arg(tool_call, arg_names=None):
    """Extract argument from a tool call with flexible argument naming."""
    if arg_names is None:
        arg_names = ["__arg1", "query", "url"]
    
    args = tool_call["args"]
    
    # Handle dictionary arguments
    if isinstance(args, dict):
        for name in arg_names:
            if name in args:
                return args[name]
    
    # Handle string arguments (JSON)
    elif isinstance(args, str):
        try:
            args_dict = json.loads(args)
            for name in arg_names:
                if name in args_dict:
                    return args_dict[name]
        except:
            return args
    
    return "unknown"


def print_agent_progress(stream):
    """Print only the agent's progress and final output."""
    final_output = None
    
    for s in stream:
        message = s["messages"][-1]
        
        # Handle tool messages (search, website reading, etc.)
        if isinstance(message, ToolMessage):
            tool_name = message.name
            
            # Find the corresponding tool call in previous messages
            for prev_msg in s["messages"]:
                if hasattr(prev_msg, "tool_calls"):
                    for tool_call in prev_msg.tool_calls:
                        if tool_call["name"] == tool_name:
                            if tool_name == "search":
                                query = extract_tool_arg(tool_call, ["__arg1", "query"])
                                print(f"🔍 Searching for: '{query}'")
                            
                            elif tool_name == "scrape_website":
                                url = extract_tool_arg(tool_call, ["__arg1", "url"])
                                domain = extract_domain(url)
                                print(f"📄 Reading website: {domain}")
                            
                            elif tool_name == "crawl_website":
                                url = extract_tool_arg(tool_call, ["__arg1", "url"])
                                domain = extract_domain(url)
                                print(f"🕸️ Browsing website: {domain}")
                            
                            break
        
        # Handle AI messages (thinking or final answer)
        elif isinstance(message, AIMessage):
            if not hasattr(message, "tool_calls") or not message.tool_calls:
                final_output = message.content
            else:
                print("🤔 Thinking...")
    
    # Print the final output
    if final_output:
        print("\n===== FINAL ANSWER =====")
        print(final_output)
        print("=======================\n")


async def test_research_agent():
    """Test the research agent with a sample query."""
    print("\n\n===== Testing Research Agent =====\n")
    
    # Create input for the agent
    query = """Retrun for me the LinkedIn profile URL of the decision maker of phyros.io
    Make sure it is the decision maker, not any other employee. Make sure it is a person, not a company.
      Don't add any other text. Just the URL."""
    print(f"Question: {query}\n")
    
    inputs = {"messages": [HumanMessage(content=query)]}
    
    # Stream the agent's responses and show progress
    stream = graph.stream(inputs, stream_mode="values")
    print_agent_progress(stream)


async def test_agent_with_follow_up():
    """Test the agent with an initial query and follow-up questions."""
    print("\n\n===== Testing Agent with Follow-up Questions =====\n")
    
    # Initial query
    query = "What are the main features of LangGraph?"
    print(f"Initial Question: {query}\n")
    
    # Initial conversation state
    state = {"messages": [HumanMessage(content=query)]}
    
    # First interaction
    stream = graph.stream(state, stream_mode="values")
    result = list(stream)
    print_agent_progress(stream)
    
    # Add a follow-up question to the conversation
    follow_up_query = "How does it compare to other workflow frameworks?"
    print(f"\nFollow-up Question: {follow_up_query}\n")
    
    # Merge the follow-up with the existing conversation
    merged_state = result[-1].copy()
    merged_state["messages"] = merged_state["messages"] + [HumanMessage(content=follow_up_query)]
    
    # Process the follow-up
    stream = graph.stream(merged_state, stream_mode="values")
    print_agent_progress(stream)


if __name__ == "__main__":
    # Run the tests
    asyncio.run(test_research_agent())
    # Uncomment to test follow-up questions
    # asyncio.run(test_agent_with_follow_up()) 