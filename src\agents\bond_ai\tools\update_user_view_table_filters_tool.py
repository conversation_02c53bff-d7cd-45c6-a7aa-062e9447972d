from typing import Optional, Dict, Any, <PERSON><PERSON>, Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool, InjectedToolArg
from langgraph.config import get_stream_writer
from bond_ai.configuration import Configuration

from ..models.models import FilterGroup
from ..agent_db import  update_table_filters

from langgraph.types import Command
from langchain_core.messages import ToolMessage
from langchain_core.tools import InjectedToolCallId

@tool
def update_user_view_table_filters_tool(
    tool_call_id: Annotated[str, InjectedToolCallId],
    config: Annotated[RunnableConfig, InjectedToolArg],
    filters: Optional[FilterGroup] = None
) -> Command:
    """Update the filters field for the current table.
    
    This tool updates only the filters field in the current table,
    which controls how data is filtered in table views.
    
    Parameters:
        config: Configuration injected by the system which contains table_id
        filters: Filtering conditions using the FilterGroup model, same structure
                as used in read_table_data tool
        
    Returns:
        Tuple[Optional[Dict[str, Any]], Optional[str]]: Tuple containing (updated_data, error)
        where updated_data is the updated table data if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Updating table filters"})
        # Get the table_id from the configuration
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        
        # Convert filters to dict without complex formatting
        if filters:
            filters_dict = filters.model_dump(mode='json')
        else:
            filters_dict = {}
        
        # Call the database function with the filters as they are
        updated_data, error = update_table_filters(table_id, filters_dict)
        
        if error:
            return Command(update={
                "last_error_message": error,
                "messages": [
                    ToolMessage(
                        content=error,
                        tool_call_id=tool_call_id,
                    )
                ]
            })
            
        # Return the updated data
        return Command(update={
                "last_error_message": None,
                "messages": [
                    ToolMessage(
                        content= updated_data,
                        tool_call_id=tool_call_id,
                    )
                ]
            })
       
            
    except Exception as e:
        error_msg = f"Error updating table filters: {str(e)}"
        return Command(update={
                "last_error_message": error_msg,
                "messages": [
                    ToolMessage(
                        content= error_msg,
                        tool_call_id=tool_call_id,
                    )
                ]
            })
