"""SQL query execution tool."""

from typing import Annotated, Optional, List, Dict, Any, Tuple
from langchain_core.tools import tool

from ..agent_db import db_run_sql_query

from langgraph.types import Command
from langchain_core.messages import ToolMessage
from langchain_core.tools import InjectedToolCallId
@tool
def run_sql_query(
    tool_call_id: Annotated[str, InjectedToolCallId],
    sql_query: str
) -> Command:
    """Execute a SQL query through a secured RPC endpoint.
    
    This tool allows the agent to execute SQL queries against the USER table to retrieve data
    or perform analysis.

    EXAMPLE: SELECT * FROM tables_views.table_id LIMIT 1; to get the first row of the table.
    
    Parameters:
        sql_query: The SQL query to execute. Must be a valid SQL query string.
        
    Returns:
        Tuple[Optional[List[Dict[str, Any]]], Optional[str]]: Tuple containing (results, error)
        where results is a list of rows returned by the query if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        # Call the db_run_sql_query function from agent_db.py
        results, error = db_run_sql_query(sql_query)
        
        if error:
            return Command(update={
                "last_error_message": error,
                "messages": [
                    ToolMessage(
                        content=error,
                        tool_call_id=tool_call_id,
                    )
                ]
            })
            
        return Command(update={
                "last_error_message": None,
                "messages": [
                    ToolMessage(
                        content= results,
                        tool_call_id=tool_call_id,
                    )
                ]
            })
        
    except Exception as e:
        error_msg = f"Error executing SQL query: {str(e)}"
        return Command(update={
                "last_error_message": error_msg,
                "messages": [
                    ToolMessage(
                        content= error_msg,
                        tool_call_id=tool_call_id,
                    )
                ]
            })
