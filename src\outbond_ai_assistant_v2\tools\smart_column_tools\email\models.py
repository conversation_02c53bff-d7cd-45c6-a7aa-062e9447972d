"""Pydantic models for LinkedIn profile column creation and editing operations."""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, ConfigDict, field_validator


class CreateOrEditPersonEmailColumnRequest(BaseModel):
    """Request model for creating or editing person email columns.
    
    This model defines the structure for creating/editing person email columns with comprehensive
    validation and documentation following Google style guidelines.
    """
    model_config = ConfigDict(use_enum_values=True)

    action: str = Field(
        ...,
        description="The action to perform on the person email column",
        max_length=15,
        examples=["COLUMN_CREATE", "COLUMN_UPDATE"]
    )
    
    column_name: str = Field(
        ...,
        description="Name of the person email column",
        min_length=1,
        max_length=255,
        examples=["Person Email", "Email", "Email Data", "Work Email", "Contact Email"]
    )
    
    full_name_injection_path: str = Field(
        ...,
        description="The injection sequence for full name (must be wrapped in double curly braces {{}})",
        min_length=4,
        examples=[
            "{{Linkedin Profile.cell_details.full_name}}",
            "{{Person.cell_details.full_name}}",
            "{{Full Name.cell_value}}"
        ]
    )
    
    company_website_injection_path: str = Field(
        ...,
        description="The injection sequence for company website (must be wrapped in double curly braces {{}})",
        min_length=4,
        examples=[
            "{{Linkedin Company Profile.cell_details.company_website}}",
            "{{Company.cell_details.website}}",
            "{{Company Website.cell_value}}"
        ]
    )

    @field_validator('full_name_injection')
    @classmethod
    def validate_full_name_injection_sequence_format(cls, v: str) -> str:
        """Validate that injection sequence is wrapped in double curly braces."""
        if not isinstance(v, str):
            raise ValueError('full_name_injection must be a string')
        
        # Check if it starts with {{ and ends with }}
        if not (v.startswith('{{') and v.endswith('}}')):
            raise ValueError('full_name_injection must be wrapped in double curly braces {{}}')
        
        # Check that there's content between the braces
        content = v[2:-2].strip()
        if not content:
            raise ValueError('full_name_injection cannot be empty between the curly braces')
        
        return v

    @field_validator('company_website_injection')
    @classmethod
    def validate_company_website_injection_sequence_format(cls, v: str) -> str:
        """Validate that injection sequence is wrapped in double curly braces."""
        if not isinstance(v, str):
            raise ValueError('company_website_injection must be a string')
        
        # Check if it starts with {{ and ends with }}
        if not (v.startswith('{{') and v.endswith('}}')):
            raise ValueError('company_website_injection must be wrapped in double curly braces {{}}')
        
        # Check that there's content between the braces
        content = v[2:-2].strip()
        if not content:
            raise ValueError('company_website_injection cannot be empty between the curly braces')
        
        return v

class PersonEmailColumnResponse(BaseModel):
    """Response model for person email column operations.
    
    This model defines the structure of successful person email column responses,
    providing clear typing and documentation.
    """
    model_config = ConfigDict(use_enum_values=True)
    
    column: Optional[Dict[str, Any]] = Field(
        default=None,
        description="The complete column data when operation is successful"
    )
    
    action: Optional[str] = Field(
        default=None,
        description="The action to perform on the person email column",
        max_length=15,
        examples=["COLUMN_CREATE", "COLUMN_UPDATE"]
    )
    
    success: bool = Field(
        default=True,
        description="Indicates if the operation was successful"
    )

    error_message: Optional[str] = Field(
        default=None,
        description="Detailed error message explaining what went wrong"
    )

