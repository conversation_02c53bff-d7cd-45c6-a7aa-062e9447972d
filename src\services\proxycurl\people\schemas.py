from typing import List, Optional, Union
from pydantic import BaseModel, ConfigDict
from src.schemas.linkedin import (
    LinkedinPersonProfileUrl,
    LinkedinCompanyProfileUrl,
    LinkedinSchoolProfileUrl,
)


# Date Object Model
class DateObject(BaseModel):
    day: Optional[int] = None
    month: Optional[int] = None
    year: Optional[int] = None


# Experience Model
class Experience(BaseModel):
    model_config = ConfigDict(extra="ignore")
    company: Optional[str] = None
    company_linkedin_profile_url: Optional[
        Union[LinkedinSchoolProfileUrl, LinkedinCompanyProfileUrl]
    ] = None
    company_facebook_profile_url: Optional[str] = None
    title: Optional[str] = None
    description: Optional[str] = None
    location: Optional[str] = None
    logo_url: Optional[str] = None
    starts_at: Optional[DateObject] = None
    ends_at: Optional[DateObject] = None


# Education Model
class Education(BaseModel):
    model_config = ConfigDict(extra="ignore")
    school: Optional[str] = None
    school_linkedin_profile_url: Optional[
        Union[LinkedinSchoolProfileUrl, LinkedinCompanyProfileUrl]
    ] = None
    school_facebook_profile_url: Optional[str] = None
    field_of_study: Optional[str] = None
    degree_name: Optional[str] = None
    description: Optional[str] = None
    starts_at: Optional[DateObject] = None
    ends_at: Optional[DateObject] = None
    logo_url: Optional[str] = None


# Certification Model
class Certification(BaseModel):
    model_config = ConfigDict(extra="ignore")
    name: Optional[str] = None
    authority: Optional[str] = None
    license_number: Optional[str] = None
    starts_at: Optional[DateObject] = None
    ends_at: Optional[DateObject] = None


# Project Model
class Project(BaseModel):
    model_config = ConfigDict(extra="ignore")
    title: Optional[str] = None
    description: Optional[str] = None
    url: Optional[str] = None
    starts_at: Optional[DateObject] = None
    ends_at: Optional[DateObject] = None


# Activity Model
class Activity(BaseModel):
    model_config = ConfigDict(extra="ignore")
    title: Optional[str] = None
    link: Optional[str] = None
    activity_status: Optional[str] = None


# Similar Profile Model
class SimilarProfile(BaseModel):
    model_config = ConfigDict(extra="ignore")
    name: Optional[str] = None
    link: Optional[str] = None
    summary: Optional[str] = None
    location: Optional[str] = None


# People Also Viewed Model
class PeopleAlsoViewed(BaseModel):
    model_config = ConfigDict(extra="ignore")
    name: Optional[str] = None
    link: Optional[str] = None
    summary: Optional[str] = None
    location: Optional[str] = None


# Salary Range Model
class SalaryRange(BaseModel):
    model_config = ConfigDict(extra="ignore")
    min: Optional[int] = None
    max: Optional[int] = None


# LinkedIn Profile Response Model
class PersonProfileOutput(BaseModel):
    model_config = ConfigDict(extra="ignore")
    public_identifier: Optional[str] = None
    profile_pic_url: Optional[str] = None
    # background_cover_image_url: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    full_name: Optional[str] = None
    follower_count: Optional[int] = None
    occupation: Optional[str] = None
    headline: Optional[str] = None
    summary: Optional[str] = None
    country: Optional[str] = None
    country_full_name: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    connections: Optional[int] = None

    experiences: Optional[List[Experience]] = None
    education: Optional[List[Education]] = None
    certifications: Optional[List[Certification]] = None
    projects: Optional[List[Project]] = None
    skills: Optional[List[str]] = None
    inferred_salary: Optional[SalaryRange] = None
    personal_emails: Optional[List[str]] = None
    personal_numbers: Optional[List[str]] = None
    activities: Optional[List[Activity]] = None
    recommendations: Optional[List[str]] = None
    similarly_named_profiles: Optional[List[SimilarProfile]] = None
    people_also_viewed: Optional[List[PeopleAlsoViewed]] = None

    # github_profile_id: Optional[str] = None
    # facebook_profile_id: Optional[str] = None
    # twitter_profile_id: Optional[str] = None


class PersonProfileResponse(BaseModel):
    model_config = ConfigDict(extra="ignore")
    profile: PersonProfileOutput
    credit_cost: int
