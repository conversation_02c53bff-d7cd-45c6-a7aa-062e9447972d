import httpx
import asyncio

GET_PROFILE_REQUEST_TEMPLATE = {
    "organization_id": "org_a875c70027e7c3bc",
    "table_id": "tbl_c85117eb4106d464", 
    "column_id": 2,
    "row_id": 2,
    "service_id": 7,
    "run_id": 13,
    "credits": 1,
    "formula": 'Only run if <User formula> "The {{var1}} is a valid linkedin url" </User formula> <Values from the backend> {{var1}} = "https://www.linkedin.com/in/abudi-mohamed-846b67164/" </Values from the backend>',
    "providers": {
        "providers": []
    },
    "value": {
        "linkedin_profile_url": "google.com"
    },
}

LOOKUP_PROFILE_REQUEST_TEMPLATE = {
    "organization_id": "org_64bd7f51c1450d62",
    "table_id": "tbl_3655e99e74169376",
    "column_id": 7,
    "row_id": 1,
    "service_id": 16,  # Formula service ID
    "run_id": 1,
    "credits": 10,
    "formula": None,
    "value": {
        "company_domain": "outbond.io",
        "full_name": "Abudi Mohamed",
    },
}

GET_COMPANY_REQUEST_TEMPLATE = {
    "organization_id": "org_64bd7f51c1450d62",
    "table_id": "tbl_3655e99e74169376",
    "column_id": 7,
    "row_id": 1,
    "service_id": 8,  # Formula service ID
    "run_id": 1,
    "credits": 10,
    "formula": None,
    "value": {
        "linkedin_company_url": "https://www.linkedin.com/company/optum/",
    },
}

LOOKUP_COMPANY_REQUEST_TEMPLATE = {
    "organization_id": "org_64bd7f51c1450d62",
    "table_id": "tbl_3655e99e74169376",
    "column_id": 7,
    "row_id": 1,
    "service_id": 17,  # Formula service ID
    "run_id": 1,
    "credits": 10,
    "formula": "always valid",
    "value": {
        "company_domain": "outbond.io",
    },
}


async def test_get_person_profile():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:2024/run-cell",
            json=GET_PROFILE_REQUEST_TEMPLATE,
        )
        print(response.json())


async def test_lookup_person_profile():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:2024/run-cell",
            json=LOOKUP_PROFILE_REQUEST_TEMPLATE,
        )
        print(response.json())


async def test_get_company_profile():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:2024/run-cell",
            json=GET_COMPANY_REQUEST_TEMPLATE,
        )
        print(response.json())


async def test_lookup_company_profile():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8001/run-cell",
            json=LOOKUP_COMPANY_REQUEST_TEMPLATE,
        )
        print(response.json())


if __name__ == "__main__":
    # Run the async function using asyncio
    # asyncio.run(test_get_person_profile())
    # asyncio.run(test_get_company_profile())
    #asyncio.run(test_lookup_person_profile())
    asyncio.run(test_lookup_company_profile())
