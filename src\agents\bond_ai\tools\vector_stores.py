"""Simple domain category search tool.

This tool loads a list of domain categories from either a JSON file or a
semicolon separated text file and performs a lightweight token-based similarity
search.

JSON format (preferred):
    Each item should contain: {"id", "text", "category", "description"}

It is intended to help the build list agent map free-form user queries such as
"tech domain" to the most relevant predefined categories.

The implementation supports both OpenAI embeddings (when API key is available)
Similarity is computed using cosine similarity over embeddings.
"""
from __future__ import annotations

from pydantic import BaseModel, field_validator,Field
from typing import Annotated, List, Literal, Optional, Union

from enum import Enum

from dataclasses import  dataclass
from pathlib import Path
from typing import List, Literal, Tuple
from collections import Counter

import re
import numpy as np
import os
import hashlib
import pickle
import json
try:
    from openai import OpenAI  # type: ignore
except Exception:  # pragma: no cover - optional dependency
    OpenAI = None  # type: ignore

from langchain_core.tools import tool
from langgraph.config import get_stream_writer


DEFAULT_INDUSTRIES_JSON_FILE = Path(__file__).with_name("industries_with_descriptions.json")
DEFAULT_JOB_TITLES_CSV_FILE = Path(__file__).with_name("lima_titles.csv")



@dataclass
class Industry:
    """Represents a single domain category loaded from the CSV file."""
    industry_id: str
    label: str
    hierarchy: str
    description: str


@dataclass
class JobTitle:
    """Represents a single job title loaded from the CSV file."""
    title_id: str
    title: str

from dotenv import load_dotenv
load_dotenv()

class IndustryVectorStore:
    """In-memory vector store for industry categories.

    Falls back to a deterministic local bag-of-words embedding when OpenAI
    is unavailable (no key or package missing). Caches embeddings on disk
    keyed by a content hash of the input texts.
    """

    def __init__(self, industry_file: Path | str = DEFAULT_INDUSTRIES_JSON_FILE):
        # Load categories...
        path = Path(industry_file)

        # If use_json_format is True, try to load from JSON file first
        
        json_path = Path(industry_file).parent / "industries_with_descriptions.json"
        if json_path.exists():
            path = json_path

        if not path.exists():
            raise FileNotFoundError(f"Categories file not found: {path}")
 
        self.industries = self._load_from_json(path)
     
        texts = [f"{c.label} {c.description}" for c in self.industries]

        # Choose embedding backend
        api_key = os.environ.get("OPENAI_API_KEY")
        self._use_openai = bool(api_key) and (OpenAI is not None)
        self.client = OpenAI(api_key=api_key) if self._use_openai else None

        # Pre-compute embeddings using selected backend
        self.embeddings = self._load_or_create_embeddings(texts)

    def _load_from_json(self, path: Path) -> List[Industry]:
        """Load categories from JSON format."""
        with path.open(encoding="utf-8") as f:
            data = json.load(f)

        industries = []
        for item in data:
            industries.append(Industry(
                industry_id=str(item.get("id", "")),
                label=item.get("text", ""),
                hierarchy=item.get("category", ""),  # Use category as hierarchy
                description=item.get("description", ""),
            ))
        return industries

    # ----------------- Caching helpers -----------------
    def _get_content_hash(self, texts: List[str]) -> str:
        h = hashlib.sha256()
        for t in texts:
            h.update(t.encode("utf-8"))
            h.update(b"\0")
        return h.hexdigest()

    def _load_or_create_embeddings(self, texts: List[str]) -> np.ndarray:
        cache_path = Path("industry_embeddings.pkl")
        current_hash = self._get_content_hash(texts)

        if cache_path.exists():
            with open(cache_path, 'rb') as f:
                cached_data = pickle.load(f)
            # Backward compatibility: older cache stored raw ndarray
            if isinstance(cached_data, np.ndarray):
                return cached_data
            # New format: dict with embeddings and content_hash
            stored_hash = cached_data.get('content_hash') if isinstance(cached_data, dict) else None
            if stored_hash == current_hash and 'embeddings' in cached_data:
                return cached_data['embeddings']

        # Rebuild embeddings
        embeddings = self._get_embeddings(texts)

        # Save cache
        with open(cache_path, 'wb') as f:
            pickle.dump({'embeddings': embeddings, 'content_hash': current_hash}, f)
        return embeddings

    def _bag_of_words_embed(self, texts: List[str]) -> np.ndarray:
        # Simple 4-char prefix tokenization with light stopwords
        STOPWORDS = {
            "company","companies","co","inc","corp","corporation","llc","ltd","plc",
            "services","service","provider","providers","industry","industries",
            "business","domain","technical","and","the","of","a","an","group","groups",
            "holding","holdings",
        }
        def tokenize(s: str, k: int = 4):
            return [w[:k] for w in re.findall(r"\w+", s.lower()) if w and w not in STOPWORDS]
        vocab: list[str] = []
        seen = set()
        tokenized = []
        for t in texts:
            toks = tokenize(t)
            tokenized.append(toks)
            for tok in toks:
                if tok not in seen:
                    seen.add(tok)
                    vocab.append(tok)
        idx = {tok: i for i, tok in enumerate(vocab)}
        mat = np.zeros((len(texts), len(vocab)), dtype=float)
        for r, toks in enumerate(tokenized):
            counts = Counter(toks)
            for tok, cnt in counts.items():
                mat[r, idx[tok]] = float(cnt)
        # L2 normalize
        norms = np.linalg.norm(mat, axis=1, keepdims=True)
        norms[norms == 0] = 1.0
        return mat / norms

    def _get_embeddings(self, texts: List[str]) -> np.ndarray:
        """Get embeddings for a list of texts using OpenAI if available, otherwise BoW."""
        if self._use_openai and self.client is not None:
            try:
                response = self.client.embeddings.create(input=texts, model="text-embedding-3-small")
                return np.array([data.embedding for data in response.data])
            except Exception:
                pass  # fall back to local embedding
        return self._bag_of_words_embed(texts)

    def query(self, text: str, top_k: int = 3) -> List[Tuple[Industry, float]]:
        # Expand common abbreviations
        expanded_text = text.lower()
        # if "saas" in expanded_text:
        #     expanded_text = expanded_text.replace("saas", "software as a service")
        # if "tech" in expanded_text:
        #     expanded_text = expanded_text.replace("tech", "technology")

        # Get query embedding and compute similarities
        query_embedding = self._get_embeddings([expanded_text])[0]
        similarities = np.dot(self.embeddings, query_embedding) / (
            np.linalg.norm(self.embeddings, axis=1) * np.linalg.norm(query_embedding)
        )

        top_indices = np.argsort(similarities)[::-1][:top_k]
        return [(self.industries[i], float(similarities[i])) for i in top_indices]


class JobTitleVectorStore:
    """In-memory vector store for job titles.

    Falls back to a deterministic local bag-of-words embedding when OpenAI
    is unavailable (no key or package missing). Caches embeddings on disk
    keyed by a content hash of the input texts.
    """

    def __init__(self, titles_file: Path | str = DEFAULT_JOB_TITLES_CSV_FILE):
        # Load job titles from CSV
        path = Path(titles_file)

        if not path.exists():
            raise FileNotFoundError(f"Job titles file not found: {path}")

        self.job_titles = self._load_from_csv(path)

        texts = [jt.title for jt in self.job_titles]

        # Choose embedding backend
        api_key = os.environ.get("OPENAI_API_KEY")
        self._use_openai = bool(api_key and api_key.strip()) and (OpenAI is not None)
        self.client = OpenAI(api_key=api_key) if self._use_openai else None

        # Pre-compute embeddings using selected backend
        self.embeddings = self._load_or_create_embeddings(texts)

        # Determine actual embedding method used (may differ from intended if OpenAI fails)
        self._actual_embedding_method = "openai" if self.embeddings.shape[1] == 1536 else "bow"

    def _load_from_csv(self, path: Path) -> List[JobTitle]:
        """Load job titles from CSV format."""
        import csv

        job_titles = []
        with path.open(encoding="utf-8") as f:
            reader = csv.DictReader(f)
            for i, row in enumerate(reader):
                # Use the 'title' column or first column if no header
                title = row.get('title', list(row.values())[0] if row else '')
                if title.strip():  # Skip empty titles
                    job_titles.append(JobTitle(
                        title_id=str(i),
                        title=title.strip()
                    ))
        return job_titles

    # ----------------- Caching helpers -----------------
    def _get_content_hash(self, texts: List[str]) -> str:
        h = hashlib.sha256()
        for t in texts:
            h.update(t.encode("utf-8"))
            h.update(b"\0")
        return h.hexdigest()

    def _load_or_create_embeddings(self, texts: List[str]) -> np.ndarray:
        cache_path = Path("job_title_embeddings.pkl")
        current_hash = self._get_content_hash(texts)
        embedding_method = "openai" if self._use_openai else "bow"
        cache_key = f"{current_hash}_{embedding_method}"

        if cache_path.exists():
            with open(cache_path, 'rb') as f:
                cached_data = pickle.load(f)
            if isinstance(cached_data, dict):
                stored_key = cached_data.get('cache_key')
                emb = cached_data.get('embeddings')
                vocab = cached_data.get('vocab')
                if stored_key == cache_key and emb is not None:
                    # Restore vocab (needed for BoW queries)
                    if vocab is not None:
                        self._vocab = vocab
                        self._vocab_idx = {tok: i for i, tok in enumerate(vocab)}
                    return emb

        # Rebuild embeddings + vocab if BoW
        embeddings = self._get_embeddings(texts)

        payload = {'embeddings': embeddings, 'cache_key': cache_key}
        if getattr(self, '_vocab', None) is not None:
            payload['vocab'] = self._vocab

        with open(cache_path, 'wb') as f:
            pickle.dump(payload, f)
        return embeddings


    def _bag_of_words_embed(self, texts: List[str]) -> np.ndarray:
        STOPWORDS = {"and","the","of","a","an","or","for","in","at","to","with","by"}

        def tokenize(s: str, k: int = 4):
            import re
            return [w[:k] for w in re.findall(r"\w+", s.lower()) if w and w not in STOPWORDS]

        # If we already have a vocab, we are embedding queries: project into it.
        if hasattr(self, '_vocab') and hasattr(self, '_vocab_idx'):
            vocab = self._vocab
            idx = self._vocab_idx
            mat = np.zeros((len(texts), len(vocab)), dtype=float)
            for r, t in enumerate(texts):
                counts = Counter(tokenize(t))
                for tok, cnt in counts.items():
                    j = idx.get(tok)
                    if j is not None:
                        mat[r, j] = float(cnt)
            norms = np.linalg.norm(mat, axis=1, keepdims=True)
            norms[norms == 0] = 1.0
            return mat / norms

        # Otherwise we’re building from the corpus: create and store vocab.
        vocab: list[str] = []
        seen = set()
        tokenized = []
        for t in texts:
            toks = tokenize(t)
            tokenized.append(toks)
            for tok in toks:
                if tok not in seen:
                    seen.add(tok)
                    vocab.append(tok)
        idx = {tok: i for i, tok in enumerate(vocab)}
        self._vocab = vocab
        self._vocab_idx = idx

        mat = np.zeros((len(texts), len(vocab)), dtype=float)
        for r, toks in enumerate(tokenized):
            counts = Counter(toks)
            for tok, cnt in counts.items():
                j = idx.get(tok)
                if j is not None:
                    mat[r, j] = float(cnt)
        norms = np.linalg.norm(mat, axis=1, keepdims=True)
        norms[norms == 0] = 1.0
        return mat / norms

    def _clean_texts(self, texts: list) -> list[str]:
        """Coerce to plain str list compatible with OpenAI embeddings API."""
        clean: list[str] = []
        for t in texts:
            # normalize None and non-str types
            if t is None:
                s = ""
            elif isinstance(t, str):
                s = t
            else:
                # numpy.str_, bytes, numbers -> string
                try:
                    s = str(t)
                except Exception:
                    s = ""
            s = s.strip()
            # OpenAI accepts empty strings but better to keep a single space to be explicit
            clean.append(s if s else " ")
        return clean

    def _get_embeddings(self, texts: List[str]) -> np.ndarray:
        # If corpus embedding method already decided, keep it for queries
        if hasattr(self, '_actual_embedding_method') and self._actual_embedding_method == "bow":
            return self._bag_of_words_embed(texts)

        if self._use_openai and self.client is not None:
            try:
                clean = self._clean_texts(list(texts))  # ensure py list[str]
                # Avoid accidentally passing a numpy array
                resp = self.client.embeddings.create(
                    model="text-embedding-3-small",
                    input=clean,
                )
                embs = np.array([d.embedding for d in resp.data], dtype=float)
                return embs
            except Exception as e:
                # One bad request likely means incompatible input; switch to BoW for this instance
                print(f"OpenAI embedding failed, falling back to BoW: {e}")
                self._actual_embedding_method = "bow"
                self._use_openai = False
                self.client = None

        return self._bag_of_words_embed(texts)


    def query(self, text: str, top_k: int = 5) -> List[Tuple[JobTitle, float]]:
        # Expand common job title abbreviations and synonyms
        expanded_text = text.lower()

        # Common job title expansions
        expansions = {
            "dev": "developer",
            "eng": "engineer",
            "mgr": "manager",
            "dir": "director",
            "vp": "vice president",
            "ceo": "chief executive officer",
            "cto": "chief technology officer",
            "cfo": "chief financial officer",
            "hr": "human resources",
            "qa": "quality assurance",
            "ui": "user interface",
            "ux": "user experience",
            "ai": "artificial intelligence",
            "ml": "machine learning",
            "it": "information technology",
            "admin": "administrator",
            "coord": "coordinator",
            "spec": "specialist",
            "rep": "representative",
            "exec": "executive"
        }

        for abbrev, full in expansions.items():
            if abbrev in expanded_text:
                expanded_text = expanded_text.replace(abbrev, full)

      # Get query embedding and compute similarities
        qe = self._get_embeddings([expanded_text])

        # Normalize to a 1D vector
        qe = np.asarray(qe)
        if qe.ndim == 2:
            # Expecting (1, D); if something else, take the first row defensively
            if qe.shape[0] == 0:
                raise ValueError("Empty query embedding returned.")
            query_embedding = qe[0]
        elif qe.ndim == 1:
            query_embedding = qe
        else:
            raise ValueError(f"Unexpected query embedding shape: {qe.shape}")

        # Ensure 1D (flatten no-ops if already 1D)
        query_embedding = query_embedding.ravel()

        # Sanity check: must match corpus dimensionality
        if query_embedding.shape[0] != self.embeddings.shape[1]:
            raise ValueError(
                f"Embedding dim mismatch: corpus={self.embeddings.shape[1]} "
                f"vs query={query_embedding.shape[0]}. "
                "Likely missing/restoring BoW vocab or stale cache."
            )

        # Cosine similarity
        den = np.linalg.norm(self.embeddings, axis=1) * np.linalg.norm(query_embedding)
        # avoid division by zero
        den[den == 0] = 1.0
        similarities = (self.embeddings @ query_embedding) / den


        top_indices = np.argsort(similarities)[::-1][:top_k]
        return [(self.job_titles[i], float(similarities[i])) for i in top_indices]

# Global store instance to maintain vocabulary consistency
_job_title_store = None
_industry_store = None

def _get_job_title_store():
    """Get or create the global job title store instance."""
    global _job_title_store
    if _job_title_store is None:
        _job_title_store = JobTitleVectorStore()
    return _job_title_store

def _get_industry_store():
    """Get or create the global domain category store instance."""
    global _industry_store
    if _industry_store is None:
        _industry_store = IndustryVectorStore()
    return _industry_store


class SearchInput(BaseModel):
    """Input model for unified search tool."""
    
    source: Literal["industry", "job_title"] = Field(
        ..., 
        description="The type of search to perform: 'industry' for industry categories, 'job_title' for job titles"
    )
    query: str = Field(
        ..., 
        min_length=1,
        description="Free-form user description to search for"
    )
    top_k: int = Field(
        default=5,
        ge=1,
        le=20,
        description="Number of results to return (1-20)"
    )
    
    @field_validator('query')
    def validate_query(cls, v):
        """Ensure query is not just whitespace."""
        if not v.strip():
            raise ValueError("Query cannot be empty or just whitespace")
        return v.strip()


@tool(args_schema=SearchInput)
def vector_store_semantic_search(source: str, query: str, top_k: int = 5) -> List[str]:
    """Unified search tool for domain categories and job titles.
    
    This tool can search either domain categories or job titles based on the source parameter.
    It can be easily extended to support additional search sources in the future.
    
    Args:
        input_data: SearchInput model containing source, query, and top_k parameters
        
    Returns:
        List of the top matching results ordered from most to least similar.
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({
            "custom_tool_call": f"Searching {source} for '{query}' (top {top_k})"
        })
    except Exception:
        # Stream writer not available in this context
        pass
    
    if source == "industry":
        store = _get_industry_store()
        results = store.query(query, top_k=top_k)
        return [cat.label for cat, _ in results]
    
    elif source == "job_title":
        store = _get_job_title_store()
        results = store.query(query, top_k=top_k)
        return [job_title.title for job_title, _ in results]
    
    else:
        # This should never happen due to Literal type validation, but good to have
        raise ValueError(f"Unsupported search source: {source}")



# @tool
# def search_domain_categories_deprecated(query: str, top_k: int = 3) -> List[str]:
#     """Return the most relevant domain categories for the provided query.

#     Args:
#         query: Free-form user description of a company or people domain.
#         top_k: Number of categories to return (default: 3).

#     Returns:
#         List of the top matching categories ordered from most to least similar.
#     """

#     stream_writer = get_stream_writer()
#     stream_writer({"custom_tool_call": f"Searching domain categories for {query}"})
    
#     store = DomainCategoryVectorStore()
    
#     results = store.query(query, top_k=top_k)
#     return [cat.label for cat, _ in results]




# @tool
# def search_job_titles_deprecated(query: str, top_k: int = 5) -> List[str]:
#     """Return the most relevant job titles for the provided query.

#     Args:
#         query: Free-form user description of a job role or position.
#         top_k: Number of job titles to return (default: 5).

#     Returns:
#         List of the top matching job titles ordered from most to least similar.
#     """

#     try:
#         stream_writer = get_stream_writer()
#         stream_writer({"custom_tool_call": f"Searching job titles for {query}"})
#     except Exception:
#         # Stream writer not available in this context
#         pass

#     store = _get_job_title_store()

#     results = store.query(query, top_k=top_k)
#     return [job_title.title for job_title, _ in results]

