"""Database operations for updating column data by names or IDs."""

from typing import Dict, <PERSON><PERSON>, Optional, Any, Union, List
from ..client import supabase
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg
from typing import Annotated
from ...configuration import Configuration


def update_column_by_name(
    config: Annotated[RunnableConfig, InjectedToolArg],
    column_name: str,
    fields: Dict[str, Any]
) -> <PERSON><PERSON>[Optional[Dict[str, Any]], Optional[str]]:
    """Update multiple fields of a column by column name.
    
    Args:
        column_name (str): Name of the column to update
        fields (Dict[str, Any]): Dictionary of field names and their new values
                                 (e.g., {'description': 'new desc', 'settings': {...}})
        
    Returns:
        Tuple[Optional[Dict[str, Any]], Optional[str]]: Tuple containing (updated_data, error)
        where updated_data is the updated column data if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        # Extract table_id from config using Configuration class
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        
        # Update the column by name and table_id with multiple fields
        response = supabase.table('columns').update(fields).eq('table_id', table_id).eq('name', column_name).execute()
        
        # Check if data is available
        if not hasattr(response, 'data') or response.data is None:
            return None, "Server Error: Data not available"
        
        # Check if any rows were updated
        if len(response.data) == 0:
            return None, f"Column '{column_name}' not found in table '{table_id}'"
        
        return response.data[0], None
        
    except Exception as e:
        error_msg = f"Error updating column by name: {str(e)}"
        print(error_msg)
        return None, error_msg


def update_column_by_id(
    config: Annotated[RunnableConfig, InjectedToolArg],
    column_id: int,
    fields: Dict[str, Any]
) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
    """Update multiple fields of a column by column ID.
    
    Args:
        column_id (int): ID of the column to update
        fields (Dict[str, Any]): Dictionary of field names and their new values
                                 (e.g., {'description': 'new desc', 'settings': {...}})
        
    Returns:
        Tuple[Optional[Dict[str, Any]], Optional[str]]: Tuple containing (updated_data, error)
        where updated_data is the updated column data if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        # Extract table_id from config using Configuration class
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        
        # Update the column by ID and table_id (for security) with multiple fields
        response = supabase.table('columns').update(fields).eq('table_id', table_id).eq('id', column_id).execute()
        
        # Check if data is available
        if not hasattr(response, 'data') or response.data is None:
            return None, "Server Error: Data not available"
        
        # Check if any rows were updated
        if len(response.data) == 0:
            return None, f"Column with ID '{column_id}' not found in table '{table_id}'"
        
        return response.data[0], None
        
    except Exception as e:
        error_msg = f"Error updating column by ID: {str(e)}"
        print(error_msg)
        return None, error_msg


def update_columns_batch(
    config: Annotated[RunnableConfig, InjectedToolArg],
    updates: List[Dict[str, Any]]
) -> Tuple[Optional[List[Dict[str, Any]]], Optional[str]]:
    """Update multiple columns in batch.
    
    Args:
        updates (List[Dict[str, Any]]): List of update operations, each containing:
            - 'column_id' or 'column_name': identifier for the column
            - 'fields': dictionary of field names and their new values
        
    Returns:
        Tuple[Optional[List[Dict[str, Any]]], Optional[str]]: Tuple containing (updated_data_list, error)
        where updated_data_list is a list of updated column data if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        updated_columns = []
        
        for update in updates:
            if 'column_id' in update:
                # Update by ID
                result, error = update_column_by_id(
                    config=config,
                    column_id=update['column_id'],
                    fields=update['fields']
                )
            elif 'column_name' in update:
                # Update by name
                result, error = update_column_by_name(
                    config=config,
                    column_name=update['column_name'],
                    fields=update['fields']
                )
            else:
                return None, "Each update must contain either 'column_id' or 'column_name'"
            
            if error:
                return None, f"Batch update failed: {error}"
            
            updated_columns.append(result)
        
        return updated_columns, None
        
    except Exception as e:
        error_msg = f"Error in batch column update: {str(e)}"
        print(error_msg)
        return None, error_msg



