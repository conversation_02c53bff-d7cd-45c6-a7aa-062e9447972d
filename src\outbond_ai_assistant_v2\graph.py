from typing import Annotated
from langchain_core.tools import tool
from langgraph.prebuilt import create_react_agent
from langgraph.graph import StateGraph, START, END
from src.outbond_ai_assistant_v2.state import AgentState
from langchain_core.messages import AIMessage, ToolMessage
from src.outbond_ai_assistant_v2.utils import get_supervisor_model, get_sub_agent_model
from src.outbond_ai_assistant_v2.prompts import SUPERVISOR_PROMPT, SEARCH_AGENT_PROMPT
from src.outbond_ai_assistant_v2.tools.web_search_tools.web_tools import SEARCH_TOOLS
from src.outbond_ai_assistant_v2.tools.smart_column_tools.linkedIn.linkedin_profile_column import create_and_edit_linkedin_profile_column
from src.outbond_ai_assistant_v2.configuration import Configuration
from src.outbond_ai_assistant_v2.supabase.table.get_table_metadata import get_table_metadata_by_id
from src.outbond_ai_assistant_v2.tools.read_table_tools.read_table import read_table_data_tool
from src.outbond_ai_assistant_v2.supabase.table.get_columns import get_columns_by_table_id
from src.outbond_ai_assistant_v2.supabase.table.update_columns import update_column_by_name
from langgraph_supervisor import create_supervisor



# Create search agent
search_agent = create_react_agent(
    model=get_sub_agent_model(),
    tools=SEARCH_TOOLS,
    prompt=(
        SEARCH_AGENT_PROMPT + "\n\n"
        "INSTRUCTIONS:\n"
        "- You have access to the full conversation history\n"
        "- Use your web search tools to gather relevant information based on the user's request\n"
        "- Provide comprehensive, well-researched answers based on your search findings\n"
        "- Focus on answering the user's question or completing the requested task\n"
        "- Include current and accurate information from your searches\n"
        "- Return your findings directly to continue the conversation"
    ),
    name="search_agent",
)

# Create LinkedIn profiles agent
linkedin_profiles_agent = create_react_agent(
    model=get_sub_agent_model(),
    tools=[create_and_edit_linkedin_profile_column],
    prompt=(
        "You are a specialized LinkedIn Profiles Agent focused on creating and managing LinkedIn profile columns in user tables.\n\n"
        "INSTRUCTIONS:\n"
        "- You have access to the full conversation history\n"
        "- Use the create_and_edit_linkedin_profile_column tool to create or edit LinkedIn profile columns\n"
        "- When users request LinkedIn profile column creation or editing, gather the necessary information:\n"
        "  * Column name for the LinkedIn profile data\n"
        "  * LinkedIn profile URL injection sequence (how to get the LinkedIn URL from existing data)\n"
        "  * Whether this is a new column creation or updating an existing column\n"
        "- Validate that the injection sequence makes sense for extracting LinkedIn profile URLs\n"
        "- Provide clear feedback on the success or failure of column operations\n"
        "- Help users understand how LinkedIn profile columns work and what data they will contain\n"
        "- Focus specifically on LinkedIn profile column management tasks\n"
        "- Return detailed results about the column creation or update process"
    ),
    name="linkedin_profiles_agent",
)


# Create supervisor agent
supervisor_agent_base = create_supervisor(
    model=get_supervisor_model(),
    agents=[search_agent, linkedin_profiles_agent],
    prompt=(
        SUPERVISOR_PROMPT + "\n\n"
        "INSTRUCTIONS:\n"
        "- You are managing specialized sub-agents for different tasks\n"
        "- Currently available agents:\n"
        "  * Search agent: For research, web search, and information retrieval\n"
        "  * LinkedIn profiles agent: For creating and editing LinkedIn profile columns in tables\n"
        "- When a user asks a question that requires specific expertise, use the appropriate transfer tool\n"
        "- After calling a transfer tool, do NOT provide additional responses - let the specialist handle it\n"
        "- Available tools:\n"
        "  * transfer_to_search_agent: For research, web search, and information retrieval tasks\n"
        "  * transfer_to_linkedin_profiles: For LinkedIn profile column creation, editing, and management\n"
        "- Do not do specialized work yourself, delegate to the appropriate agent"
    )
).compile()

def table_indexing(state, config=None):
    """Table indexing node that runs at the beginning to populate table index information."""
    try:
        # Validate config first
        if config is None:
            return {
                "table_index_supervisor": "Error: Configuration not available",
                "table_index_sub_agent": "Error: Configuration not available"
            }

        # Get configuration and table_id
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id

        # Get all columns for this table
        all_columns, columns_error = get_columns_by_table_id(config)
        if columns_error or not all_columns:
            error_msg = columns_error or "No columns found for table"
            return {
                "table_index_supervisor": f"Error fetching columns: {error_msg}",
                "table_index_sub_agent": f"Columns unavailable due to error: {error_msg}"
            }

        # Check if any column has agent_description data (check if we need to fetch new data)
        has_cached_data = any(
            col.get('agent_description') and 
            col['agent_description'] != '' and 
            col['agent_description'] is not None 
            for col in all_columns
        )

        if has_cached_data:
            # Construct response from cached agent_description data
            print("Using cached agent_description data from database")
            
            supervisor_columns = []
            reconstructed_columns = []
            total_rows = 0
            
            for column in all_columns:
                agent_desc = column.get('agent_description')
                if agent_desc and isinstance(agent_desc, dict):
                    # Extract cached data
                    cached_column = agent_desc.get('column', {})
                    total_rows = agent_desc.get('total_rows', 0)
                    
                    # Add to supervisor state (limited info)
                    supervisor_columns.append({
                        "column_data_summary": cached_column.get("column_data_summary", "No summary available"),
                        **({"is_runnable": cached_column.get("is_runnable")} if cached_column.get("is_runnable") is not None else {}),
                        **({"column_run_status": cached_column.get("column_run_status")} if cached_column.get("column_run_status") is not None else {})
                    })
                    
                    # Add to sub-agent state (full data)
                    reconstructed_columns.append(cached_column)
            
            supervisor_index = {
                "columns": supervisor_columns,
                "total_rows": total_rows,
                "success": True,
                "error_message": None
            }
            
            sub_agent_index = {
                "columns": reconstructed_columns,
                "total_rows": total_rows,
                "success": True,
                "error_message": None
            }
            
            return {
                "table_index_supervisor": supervisor_index,
                "table_index_sub_agent": sub_agent_index,
            }

        # If no cached data, fetch fresh data from API
        print("No cached data found, fetching fresh data from API")
        
        # Fetch table metadata using the new function
        table_metadata, error = get_table_metadata_by_id(table_id)

        if error or table_metadata is None:
            # If there's an error, return error information in the state
            error_msg = error or "Unknown error: table metadata is None"
            return {
                "table_index_supervisor": f"Error fetching table metadata: {error_msg}",
                "table_index_sub_agent": f"Table metadata unavailable due to error: {error_msg}"
            }
        
        # If successful, return the table metadata in the supervisor state
        table_filters = table_metadata.get('filters', None)
        table_sorts = table_metadata.get('sorts', None)
        
        # Transform column IDs to column names in both filters and sorts using all_columns data
        if all_columns:
            # Create mapping from column_id to column_name using the fetched data
            id_to_name = {col['id']: col['name'] for col in all_columns}
            
            # Transform filters: replace column_id with column_name
            if table_filters and table_filters.get('rules'):
                for rule in table_filters['rules']:
                    if 'column_id' in rule:
                        column_id = rule['column_id']
                        if column_id in id_to_name:
                            rule['column_name'] = id_to_name[column_id]
                            del rule['column_id']  # Remove the old field
            
            # Transform sorts: replace id with column_name and desc with direction
            if table_sorts and isinstance(table_sorts, list):
                for sort in table_sorts:
                    if 'id' in sort:
                        column_id = sort['id']
                        if column_id in id_to_name:
                            sort['column_name'] = id_to_name[column_id]
                            del sort['id']  # Remove the old field
                    
                    # Transform desc (boolean) to direction (string)
                    if 'desc' in sort:
                        sort['direction'] = 'desc' if sort['desc'] else 'asc'
                        del sort['desc']  # Remove the old field
        
        # Call read_table tool with the fetched filters and sorts
        if config is not None:
            table_data_response = read_table_data_tool.invoke({
                "max_rows": 10,
                "filters": None,
                "sorts": None
            }, config=config)
            
            if table_data_response and table_data_response.success:
                # Extract data for supervisor: only column_data_summary and is_runnable
                supervisor_columns = []
                for column in table_data_response.columns:
                    supervisor_columns.append({
                        "column_data_summary": column.get("column_data_summary", "No summary available"),
                        **({"is_runnable": column.get("is_runnable")} if column.get("is_runnable") is not None else {}),
                        **({"column_run_status": column.get("column_run_status")} if column.get("column_run_status") is not None else {})
                    })
                
                supervisor_index = {
                    "columns": supervisor_columns,
                    "total_rows": table_data_response.total_rows,
                    "success": table_data_response.success,
                    "error_message": table_data_response.error_message
                }
                
                # Sub-agent gets the full JSON
                sub_agent_index = {
                    "columns": table_data_response.columns,
                    "total_rows": table_data_response.total_rows,
                    "success": table_data_response.success,
                    "error_message": table_data_response.error_message
                }
                
                # Save the response data in the database for each column
                try:
                    # Helper function to extract column name from column object (same logic as in summerize.py)
                    def get_column_name_from_column_obj(column_obj):
                        metadata_fields = {"is_runnable", "column_run_status", "column_data_summary"}
                        for key in column_obj.keys():
                            if key not in metadata_fields:
                                return key
                        return None
                    
                    for column in table_data_response.columns:
                        # Extract column name from the column object structure
                        column_name = get_column_name_from_column_obj(column)
                        
                        if column_name:
                            # Create JSON data to store in agent_description
                            agent_description_data = {
                                "column": column,
                                "total_rows": table_data_response.total_rows
                            }
                            
                            # Update the column's agent_description field using column name
                            update_result, update_error = update_column_by_name(
                                config=config,
                                column_name=column_name,
                                fields={"agent_description": agent_description_data}
                            )
                            
                            if update_error:
                                print(f"Warning: Failed to update agent_description for column '{column_name}': {update_error}")
                        else:
                            print(f"Warning: Could not extract column name from column object, skipping agent_description update")
                            
                except Exception as e:
                    print(f"Warning: Error updating agent_description in database: {str(e)}")
                    # Don't fail the whole function if database update fails
                
            else:
                supervisor_index = {"error": "Failed to fetch table data"}
                sub_agent_index = {"error": "Failed to fetch table data"}
        else:
            supervisor_index = {"error": "Configuration not available"}
            sub_agent_index = {"error": "Configuration not available"}

        return {
            "table_index_supervisor": supervisor_index,
            "table_index_sub_agent": sub_agent_index,
        }
        
    except Exception as e:
        error_msg = f"Unexpected error in table indexing: {str(e)}"
        return {
            "table_index_supervisor": error_msg,
            "table_index_sub_agent": error_msg
        }


def filter_empty_messages(messages):
    """Filter out empty messages from a list of messages."""
    filtered_messages = []
    
    for message in messages:
        # Check if message has meaningful content
        has_content = False
        
        if message.content:
            if isinstance(message.content, str):
                has_content = message.content.strip() != ""
            elif isinstance(message.content, list):
                has_content = len(message.content) > 0
            else:
                has_content = True  # For other content types, keep them
        
        # Keep messages that have content or tool calls
        if has_content or (hasattr(message, 'tool_calls') and message.tool_calls):
            filtered_messages.append(message)
        # Keep ToolMessages (responses to tool calls)
        elif isinstance(message, ToolMessage):
            filtered_messages.append(message)
    
    return filtered_messages


def supervisor_agent(state):
    """Supervisor agent wrapper that includes message filtering."""
    # Run the supervisor agent
    response = supervisor_agent_base.invoke(state)
    
    # Filter empty messages before returning
    filtered_messages = filter_empty_messages(response["messages"])
    
    return {"messages": filtered_messages}


def should_continue_supervisor(state):
    """Check if the supervisor should continue or end based on the last message."""
    messages = state.get("messages", [])
    if not messages:
        return END
    
    last_message = messages[-1]
    
    # If the last message is from the supervisor and doesn't contain tool calls,
    # it likely means the conversation is complete
    if (hasattr(last_message, 'name') and 
        last_message.name == 'supervisor' and 
        not (hasattr(last_message, 'tool_calls') and last_message.tool_calls)):
        return END
    
    # If there are pending tool calls or the conversation seems ongoing, continue
    return "supervisor"


def create_graph():
    """Create and return the multi-agent supervisor graph with prebuilt supervisor handling routing."""
    graph = (
        StateGraph(AgentState, config_schema=Configuration)
        .add_node("table_indexing", table_indexing)
        .add_node("supervisor", supervisor_agent)
        .add_edge(START, "table_indexing")
        .add_edge("table_indexing", "supervisor")
        .add_conditional_edges(
            "supervisor",
            should_continue_supervisor,
            {
                "supervisor": "supervisor",  # Continue with supervisor
                END: END                     # End the conversation
            }
        )
        .compile()
    )
    
    graph.name = "Supervisor Agent"
    return graph


# Create the graph instance
graph = create_graph()
