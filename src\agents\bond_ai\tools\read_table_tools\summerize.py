"""Column data schema extraction and summarization utilities.

This module provides robust, production-ready functions for analyzing table column data
structures and generating AI-powered summaries. It processes column data from database
responses while protecting sensitive information and providing meaningful insights.

Key features:
    - Schema extraction with recursive data structure analysis
    - AI-powered column summarization via LangSmith integration
    - Secure handling of sensitive data (excludes run_status fields)
    - Comprehensive error handling and logging
    - Type-safe operations with full type annotations

Typical usage:
    # Extract schema only
    schema = extract_column_schema(columns_data)
    
    # Get schema with AI summaries
    enhanced_schema = extract_column_schema_with_summary(columns_data)
"""

import json
import logging
import os
from typing import Any, Dict, List, Optional, Union

from langsmith import Client

from src.core.config import get_settings
from langchain_core.runnables import RunnableConfig
from src.agents.bond_ai.supabase.validate_injection_sequance.models import (
    ValidateInjectionSequenceRequest,
)
from src.agents.bond_ai.supabase.validate_injection_sequance.validate_injection_sequance import (
    validate_injection_sequence,
)


# Constants
_SCHEMA_PLACEHOLDER = "..."
_METADATA_FIELDS = frozenset(["is_runnable", "column_run_status"])
_EXCLUDED_FIELDS = frozenset(["run_status", "column_id"])
_LANGSMITH_PROMPT_NAME = "summerize_table"
_JSON_INDENT = 2
_SUMMARY_UNAVAILABLE_MSG = "Summary not available"
_MAX_RETRY_ATTEMPTS = 3

# Configure module logger
logger = logging.getLogger(__name__)


class SchemaExtractionError(Exception):
    """Raised when schema extraction fails due to invalid input or processing errors."""
    pass


class LLMSummarizationError(Exception):
    """Raised when LLM summarization fails due to API or processing errors."""
    pass


def _extract_schema_recursive(data: Any) -> Any:
    """Recursively extract schema from nested data structures.
    
    This function traverses complex nested data structures and replaces actual
    values with placeholder strings while preserving the overall structure.
    
    Args:
        data: The data structure to extract schema from. Can be dict, list, or primitive.
        
    Returns:
        Schema representation with placeholders for values but preserving structure.
        
    Examples:
        >>> _extract_schema_recursive({"name": "John", "age": 30})
        {"name": "...", "age": "..."}
        
        >>> _extract_schema_recursive([{"id": 1}, {"id": 2}])
        [{"id": "..."}]
    """
    if isinstance(data, dict):
        return {_normalize_schema_key(key): _extract_schema_recursive(value) for key, value in data.items()}
    elif isinstance(data, list):
        # Extract schema from first item to represent the list structure
        return [_extract_schema_recursive(data[0])] if data else []
    else:
        # For primitive values, return placeholder
        return _SCHEMA_PLACEHOLDER


def _get_column_name_and_data(column_obj: Dict[str, Any]) -> tuple[Optional[str], Optional[Any]]:
    """Extract column name and data from column object.
    
    Args:
        column_obj: Column object containing metadata and data.
        
    Returns:
        Tuple of (column_name, column_data) or (None, None) if not found.
    """
    for key, value in column_obj.items():
        if key not in _METADATA_FIELDS:
            return key, value
    return None, None


def _process_metadata_fields(column_obj: Dict[str, Any]) -> Dict[str, Any]:
    """Process and copy metadata fields from column object.
    
    Args:
        column_obj: Source column object.
        
    Returns:
        Dictionary containing relevant metadata fields.
    """
    metadata = {}
    
    if "is_runnable" in column_obj:
        metadata["is_runnable"] = column_obj["is_runnable"]
        # Only include column_run_status if is_runnable is True
        if column_obj["is_runnable"] and "column_run_status" in column_obj:
            metadata["column_run_status"] = column_obj["column_run_status"]
    
    return metadata


def _normalize_schema_key(name: Any) -> Any:
    """Normalize keys to lowercase with spaces replaced by underscores.
    
    Args:
        name: Key to normalize.
        
    Returns:
        Normalized key if input is a string, otherwise returns input unchanged.
    """
    if isinstance(name, str):
        return name.strip().lower().replace(" ", "_")
    return name


def _extract_item_schema(first_item: Dict[str, Any]) -> Any:
    """Extract schema from the first item in column data.
    
    This flattens legacy wrappers by:
    - Removing "cell_details" wrapper and lifting its contents directly
      under the column key
    - Removing "cell_value" wrapper and returning a direct placeholder value
    
    Args:
        first_item: First data item to analyze for schema.
        
    Returns:
        Schema representation of the item structure with wrappers removed.
    """
    if not isinstance(first_item, dict):
        return _SCHEMA_PLACEHOLDER

    # Remove excluded fields like run_status
    cleaned_item = {
        item_key: item_value
        for item_key, item_value in first_item.items()
        if item_key not in _EXCLUDED_FIELDS
    }

    # Prefer detailed structures when available
    if "cell_details" in cleaned_item:
        # Lift details directly under the column key
        return _extract_schema_recursive(cleaned_item["cell_details"])

    # If only a primitive value is present, return placeholder directly
    if "cell_value" in cleaned_item:
        return _SCHEMA_PLACEHOLDER

    # Lift generic "value" wrapper when present
    if "value" in cleaned_item:
        wrapped_value = cleaned_item["value"]
        if isinstance(wrapped_value, dict):
            return _extract_schema_recursive(wrapped_value)
        else:
            return _SCHEMA_PLACEHOLDER

    # Fallback: extract schema for remaining keys
    return {
        _normalize_schema_key(item_key): _extract_schema_recursive(item_value)
        for item_key, item_value in cleaned_item.items()
    }


def _generate_column_schema(column_data: Any) -> Any:
    """Generate schema for individual column data.
    
    This scans multiple rows (if available) to find a representative structure.
    Preference order:
      1) Any row that contains "cell_details" (rich nested structure)
      2) The first dict row (even if it only has "cell_value")
      3) Fallback to a simple value placeholder
    
    Args:
        column_data: The data to generate schema for.
        
    Returns:
        Schema representation of the column data.
    """
    if isinstance(column_data, list) and column_data:
        fallback_item: Optional[Dict[str, Any]] = None
        for item in column_data:
            if not isinstance(item, dict):
                # Non-dict sample -> treat as primitive
                if fallback_item is None:
                    fallback_item = None
                continue
            # Remove excluded fields like run_status
            cleaned_item = {
                item_key: item_value
                for item_key, item_value in item.items()
                if item_key not in _EXCLUDED_FIELDS
            }
            # Prefer rows that have detailed structures
            if "cell_details" in cleaned_item:
                return _extract_schema_recursive(cleaned_item["cell_details"])
            # Keep the first dict row as a fallback
            if fallback_item is None:
                fallback_item = cleaned_item
        if fallback_item is not None:
            return _extract_item_schema(fallback_item)
        return _SCHEMA_PLACEHOLDER
    else:
        return _SCHEMA_PLACEHOLDER


def extract_column_schema(
    columns: List[Dict[str, Any]],
    config: Optional[RunnableConfig] = None,
) -> List[Dict[str, Any]]:
    """Extract structural schema from columns data without exposing actual values.
    
    This function analyzes column data structure and creates a schema showing
    data types and structure while protecting sensitive information. It excludes
    run_status fields and provides deep schema analysis for nested structures.
    
    Args:
        columns: List of column objects from database response. Each object contains
                metadata fields and actual column data.
        config: Optional runnable config, used to fetch up to 5 non-empty values
                via validate_injection_sequence for more reliable schema extraction.
                If not provided, falls back to in-memory data.
                
    Returns:
        List of column objects with schema representations instead of actual data.
        Preserves metadata fields while replacing data with structural schema.
        
    Raises:
        SchemaExtractionError: If schema extraction fails due to invalid input.
        
    Examples:
        >>> columns = [{"Test Column": [{"cell_value": "data"}], "is_runnable": False}]
        >>> extract_column_schema(columns)
        [{"Test Column": {"cell_value": "..."}, "is_runnable": False}]
    """
    if not isinstance(columns, list):
        raise SchemaExtractionError("Columns must be a list")
    
    try:
        schema_columns = []
        
        for column_obj in columns:
            if not isinstance(column_obj, dict):
                logger.warning("Skipping non-dict column object: %s", type(column_obj))
                continue
                
            # Start with metadata fields
            schema_column = _process_metadata_fields(column_obj)
            
            # Extract column name and data
            column_name, column_data = _get_column_name_and_data(column_obj)

            # Prefer non-empty values via validation RPC when available
            if config is not None and column_name:
                normalized_column_name = _normalize_schema_key(column_name)
                injection_sequence = f"$${normalized_column_name}$$"
                try:
                    validation_response = validate_injection_sequence(
                        config=config,
                        request=ValidateInjectionSequenceRequest(
                            injection_sequence=injection_sequence
                        ),
                    )
                    if getattr(validation_response, "success", False) and getattr(
                        validation_response, "values", None
                    ):
                        column_data = validation_response.values
                except Exception as validation_error:
                    logger.debug(
                        "Validation fetch failed for column %s: %s",
                        column_name,
                        str(validation_error),
                    )
            
            if column_name and column_data is not None:
                normalized_column_name = _normalize_schema_key(column_name)
                schema_column[normalized_column_name] = _generate_column_schema(column_data)
            
            schema_columns.append(schema_column)
        
        logger.info("Successfully extracted schema for %d columns", len(schema_columns))
        return schema_columns
        
    except Exception as e:
        logger.error("Failed to extract column schema: %s", str(e))
        raise SchemaExtractionError(f"Schema extraction failed: {str(e)}") from e


def _extract_cell_data(cell: Union[Dict[str, Any], Any]) -> Optional[Dict[str, Any]]:
    """Extract data from individual cell, excluding sensitive fields.
    
    Args:
        cell: Cell data to process.
        
    Returns:
        Cleaned cell data or None if no valid data.
    """
    if isinstance(cell, dict):
        cell_data = {
            cell_key: cell_value
            for cell_key, cell_value in cell.items()
            if cell_key not in _EXCLUDED_FIELDS
        }
        return cell_data if cell_data else None
    else:
        return cell


def extract_column_data_for_llm(columns: List[Dict[str, Any]]) -> Dict[str, List[Any]]:
    """Extract actual column data for LLM summarization.
    
    Extracts cell_details and cell_value data from each column while excluding
    sensitive run_status information. This data is used for AI-powered summarization.
    
    Args:
        columns: List of column objects from database response.
        
    Returns:
        Dictionary mapping column names to their cleaned data for summarization.
        Only includes columns that have actual data after filtering.
        
    Examples:
        >>> columns = [{"User Name": [{"cell_value": "John", "run_status": "done"}]}]
        >>> extract_column_data_for_llm(columns)
        {"User Name": [{"cell_value": "John"}]}
    """
    column_data = {}
    
    for column_obj in columns:
        if not isinstance(column_obj, dict):
            continue
            
        column_name, column_values = _get_column_name_and_data(column_obj)
        
        if not column_name or not column_values:
            continue
            
        extracted_data = []
        
        if isinstance(column_values, list):
            for cell in column_values:
                cleaned_cell = _extract_cell_data(cell)
                if cleaned_cell is not None:
                    extracted_data.append(cleaned_cell)
        
        if extracted_data:
            column_data[column_name] = extracted_data
    
    logger.debug("Extracted data for %d columns for LLM processing", len(column_data))
    return column_data


def _get_langsmith_client() -> Client:
    """Get configured LangSmith client.
    
    Returns:
        Configured LangSmith client.
        
    Raises:
        LLMSummarizationError: If LangSmith API key is not available.
    """
    settings = get_settings()
    
    # Set OpenAI API key if available
    if settings.OPENAI_API_KEY:
        os.environ["OPENAI_API_KEY"] = settings.OPENAI_API_KEY.get_secret_value()
    
    # Get LangSmith API key
    if not settings.LANGSMITH_API_KEY:
        raise LLMSummarizationError("LANGSMITH_API_KEY not found in settings")
    
    api_key = settings.LANGSMITH_API_KEY.get_secret_value()
    return Client(api_key=api_key)


def _validate_llm_response(response: Any) -> Dict[str, List[Dict[str, str]]]:
    """Validate and extract data from LLM response.
    
    Args:
        response: Response from LangSmith model.
        
    Returns:
        Validated response data.
        
    Raises:
        LLMSummarizationError: If response format is invalid.
    """
    if not isinstance(response, dict) or "columns" not in response:
        raise LLMSummarizationError(
            f"Invalid response format from LangSmith: {type(response)}. "
            f"Expected dict with 'columns' key. Response: {response}"
        )
    
    if not isinstance(response["columns"], list):
        raise LLMSummarizationError(
            f"Invalid 'columns' format: expected list, got {type(response['columns'])}"
        )
    
    return response


def summarize_columns_with_llm(columns: List[Dict[str, Any]]) -> Dict[str, str]:
    """Generate AI-powered summaries for column data using LangSmith.
    
    This function processes column data through a LangSmith-hosted LLM prompt
    to generate meaningful, business-context summaries for each column.
    
    Args:
        columns: List of column objects from database response.
        
    Returns:
        Dictionary mapping column names to their AI-generated summaries.
        Returns empty dict if summarization fails.
        
    Raises:
        LLMSummarizationError: If critical errors occur during summarization.
        
    Examples:
        >>> columns = [{"Email": [{"cell_value": "<EMAIL>"}]}]
        >>> summarize_columns_with_llm(columns)
        {"Email": "Contains email addresses for user contacts..."}
    """
    if not isinstance(columns, list):
        raise LLMSummarizationError("Columns must be a list")
    
    try:
        # Extract data for LLM processing
        column_data = extract_column_data_for_llm(columns)
        
        if not column_data:
            logger.warning("No column data available for summarization")
            return {}
        
        # Get LangSmith client and model
        client = _get_langsmith_client()
        model = client.pull_prompt(_LANGSMITH_PROMPT_NAME, include_model=True)
        
        # Prepare data for LLM
        data_str = json.dumps(column_data, indent=_JSON_INDENT, default=str)
        
        #print(f"[Debug] Data str: {data_str}")
        
        logger.info("Sending %d columns to LLM for summarization", len(column_data))
        
        # Get structured response from LangSmith
        logger.debug("Invoking LangSmith model for column summarization")
        response = model.invoke({"columns_data": data_str})
        
        # Validate and process response
        validated_response = _validate_llm_response(response)
        
        # Convert response to expected format
        summaries = {}
        for item in validated_response["columns"]:
            if (isinstance(item, dict) and 
                "column_name" in item and 
                "summary" in item and
                isinstance(item["column_name"], str) and
                isinstance(item["summary"], str)):
                summaries[item["column_name"]] = item["summary"]
            else:
                logger.warning("Skipping invalid summary item: %s", item)
        
        logger.info("Successfully generated summaries for %d/%d columns", 
                   len(summaries), len(column_data))
        return summaries
        
    except Exception as e:
        logger.error("Error during LLM summarization: %s", str(e))
        # For production, we return empty dict instead of raising to ensure service stability
        return {}


def extract_column_schema_with_summary(
    columns: List[Dict[str, Any]],
    config: Optional[RunnableConfig] = None,
) -> List[Dict[str, Any]]:
    """Extract column schema and enhance with AI-generated summaries.
    
    This function combines structural schema extraction with AI-powered content
    summarization to provide comprehensive column analysis. It's the primary
    function for getting enhanced column information.
    
    Args:
        columns: List of column objects from database response.
        config: Optional runnable config to be passed to schema extraction
                for reliable non-empty value sampling.
        
    Returns:
        List of column objects enhanced with both schema information and
        AI-generated summaries. Each column includes a 'column_data_summary' field.
        Falls back to basic schema without summaries if AI summarization fails.
        
    Examples:
        >>> columns = [{"Name": [{"cell_value": "John Doe"}], "is_runnable": False}]
        >>> result = extract_column_schema_with_summary(columns)
        >>> result[0]["column_data_summary"]
        "Contains personal names and contact information..."
    """
    if not isinstance(columns, list):
        logger.error("Invalid input: columns must be a list, got %s", type(columns))
        return []
    
    try:
        # Extract structural schema
        schema_columns = extract_column_schema(columns, config=config)
        
        # Generate AI summaries (non-blocking - continue with basic schema if fails)
        try:
            summaries = summarize_columns_with_llm(columns)
        except Exception as e:
            logger.warning("Failed to generate AI summaries, continuing with basic schema: %s", str(e))
            summaries = {}
        
        # Build mapping from normalized column names to original names for summary lookup
        norm_to_orig: Dict[str, str] = {}
        for column_obj in columns:
            orig_name, _ = _get_column_name_and_data(column_obj)
            if orig_name:
                norm_to_orig[_normalize_schema_key(orig_name)] = orig_name

        # Enhance schema with summaries
        for schema_column in schema_columns:
            column_name, _ = _get_column_name_and_data(schema_column)
            
            # Map normalized name back to original for summaries
            original_name = norm_to_orig.get(column_name or "", column_name)
            if original_name and original_name in summaries:
                schema_column["column_data_summary"] = summaries[original_name]
            else:
                schema_column["column_data_summary"] = _SUMMARY_UNAVAILABLE_MSG
        
        logger.info("Successfully created enhanced schema for %d columns", len(schema_columns))
        return schema_columns
        
    except SchemaExtractionError as e:
        logger.error("Schema extraction failed: %s", str(e))
        return []
    except Exception as e:
        logger.error("Unexpected error in extract_column_schema_with_summary: %s", str(e))
        return []
