from enum import Enum
from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, Union, List, Dict, Any


class NotifyFEType(str, Enum):
    """Type of notification to send to FE."""
    TABLE_UPDATE = "TABLE_UPDATE"
    """Table update"""

    TABLE_CREATE = "TABLE_CREATE"
    """Table create"""

    TABLE_DELETE = "TABLE_DELETE"
    """Table delete"""

    ROW_UPDATE = "ROW_UPDATE"
    """Row update"""

    ROW_CREATE = "ROW_CREATE"
    """Row create"""

    ROW_DELETE = "ROW_DELETE"
    """Row delete"""

    COLUMN_UPDATE = "COLUMN_UPDATE"
    """Column update"""

    COLUMN_CREATE = "COLUMN_CREATE"
    """Column create"""

    COLUMN_DELETE = "COLUMN_DELETE"
    """Column delete"""

    CELL_UPDATE = "CELL_UPDATE"
    """Cell update"""




class NotifyFERequest(BaseModel):
    """Request model for notifying Frontend about changes in the table.
    
    This model defines the structure for notifying FE.
    """
    model_config = ConfigDict(use_enum_values=True)
    
    channel: str = Field(
        ...,
        description="The unique identifier of the channel to send the notification to, it is the table id",
        min_length=1,
        examples=["tbl_c85117eb4106d464"]
    )
    
    type: NotifyFEType = Field(
        ...,
        description="Type of notification to send to FE",
        examples=[NotifyFEType.TABLE_UPDATE, NotifyFEType.TABLE_CREATE, NotifyFEType.TABLE_DELETE, NotifyFEType.ROW_UPDATE, NotifyFEType.ROW_CREATE, NotifyFEType.ROW_DELETE, NotifyFEType.COLUMN_UPDATE, NotifyFEType.COLUMN_CREATE, NotifyFEType.COLUMN_DELETE, NotifyFEType.CELL_UPDATE]
    )
    
    payload: Union[Dict[str, Any], List[Dict[str, Any]]] = Field(
        ...,
        description="Data to send to FE",
        examples=[{"id": "tbl_c85117eb4106d464", "name": "Table 1", "filters": {} }]
    )



class NotifyFEResponse(BaseModel):
    """Response model for notifying Frontend about changes in the table."""
    model_config = ConfigDict(use_enum_values=True)
    
    success: bool = Field(
        default=True,
        description="Indicates if the notification was sent successfully"
    )
    
    error_message: Optional[str] = Field(
        default=None,
        description="Error message when creation fails"
    )
    



