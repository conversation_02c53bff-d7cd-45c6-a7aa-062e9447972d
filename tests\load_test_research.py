"""
Load test script for the formula endpoint.
Submits 100 concurrent AI formula requests to test system performance.
"""

import os
import asyncio
import httpx
import time
import uuid
from dotenv import load_dotenv
import random

# Load environment variables
load_dotenv()

# Base URL for the API
BASE_URL = "http://localhost:2024"

# Get the API token from environment variables or set it manually
API_TOKEN = os.getenv("SUPABASE_SERVICE_ROLE_KEY", "your-api-token")

# Headers for the requests
HEADERS = {"Authorization": f"Bearer {API_TOKEN}", "Content-Type": "application/json"}

query = """Retrun for me the LinkedIn profile URL of the decision maker of phyros.io
    Make sure it is the decision maker, not any other employee. Make sure it is a person, not a company.
      Don't add any other text. Just the URL."""

# Template for formula request
FORMULA_REQUEST_TEMPLATE = {
    "organization_id": "org_64bd7f51c1450d62",
    "table_id": "tbl_3655e99e74169376",
    "column_id": 7,
    "service_id": 21,  # Formula service ID
    "credits": 10,
    "formula": None,
    "value": {
        "user_prompt": query,
    },
}

# Investment scenarios to make each request slightly different
INVESTMENT_SCENARIOS = [
    "Initial investment of $10,000 with 5% annual return over 3 years",
    "Real estate investment of $250,000 with 8% appreciation over 5 years",
    "Stock portfolio with $50,000 initial investment and 10% annual growth",
    "Startup investment of $100,000 with 20% potential return but high risk",
    "Bond investment of $30,000 with 3% fixed return over 10 years",
    "Cryptocurrency investment of $5,000 with volatile returns",
    "Mutual fund investment of $25,000 with 7% average annual return",
    "Rental property generating $2,000 monthly with 3% annual appreciation",
    "Index fund investment of $40,000 with 6% expected annual return",
    "Small business investment of $150,000 with 15% projected annual profit",
]


async def send_formula_request(session, request_id):
    """Send a single formula request."""
    # Create a unique request by varying the row_id and investment scenario
    request_data = FORMULA_REQUEST_TEMPLATE.copy()
    request_data["row_id"] = request_id
    request_data["run_id"] = request_id

    # Select a random investment scenario

    start_time = time.time()
    try:
        response = await session.post(
            f"{BASE_URL}/run-cell",
            json=request_data,
            headers=HEADERS,
            timeout=30.0,
        )

        elapsed = time.time() - start_time
        status = response.status_code

        if status == 200:
            result = "✅"
        else:
            result = "❌"

        print(
            f"Request {request_id:03d}: {result} Status: {status} Time: {elapsed:.2f}s"
        )
        return {
            "request_id": request_id,
            "status_code": status,
            "elapsed_time": elapsed,
            "success": status == 200,
            "response": response.json() if status == 200 else None,
        }
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"Request {request_id:03d}: ❌ Error: {str(e)} Time: {elapsed:.2f}s")
        return {
            "request_id": request_id,
            "status_code": 0,
            "elapsed_time": elapsed,
            "success": False,
            "error": str(e),
        }


async def run_load_test(num_requests=100, concurrency=100):
    """Run a load test with the specified number of requests and concurrency."""
    print(
        f"\n=== Starting Load Test: {num_requests} requests with concurrency {concurrency} ===\n"
    )

    start_time = time.time()

    # Create a client session for all requests
    async with httpx.AsyncClient() as session:
        # Create a list of tasks
        tasks = []
        for i in range(1, num_requests + 1):
            tasks.append(send_formula_request(session, i))

        # Run tasks with limited concurrency
        # We use asyncio.Semaphore to limit the number of concurrent requests
        semaphore = asyncio.Semaphore(concurrency)

        async def bounded_fetch(coro):
            async with semaphore:
                return await coro

        # Execute all requests with the semaphore to limit concurrency
        results = await asyncio.gather(*(bounded_fetch(task) for task in tasks))

    # Calculate statistics
    total_time = time.time() - start_time
    successful_requests = sum(1 for r in results if r["success"])
    failed_requests = num_requests - successful_requests

    # Print summary
    print(f"\n=== Load Test Complete ===")
    print(f"Total time: {total_time:.2f} seconds")
    print(f"Requests per second: {num_requests / total_time:.2f}")
    print(
        f"Successful requests: {successful_requests} ({successful_requests / num_requests * 100:.1f}%)"
    )
    print(
        f"Failed requests: {failed_requests} ({failed_requests / num_requests * 100:.1f}%)"
    )

    # Return results for further analysis if needed
    return {
        "total_time": total_time,
        "requests_per_second": num_requests / total_time,
        "successful_requests": successful_requests,
        "failed_requests": failed_requests,
        "results": results,
    }


if __name__ == "__main__":
    # Number of requests and concurrency level
    NUM_REQUESTS = 1
    CONCURRENCY = 100

    # Run the load test
    asyncio.run(run_load_test(NUM_REQUESTS, CONCURRENCY))
