from src.db.utils import get_type_id, get_type_name
import httpx
from src.core.config import get_settings
import asyncio
import logging
import os
from typing import Dict, Any

settings = get_settings()

logger = logging.getLogger(__name__)


def transform_phone_number(src: Dict[str, Any]) -> list[Dict[str, Any]]:
    """Transform LinkedIn profile data into a structured format"""

    result = [
        {
            "name": "Mobile Number",
            "type_id": get_type_id("phone"),
            "type": get_type_name("phone"),
            "is_brand": True,
            "value": src["mobile_number"],
        },
        {
            "name": "Source",
            "type_id": get_type_id("text"),
            "type": get_type_name("text"),
            "is_brand": False,
            "value": "Prospeo",
        },
    ]

    return result


async def get_phone_number(
    input: Dict[str, Any],
) -> Dict[str, Any]:
    """Fetch phone number from the Prospeo API

    Returns:
        tuple: (PersonProfileOutput, credit_cost)s
    """
    try:
        if settings.IS_STRESS_TESTING:
            # wait for 30 seconds
            await asyncio.sleep(30)
            return {"success": True, "data": {"mobile_number": "1234567890"}, "credit_cost": 0}
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://api.prospeo.io/mobile-finder",
                json={
                    "url": input["linkedin_profile_url"],
                },
                headers={
                    "x-key": f"{settings.PROSPEO_API_KEY.get_secret_value()}",
                    "content-type": "application/json",
                    "accept": "application/json",
                },
                timeout=60.0,  # 60 second timeout
            )
            response.raise_for_status()

            # Extract credit cost from headers
            output = response.json()
            # Check if error exists in the response and is truthy
            if output.get("error"):
                return {"success": False, "message": "Mobile number not found"}

            # Check if raw_format exists in the response
            if "response" not in output:
                return {
                    "success": False,
                    "message": "Mobile number format not found in response",
                }

            return {
                "success": True,
                "data": {
                    "mobile_number": output["response"]["raw_format"],
                },
                "credit_cost": 5,
            }
    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404 or e.response.status_code == 400:
            logger.info("Prospeo Phone Finder Not Found")
            return {"success": False, "message": "Mobile number not found"}
        elif e.response.status_code == 429:
            logger.info("Prospeo Phone Finder Rate Limit")
            # Extract retry-after header if available
            retry_after = e.response.headers.get("Retry-After")
            return {
                "success": False,
                "message": "Rate limit exceeded. Try again later.",
                "rate-limit": True,
            }
        else:
            logger.error(
                f"Prospeo Phone Finder HTTP unhandled Error: {e.response.status_code}"
            )
            return {
                "success": False,
                "error": f"HTTP Error: {e.response.status_code}",
            }
    except Exception as e:
        logger.error(f"Prospeo Phone Finder Exception: {str(e)}")
        return {"success": False, "error": str(e)}
