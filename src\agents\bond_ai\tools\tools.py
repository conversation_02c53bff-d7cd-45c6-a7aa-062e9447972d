"""Define tools for the Re<PERSON>ct agent."""

# Import all tools from separate files
import uuid

from bond_ai.tools.build_list import build_people_list_from_linkedin

from .read_table_data import read_table_data
from .upsert_linkedin_person_profile_column_from_url import upsert_linkedin_person_profile_column_from_url
from .upsert_linkedin_company_profile_column_from_url import upsert_linkedin_company_profile_column_from_url
from .upsert_phone_number_column import upsert_phone_number_column
from .upsert_work_email_column import upsert_work_email_column
from .run_column import run_column
from .upsert_text_column import upsert_text_column
from .upsert_ai_text_column import upsert_ai_text_column
from .upsert_bond_ai_researcher_column import upsert_bond_ai_researcher_column
from .upsert_ai_message_copywriter import upsert_ai_message_copywriter
from .read_user_view_table_filters import read_user_view_table_filters
from .update_user_view_table_filters_tool import update_user_view_table_filters_tool

from .vector_stores import vector_store_semantic_search

from src.agents.bond_ai.tools.perplexity.perplexity_search import perplexity_search
# from langchain.embeddings import init_embeddings
# from langgraph.store.memory import InMemoryStore
# Add all tools to the list
all_tools = [
    perplexity_search,
    upsert_linkedin_person_profile_column_from_url,
    upsert_linkedin_company_profile_column_from_url,
    upsert_phone_number_column,
    upsert_work_email_column,
    run_column,
    upsert_text_column,
    upsert_ai_text_column,
    upsert_bond_ai_researcher_column,
    upsert_ai_message_copywriter,
    read_user_view_table_filters,
    update_user_view_table_filters_tool,
    read_table_data,
    build_people_list_from_linkedin,
    vector_store_semantic_search
    
]

# Create a lookup dictionary for tools by name
# Used by planned node
tools_by_name = {tool.name: tool for tool in all_tools}

# tool_registry = {
#     str(uuid.uuid4()): tool
#     for tool in tools
# }
# embeddings  =  init_embeddings("openai:text-embedding-3-small")
# tools_store = InMemoryStore(
#     index={
#         "embed": embeddings,
#         "dims": 1536,  # OpenAI embedding dimensions
#         "fields": ["description"],  # Index tool descriptions for search
#     }
# )

# # Index all tools in the store for semantic similarity search
# for tool_id, tool in tool_registry.items():
#     tools_store.put(
#         ("tools",),  # Namespace for tool storage
#         tool_id,
#         {
#             "description": f"{tool.name}: {tool.description}",
#         },
#     )