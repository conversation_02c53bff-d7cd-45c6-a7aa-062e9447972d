"""Bond AI researcher column creation tool."""

from typing import Optional, <PERSON>, Tuple, Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool, InjectedToolArg
from langchain_core.messages import SystemMessage, HumanMessage
from langgraph.config import get_stream_writer
from bond_ai.configuration import Configuration
import json
import re

from ..agent_db import upsert_smart_column
from ..prompts_v1 import BOND_AI_RESEARCHER_COLUMN_PROMPT
from ..utils import load_chat_model_non_thinking
from langgraph.types import Command
from langchain_core.messages import ToolMessage
from langchain_core.tools import InjectedToolCallId

@tool
def upsert_bond_ai_researcher_column(
    tool_call_id: Annotated[str, InjectedToolCallId],
    config: Annotated[RunnableConfig, InjectedToolArg],
    column_name: str,
    prompt: str,
    required_fields: List[str] = [],
    run_condition: Optional[str] = None,
    column_id: Optional[int] = None,
) -> <PERSON><PERSON>[Optional[str], Optional[str]]:
    """Create a new Bond AI researcher column OR update/edit an existing one for online research.
    
    IMPORTANT: ALWAYS provide the column_id if you want to update/edit an existing column. Do NOT duplicate the column by NOT providing the column_id.
    
    This tool creates OR updates/edits an AI-powered research column that can search for information online
    based on values from other columns in your table.
    
    Parameters:
        column_name: A short descriptive name of the column. Must be unique per table.
        prompt: The research prompt template. Can include column references as {{injection path}}.
                Example: "Research the company website for information. Company website: {{linkedin company.cell_details.company_website}}"
        required_fields: List of column references that are required for the research to run.
                         Example: ["linkedin company.cell_details.company_website"] would mark the company website column as required and will not run the column if it doesn't exist.
        run_condition: Optional condition that determines when the AI research should run.
                      Can include column references as {{injection path}}.
                      Example: "The company website is a valid URL: {{linkedin company.cell_details.company_website}}". RCOMMENDED to add when possible to ensure the research is only run when the input data are valid.
        column_id: Optional ID of the existing column to update/edit. If not provided, a new column will be created.
        
    Returns:
        Tuple[Optional[str], Optional[str]]: Tuple containing (success_message, error_message)
        where success_message is a confirmation if successful, None if failed
        and error_message is the error description if failed, None if successful
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Working on {column_name} column"})
        # Set up metadata
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        service_id = 22  # Bond AI Researcher service ID
        
        stream_writer({"custom_tool_call": f"Writing prompt to AI"})
        # Enhance the prompt using the model
        model = load_chat_model_non_thinking(configuration.model)
        # Create the messages
        messages = [
            SystemMessage(BOND_AI_RESEARCHER_COLUMN_PROMPT),
            HumanMessage(prompt)
        ]
        # Get the model response and extract the content
        enhanced_prompt_message = model.invoke(messages, config)
        # Extract the string content from the AIMessage
        enhanced_prompt = enhanced_prompt_message.content
        
        # Ensure enhanced_prompt is a string
        if isinstance(enhanced_prompt, list):
            # If content is a list of structured content, extract only text parts
            text_parts = []
            for item in enhanced_prompt:
                if isinstance(item, dict) and item.get('type') == 'text':
                    text_parts.append(item.get('text', ''))
                elif isinstance(item, str):
                    text_parts.append(item)
                elif hasattr(item, 'text'):
                    text_parts.append(str(item.text))
                else:
                    # Fallback for other types, but skip thinking parts
                    if not (isinstance(item, dict) and item.get('type') == 'thinking'):
                        text_parts.append(str(item))
            enhanced_prompt = ''.join(text_parts)
        elif not isinstance(enhanced_prompt, str):
            # Convert to string if it's some other type
            enhanced_prompt = str(enhanced_prompt)
        
        # Helper function to process text with column injections
        def process_with_injections(text, required_fields_list):
            def replace_injection(match):
                injection_id = match.group(1)
                is_required = "true" if injection_id in required_fields_list else "false"
                return f'<span data-type="column-embed" data-id="{{{{%s}}}}" data-is-required="{is_required}"></span>' % injection_id
                
            # Replace all {{N}} patterns with the HTML tag format
            processed_text = re.sub(r'\{\{([^}]+)\}\}', replace_injection, text)
            return f"<p>{processed_text}</p>"
        
        # Process the enhanced prompt with required fields
        user_prompt = process_with_injections(enhanced_prompt, required_fields)
        
        # Process run condition if provided
        formula = None
        if run_condition:
            formula = process_with_injections(run_condition, [])
        
        # Set up empty inputs and providers as specified in the payload
        inputs = []
        providers = []
        
        # Set up parameters with the user prompt
        parameters = [{"user_prompt": user_prompt}]
        
        # Add formula to parameters if run_condition was provided
        if formula:
            parameters.append({"formula": formula})
        else:
            parameters.append({})
        
        # Call the upsert_smart_column function
        response, error = upsert_smart_column(
            table_id=table_id,
            column_name=column_name,
            service_id=service_id,
            inputs=inputs,
            parameters=parameters,
            providers=providers,
            column_id=column_id
        )
        
        if error:
           return Command(update={
                "last_error_message": error,
                "messages": [
                    ToolMessage(
                        content=error,
                        tool_call_id=tool_call_id,
                    )
                ]
            })
            
        # Extract column details from response
        column = response.get('column', {})
        column_id = column.get('id')
        column_type = column.get('type') 
        name = column.get('name')
        
        action = "updated" if column_id is not None else "created"
        
        # Create JSON response
        response_json = {
            "action": action,
            "column_name": name,
            "message": f"The Bond AI researcher column was successfully {action}. Column name: '{name}' Column ID: {column_id}, Column Type: {column_type}"
        }
        
        response = json.dumps(response_json)
        return Command(update={
                "last_error_message": None,
                "messages": [
                    ToolMessage(
                        content= response,
                        tool_call_id=tool_call_id,
                    )
                ]
            })
        
    except Exception as e:
        return Command(update={
                "last_error_message": str(e),
                "messages": [
                    ToolMessage(
                        content= str(e),
                        tool_call_id=tool_call_id,
                    )
                ]
            })
