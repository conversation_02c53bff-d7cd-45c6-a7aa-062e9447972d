from src.schemas.requests import ServiceRequest
from ..celery_app import app
from src.services.llm.generation import run_perplexity_column
from supabase import AsyncClient
from typing import Dict, Any
import logging

# Import centralized configuration
from src.core.config import get_settings
import asyncio
from src.db.utils import get_supabase_client

# Get application settings
settings = get_settings()

logger = logging.getLogger("backend_api.tasks.perplexity")


@app.task(
    name="src.worker.tasks.perplexity.run_perplexity_task",
    ignore_result=True,
    max_retries=1,
    queue="llm",
    rate_limit="50/m",
    bind=True,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_kwargs={"max_retries": 1},
)
def run_perplexity_task(self, request):
    """Celery task to run Perplexity requests for standard models.

    Note: Celery doesn't support async functions directly as tasks.
    We convert the dict back to the appropriate request object.

    Args:
        self: The Celery task instance
        request_dict: Dictionary representation of the request
    """
    try:
        input = ServiceRequest(**request)
        asyncio.run(_run_perplexity_task(input))

    except Exception as e:
        logger.error(f"Error in run_perplexity_task: {str(e)}")
        # raise  # Re-raise to trigger Celery retry


@app.task(
    name="src.worker.tasks.perplexity.run_perplexity_deep_research_task",
    ignore_result=True,
    max_retries=1,
    queue="llm",
    rate_limit="5/m",  # Lower rate limit for deep research model
    bind=True,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_kwargs={"max_retries": 1},
)
def run_perplexity_deep_research_task(self, request):
    """Celery task to run Perplexity deep research requests.

    Note: Celery doesn't support async functions directly as tasks.
    We convert the dict back to the appropriate request object.

    Args:
        self: The Celery task instance
        request_dict: Dictionary representation of the request
    """
    try:
        input = ServiceRequest(**request)
        asyncio.run(_run_perplexity_task(input))

    except Exception as e:
        logger.error(f"Error in run_perplexity_deep_research_task: {str(e)}")
        # raise  # Re-raise to trigger Celery retry


async def _run_perplexity_task(request: ServiceRequest):
    supabase_client: AsyncClient = await get_supabase_client()

    # Run Perplexity column task
    await run_perplexity_column(request, supabase_client)
