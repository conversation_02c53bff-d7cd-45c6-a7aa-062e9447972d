import requests
import json
import os
from typing import Dict, Any, Optional, List
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set debug mode - change to False to disable debug prints
DEBUG = True

def debug_print(message, data=None):
    """Helper function to print debug information"""
    if DEBUG:
        print(f"\n[DEBUG] {message}")
        if data:43
            if isinstance(data, dict) or isinstance(data, list):
                print(json.dumps(data, indent=2))
            else:
                print(data)
        print("-" * 50)

def search_profiles_by_linkedin_slugs(linkedin_slugs: List[str], api_key: str = None, base_url: str = "https://bee.theswarm.com", limit: int = 10) -> List[str]:
    """
    Search for profiles using multiple LinkedIn identifiers/slugs.
    
    Args:
        linkedin_slugs: List of LinkedIn identifiers/slugs to search for
        api_key: The API key for The Swarm API (defaults to SWARM_API_KEY env var)
        base_url: The base URL for The Swarm API
        limit: Maximum number of results to return (default 10)
        
    Returns:
        List of profile IDs found, or empty list if none found
    """
    # Use provided API key or get from environment
    api_key = api_key or os.getenv("SWARM_API_KEY")
    if not api_key:
        raise ValueError("API key is required. Provide as argument or set SWARM_API_KEY environment variable.")
    
    debug_print(f"Searching for LinkedIn profiles with slugs: {linkedin_slugs}")
    
    # Endpoint for profile search
    endpoint = f"{base_url}/v2/profiles/search"
    
    # Create OpenSearch query to find profiles by LinkedIn usernames
    # Using terms query for exact matching of multiple values
    payload = {
        "query": {
            "bool": {
                "should": [
                    {"match": {"profile_info.linkedin_usernames": {"query": slug}}} 
                    for slug in linkedin_slugs
                ],
                "minimum_should_match": 1
            }
        },
        "limit": limit
    }
    
    debug_print("Search endpoint", endpoint)
    debug_print("Search payload", payload)
    
    # Set up headers with authentication
    headers = {
        "Content-Type": "application/json",
        "x-api-key": api_key
    }
    
    debug_print("Request headers", {"Content-Type": headers["Content-Type"], "x-api-key": "***API_KEY_HIDDEN***"})
    
    try:
        # Make the API request
        debug_print("Sending search request...")
        response = requests.post(
            endpoint,
            headers=headers,
            json=payload
        )
        
        debug_print(f"Response status code: {response.status_code}")
        
        # Check if the request was successful
        response.raise_for_status()
        
        # Parse the response
        data = response.json()
        debug_print("Search response data", data)
        
        # Check if any profiles were found
        if data and "ids" in data and data["ids"]:
            # Return the profile IDs
            profile_ids = data["ids"]
            debug_print(f"Found {len(profile_ids)} profile IDs: {profile_ids}")
            return profile_ids
        else:
            debug_print(f"No profiles found for LinkedIn slugs: {linkedin_slugs}")
            return []
            
    except requests.exceptions.RequestException as e:
        debug_print(f"Error searching for profiles: {e}")
        # If response exists, print its content
        if 'response' in locals() and hasattr(response, 'text'):
            debug_print("Error response content", response.text)
        return []

# Keep the original function for backwards compatibility
def search_profile_by_linkedin_slug(linkedin_slug: str, api_key: str = None, base_url: str = "https://bee.theswarm.com") -> Optional[str]:
    """
    Search for a profile using a single LinkedIn identifier/slug.
    
    Args:
        linkedin_slug: The LinkedIn identifier/slug of the person (e.g., "connorsdavid")
        api_key: The API key for The Swarm API (defaults to SWARM_API_KEY env var)
        base_url: The base URL for The Swarm API
        
    Returns:
        The profile ID if found, otherwise None
    """
    results = search_profiles_by_linkedin_slugs([linkedin_slug], api_key, base_url, limit=1)
    return results[0] if results else None

def fetch_profile_details(profile_ids: List[str], api_key: str = None, base_url: str = "https://bee.theswarm.com") -> Dict[str, Any]:
    """
    Fetch detailed profile information for one or more profile IDs.
    
    Args:
        profile_ids: List of profile IDs to fetch
        api_key: The API key for The Swarm API (defaults to SWARM_API_KEY env var)
        base_url: The base URL for The Swarm API
        
    Returns:
        Dictionary containing profile data and any IDs that were not found
    """
    # Use provided API key or get from environment
    api_key = api_key or os.getenv("SWARM_API_KEY")
    if not api_key:
        raise ValueError("API key is required. Provide as argument or set SWARM_API_KEY environment variable.")
    
    debug_print(f"Fetching details for profile IDs: {profile_ids}")
    
    # Endpoint for fetching profile details
    endpoint = f"{base_url}/v2/profiles/fetch"
    
    # Prepare the request payload
    payload = {
        "ids": profile_ids,
        "fields": ["profile_info", "connections"]  # Request all available fields
    }
    
    debug_print("Fetch endpoint", endpoint)
    debug_print("Fetch payload", payload)
    
    # Set up headers with authentication
    headers = {
        "Content-Type": "application/json",
        "x-api-key": api_key
    }
    
    debug_print("Request headers", {"Content-Type": headers["Content-Type"], "x-api-key": "***API_KEY_HIDDEN***"})
    
    try:
        # Make the API request
        debug_print("Sending fetch request...")
        response = requests.post(
            endpoint,
            headers=headers,
            json=payload
        )
        
        debug_print(f"Response status code: {response.status_code}")
        
        # Check if the request was successful
        response.raise_for_status()
        
        # Parse the response
        data = response.json()
        
        # Print the entire structure for debugging
        debug_print("Complete fetch response", data)
        
        if data.get("results") and len(data["results"]) > 0:
            # Print the first result to understand its structure
            first_result = data["results"][0]
            debug_print("First result structure", first_result)
            
            # Check available keys in the first result
            debug_print("First result keys", list(first_result.keys()))
            
            # Check if profile_info exists instead of profileInfo
            if "profile_info" in first_result:
                debug_print("profile_info keys", list(first_result["profile_info"].keys()))
        
        # Return the profile details
        return data
        
    except requests.exceptions.RequestException as e:
        debug_print(f"Error fetching profile details: {e}")
        # If response exists, print its content
        if 'response' in locals() and hasattr(response, 'text'):
            debug_print("Error response content", response.text)
        return {"results": [], "notFound": profile_ids}

def get_profiles_by_linkedin_slugs(linkedin_slugs: List[str], api_key: str = None, base_url: str = "https://bee.theswarm.com") -> List[Dict[str, Any]]:
    """
    Convenience function to search for multiple profiles by LinkedIn slugs and fetch their details in one step.
    
    Args:
        linkedin_slugs: List of LinkedIn identifiers/slugs
        api_key: The API key for The Swarm API (defaults to SWARM_API_KEY env var)
        base_url: The base URL for The Swarm API
        
    Returns:
        List of complete profile data for found profiles
    """
    debug_print(f"Getting profiles for LinkedIn slugs: {linkedin_slugs}")
    
    # First search for the profile IDs
    profile_ids = search_profiles_by_linkedin_slugs(linkedin_slugs, api_key, base_url)
    
    if not profile_ids:
        debug_print("No profile IDs found, aborting fetch")
        return []
    
    # Then fetch the complete profile details
    profile_data = fetch_profile_details(profile_ids, api_key, base_url)
    
    # Check if profiles were found and return them
    if profile_data and "results" in profile_data and profile_data["results"]:
        debug_print("Successfully retrieved complete profile data")
        return profile_data["results"]
    
    debug_print("Profile data not found in fetch results")
    return []

def get_profile_by_linkedin_slug(linkedin_slug: str, api_key: str = None, base_url: str = "https://bee.theswarm.com") -> Optional[Dict[str, Any]]:
    """
    Convenience function to search for a profile by LinkedIn slug and fetch its details in one step.
    
    Args:
        linkedin_slug: The LinkedIn identifier/slug of the person
        api_key: The API key for The Swarm API (defaults to SWARM_API_KEY env var)
        base_url: The base URL for The Swarm API
        
    Returns:
        The complete profile data if found, otherwise None
    """
    profiles = get_profiles_by_linkedin_slugs([linkedin_slug], api_key, base_url)
    return profiles[0] if profiles else None

# Example usage
if __name__ == "__main__":
    # Example LinkedIn slugs - can be a single username or multiple
    LINKEDIN_SLUGS = ["connorsdavid"]
    
    debug_print(f"Starting profile search for: {LINKEDIN_SLUGS}")
    debug_print(f"Using API key from environment: {'FOUND' if os.getenv('SWARM_API_KEY') else 'NOT FOUND'}")
    
    # Search for multiple profiles at once
    profiles = get_profiles_by_linkedin_slugs(LINKEDIN_SLUGS)
    
    if profiles:
        print(f"\n✅ Found {len(profiles)} profiles:")
        
        for i, profile in enumerate(profiles, 1):
            # Print the profile data structure to understand what fields are available
            debug_print(f"Profile {i} keys", list(profile.keys()))
            
            # This is a safer way to access fields that might not exist
            # First check for profileInfo, then try profile_info if that doesn't exist
            if "profileInfo" in profile:
                profile_info = profile["profileInfo"]
                debug_print("Using 'profileInfo' field")
            elif "profile_info" in profile:
                profile_info = profile["profile_info"]
                debug_print("Using 'profile_info' field")
            else:
                profile_info = {}
                debug_print("No profile info field found! Available fields:", list(profile.keys()))
            
            # Access name safely
            full_name = (
                profile_info.get("fullName") or 
                profile_info.get("full_name") or 
                "Unknown Name"
            )
            print(f"\nProfile {i}: {full_name}")
            
            # Access other fields safely
            job_title = (
                profile_info.get("jobTitle") or 
                profile_info.get("job_title") or 
                "Unknown Title"
            )
            print(f"Current title: {job_title}")
            
            company_name = (
                profile_info.get("jobCompanyName") or
                profile_info.get("job_company_name") or
                profile_info.get("company_name") or
                "Unknown Company"
            )
            print(f"Company: {company_name}")
            
            # Print available LinkedIn usernames
            linkedin_usernames = (
                profile_info.get("linkedinUsernames") or
                profile_info.get("linkedin_usernames") or
                []
            )
            if linkedin_usernames:
                print(f"LinkedIn username(s): {', '.join(linkedin_usernames)}")
            
            # Print connections if available
            if "connections" in profile and profile["connections"]:
                print(f"Number of connections: {len(profile['connections'])}")
    else:
        print(f"\n❌ No profiles found for {LINKEDIN_SLUGS}")
        print("Check the debug output above for more details on what went wrong.")
