"""
The extract node is responsible for extracting information from a tavily search.
"""
import json

from langchain_core.messages import HumanMessage
from langchain_core.runnables import RunnableConfig
from bond_ai.perplexity.state import AgentState
from bond_ai.perplexity.model import get_model

async def extract_node(state: AgentState, config: RunnableConfig):
    """
    The extract node is responsible for extracting information from a tavily search.
    """

    current_step = next((step for step in state["steps"] if step["status"] == "pending"), None)

    if current_step is None:
        raise ValueError("No current step")

    if current_step["type"] != "search":
        raise ValueError("Current step is not of type search")

    system_message = f"""
Extract actionable sales intelligence from the search results for this research step:
{json.dumps(current_step)}

EXTRACTION FOCUS:
- Key facts relevant to sales prospecting and qualification
- Actionable insights for Sales Research Documents (SRD)
- Data points that support ICP analysis or buyer persona development

INSTRUCTIONS:
1. Extract ONLY the most relevant sales intelligence from the search results
2. Focus on information that directly answers the research step objective
3. Include specific data points, metrics, and recent developments
4. Maintain source references for credibility

Format as concise, actionable insights with inline references:
- Key insight [source][1]
- Supporting data point [source][2]

[1]: URL "Source Title"
[2]: URL "Source Title"

DO NOT add commentary or analysis - just extract the relevant intelligence.
"""

    response = await get_model(state).ainvoke([
        state["messages"][0],
        HumanMessage(
            content=system_message
        )
    ], config)

    current_step["result"] = response.content
    current_step["search_result"] = None
    current_step["status"] = "complete"
    current_step["updates"] = [*current_step["updates"], "Done."]

    next_step = next((step for step in state["steps"] if step["status"] == "pending"), None)
    if next_step:
        next_step["updates"] = ["Searching the web..."]

    return state
