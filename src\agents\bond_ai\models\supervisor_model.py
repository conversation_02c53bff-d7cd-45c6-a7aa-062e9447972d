from typing import Literal, Optional
from pydantic import BaseModel, Field



class DelegationRequest(BaseModel):
    delegate_to: str = Field(description="Agent to delegate to")
    task: str = Field(description="Clear task description")
    context: str = Field(description="Relevant context and data")
    expected_output: str = Field(description="What the agent should return")
    priority: Literal["high", "medium", "low"] = Field(description="Priority level")
    
    
class SupervisorResponse(BaseModel):
    message: str = Field(description="Message to the user")
    next_step: str = Field(description="Next step or actions to be taken")
    delegation: Optional[DelegationRequest] = Field(description="Delegation request if applicable")
    