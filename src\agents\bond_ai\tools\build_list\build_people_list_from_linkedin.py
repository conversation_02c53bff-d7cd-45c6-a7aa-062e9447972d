
"""LinkedIn profiles search tool."""

from typing import List, Union, Dict, Any, Tuple, Optional, Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool, InjectedToolArg, InjectedToolCallId
from langchain_core.messages import ToolMessage
from langgraph.types import Command
import json
from langgraph.config import get_stream_writer
from bond_ai.configuration import Configuration
import os
import requests
import time
import random

from bond_ai.models.linkedin_filters import LinkedinSearchFilterTypeEnum as LinkedInFilterType, PersonSearchRequest, create_filter
from bond_ai.agent_db import get_table_row_count, create_table_row, save_linkedin_profiles_to_separate_columns, create_linkedin_profile_columns



#CRUSTDATA_API_KEY = os.getenv("CRUSTDATA_API_KEY")
LIMA_API_KEY = os.getenv("LIMA_API_KEY")


## TODO MOVE TO SHARED AND APPLY TO RCP WHEN IS REQUIRED. CHECK USER NOTIFICATION MESSAGES IF ANY
def _make_api_request_with_retry(url: str, headers: dict, payload: dict, code_errors: List[int] , max_retries: int = 3) -> requests.Response:
    """
    Make API request with exponential backoff and jitter for 500 errors.

    Args:
        url: API endpoint URL
        headers: Request headers
        payload: Request payload
        code_errors: List of error codes to retry for
        max_retries: Maximum number of retry attempts (default: 3)

    Returns:
        requests.Response: The response object

    Raises:
        requests.exceptions.RequestException: If all retries are exhausted
    """
    for attempt in range(max_retries + 1):  # +1 for initial attempt
        try:
            response = requests.post(url, headers=headers, json=payload)

            # If not a 500 error, return immediately (success or other error)
            if response.status_code not in code_errors:
                return response

            # If this is the last attempt, return the 500 response
            if attempt == max_retries:
                return response

            # Calculate exponential backoff with jitter
            base_delay = 2 ** attempt  # 1, 2, 4 seconds
            jitter = random.uniform(0.1, 0.5)  # Add 0.1-0.5 seconds of jitter
            delay = base_delay + jitter

            print(f"[DEBUG] API returned {response.status_code }, retrying in {delay:.2f} seconds (attempt {attempt + 1}/{max_retries + 1})")
            time.sleep(delay)

        except requests.exceptions.RequestException as e:
            # For connection errors, also retry with backoff
            if attempt == max_retries:
                raise e

            base_delay = 2 ** attempt
            jitter = random.uniform(0.1, 0.5)
            delay = base_delay + jitter

            print(f"[DEBUG] Connection error, retrying in {delay:.2f} seconds (attempt {attempt + 1}/{max_retries + 1}): {str(e)}")
            time.sleep(delay)

    # This should never be reached, but just in case
    return response

@tool(args_schema=PersonSearchRequest)
def build_people_list_from_linkedin(
    filters: List[Union[LinkedInFilterType, Dict[str, Any]]],
    page: int = 1,
    **kwargs,     # <- will contain tool_call_id and config
) -> Command:
    """Search for LinkedIn profiles using LimaData API and save results to table.
    This tool searches for LinkedIn profiles based on specified filters and automatically
    saves the results to the current table in separate columns (Full Name, Job Title,
    Company Name, LinkedIn URL).
    Parameters:
        config: Configuration injected by the system
        filters: List of search filters to apply.
                 Each filter should specify the filter_type, operators, and values to filter by.
                 operator can have only one of the following 2 values: 'in', 'not in'
                 Example:
                 [{'filter_type': 'company_type', 'operator': 'in', 'values': ['Technology']}, {'filter_type': 'company_headquarters', 'operator': 'in', 'values': ['United States','Canada','United Kingdom']}, {'filter_type': 'company_headcount', 'operator': 'lt', 'values': ['100']}]

        page: Page number for pagination (default: 1)

  Returns:
        Command: Updates graph state via ToolMessage and last_error_message; on success, may append a memory reference to memory_refs.
    """

    tool_call_id = kwargs.get("tool_call_id", "")
    config = kwargs.get("config")
    try:
        from bond_ai.memory.vector_store import memory_store
        from bond_ai.registry.registry import SupervisorSubAgents
        if not LIMA_API_KEY:
            msg = "Error: LIMA_API_KEY environment variable is not set."
            
            return Command(update={
                "last_error_message": msg,
                "messages": [
                    ToolMessage(content=msg, tool_call_id=tool_call_id)
                ]
            })
        print("[DEBUG] Filters input", filters)
        # Get configuration
        configuration = Configuration.from_runnable_config(config)
       #[DEBUG] Filters [CompanyHeadquartersFilter(filter_type=<FilterTypeEnum.COMPANY_HEADQUARTERS: 'COMPANY_HEADQUARTERS'>, type='in', value=['United States']), {'filter_type': 'COMPANY_HEADCOUNT', 'type': 'in', 'value': ['1-10', '11-50']}, IndustryFilter(filter_type=<FilterTypeEnum.INDUSTRY: 'INDUSTRY'>, type='in', value=['Information Technology', 'Computer Software', 'Internet', 'Technology'])]
        table_id = configuration.table_id

        # Process filters to ensure they're in the correct format
     
        processed_filters = []
        for filter_item in filters:
            if isinstance(filter_item, dict):
                # Convert dictionary to proper filter object
                try:
                    filter_type = filter_item.get('filter_type')
                    if not filter_type:
                        msg = f"Missing 'filter_type' in filter: {filter_item}"
                        return Command(update={
                            "last_error_message": msg,
                            "messages": [ToolMessage(content=msg, tool_call_id=tool_call_id)]
                        })

                    # Extract other parameters
                    filter_params = {k: v for k, v in filter_item.items() if k != 'filter_type'}

                    # Create the proper filter object
                    filter_obj = create_filter(filter_type, **filter_params)
                    processed_filters.append(filter_obj)
                except Exception as e:
                    msg = f"Error converting filter {filter_item}: {str(e)}"
                    return Command(update={
                        "last_error_message": msg,
                        "messages": [ToolMessage(content=msg, tool_call_id=tool_call_id)]
                    })
            else:
                # Already a proper filter object
                processed_filters.append(filter_item)


        # Create or get the LinkedIn profile columns
        column_ids, column_error = create_linkedin_profile_columns(table_id)
        if column_error:
            msg = f"Error creating LinkedIn profile columns: {column_error}"
            return Command(update={
                "last_error_message": msg,
                "messages": [ToolMessage(content=msg, tool_call_id=tool_call_id)]
            })

        # Ensure table has at least 25 rows for the search results
        row_count, row_count_error = get_table_row_count(table_id)
        if row_count_error:
            msg = f"Error checking table row count: {row_count_error}"
            return Command(update={
                "last_error_message": msg,
                "messages": [ToolMessage(content=msg, tool_call_id=tool_call_id)]
            })


        # TODO right now create rows even if the later api fails. According to Abudi the FE new version should have a loading state. So we can flip the logic later on
        # TODO MISSING ERROR HANDLING. IS THERE A TRANSACTION AT DB SIDE?
        # If table has fewer than 25 rows, create the needed rows
        if row_count < 25:
            rows_to_create = 25 - row_count
            print(f"Table has {row_count} rows, creating {rows_to_create} additional rows to reach 25")

            for i in range(rows_to_create):
                # Index should be current row_count + i + 1 (1-based indexing)
                index = row_count + i + 1
                _, create_row_error = create_table_row(table_id, index)
                if create_row_error:
                    msg = f"Error creating row {index}: {create_row_error}"
                    return Command(update={
                        "last_error_message": msg,
                        "messages": [ToolMessage(content=msg, tool_call_id=tool_call_id)]
                    })

            print(f"Successfully created {rows_to_create} rows")
        else:
            print(f"Table already has {row_count} rows (>= 25), no additional rows needed")

        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": "Searching LinkedIn Profiles"})

        print("[Debug] Search LinkedIn Profiles",processed_filters)
        # Create the request model with processed filters and page
        try:
            search_request = PersonSearchRequest(filters=processed_filters, page=page)
        except Exception as e:
            print(f"Error creating search request: {str(e)}")
            msg = f"Error creating search request: {str(e)}"
            return Command(update={
                "last_error_message": msg,
                "messages": [ToolMessage(content=msg, tool_call_id=tool_call_id)]
            })

        print("[Debug] Search LinkedIn Profiles",search_request)




        # Convert to dictionary for the API call
        payload = search_request.model_dump(mode='json')
        print("[Debug] Payload", payload )
        # Make the API request
        headers = {
            'Content-Type': 'application/json',
            #'Accept': 'application/json, text/plain, */*',
            #'Authorization': f'Token {LIMA_API_KEY}'
            'X-Api-Key': f'{LIMA_API_KEY}'
        }
        url = "https://api.limadata.com/api/v2/prospect/live/people/filter"
        MOCKUP = False
        # Make API request with retry mechanism for code_errors errors
        try:
            if MOCKUP:
                import time

                class MockResponse:
                    def __init__(self):
                        self.status_code = 200

                    def json(self):
                        return MOCKUP_LIMADATA_RESPONSE

                # Simulate API delay
                time.sleep(4)
                response = MockResponse()
            else:
                response = _make_api_request_with_retry(url, headers, payload,  code_errors=[500], max_retries=3)
                print("[Debug] Response api", response.json())
        except requests.exceptions.RequestException as e:
            err_msg = {
                400: f"Bad request. Check the payload format. Error details: {payload }",
                401: "Authentication failed. Please check your LIMA_API_KEY.",
                403: "Permission denied. Please check your LIMA_API_KEY.",
                429: "Rate limit exceeded. Please try again later.",
                500: "Internal server error. Please try again later."

            }.get(getattr(response, 'status_code', None) or 500, f"API request failed after retries: {str(e)}")
            msg = f"API request failed after retries: {str(e)}\n {err_msg}"
            return Command(update={
                "last_error_message": msg,
                "messages": [ToolMessage(content=msg, tool_call_id=tool_call_id)]
            })



        # Check response status
        if response.status_code != 200:
            err_msg = {
                400: f"Bad request. Check the payload format. Error details: {payload }",
                401: "Authentication failed. Please check your LIMA_API_KEY.",
                403: "Permission denied. Please check your LIMA_API_KEY.",
                429: "Rate limit exceeded. Please try again later.",
                500: "Internal server error. Please try again later."

            }.get(response.status_code, f"API request failed with status {response.status_code}: {response.text}")
            return Command(update={
                "last_error_message": err_msg,
                "messages": [ToolMessage(content=err_msg, tool_call_id=tool_call_id)]
            })


        data = response.json()

            # Save profiles to separate columns if we have profiles in the response
        profiles_saved_count = 0
        if 'people' in data and data['people']:
            _, save_error = save_linkedin_profiles_to_separate_columns(table_id, data['people'], column_ids)
            if save_error:
                print(f"Warning: Failed to save profiles to columns: {save_error}")
            else:
                profiles_saved_count = len(data['people'])
                print(f"Successfully saved {profiles_saved_count} profiles to separate columns")

            # Construct informative response for the agent (without returning actual profiles)
        total_display_count = data.get('total_count', 0)

        response_data = {
            "message": f"LinkedIn search completed successfully. Found {total_display_count} total profiles matching your criteria. Saved {profiles_saved_count} profiles to the table across 4 columns (Full Name, Job Title, Company Name, LinkedIn URL).",
            "total_display_count": total_display_count,
            "profiles_saved_to_table": profiles_saved_count,
            "columns_created": list(column_ids.keys()),
            "column_ids": column_ids
        }
  
            
     
        # Append memory about the operation for the build_list_agent
        memory_content = f"Saved {profiles_saved_count} profiles to table from LinkedIn search (total found: {total_display_count})."
        memory_ref = memory_store.write(SupervisorSubAgents.build_list_agent.value.name, memory_content)

        return Command(update={
            "last_error_message": None,
            "memory_refs": [memory_ref],
            "messages": [
                ToolMessage(content=json.dumps(response_data), tool_call_id=tool_call_id)
            ],
        })
        
        
        
        # elif response.status_code == 401:
        #     return None, "Authentication failed. Please check your CRUSTDATA_API_KEY."
        # elif response.status_code == 400:
        #     return None, response.json()
        # elif response.status_code == 429:
        #     return None, "Rate limit exceeded. Please try again later."
        # else:
        #     return None, f"API request failed with status {response.status_code}: {response.text}"

    except requests.exceptions.Timeout:
        return Command(update={
            "last_error_message": "Request timed out. Please try again.",
            "messages": [ToolMessage(content="Request timed out. Please try again.", tool_call_id=tool_call_id)]
        })
    except requests.exceptions.ConnectionError:
        return Command(update={
            "last_error_message": "Connection error. Please check your internet connection.",
            "messages": [ToolMessage(content="Connection error. Please check your internet connection.", tool_call_id=tool_call_id)]
        })
    except Exception as e:
        return Command(update={
            "last_error_message": f"An error occurred while searching LinkedIn profiles: {str(e)}",
            "messages": [ToolMessage(content=f"An error occurred while searching LinkedIn profiles: {str(e)}", tool_call_id=tool_call_id)]
        })



MOCKUP_LIMADATA_RESPONSE = {
  "total_count": 22,
  "people": [
    {
      "full_name": "Allie K. Miller",
      "profile_url": "https://www.linkedin.com/in/alliekmiller",
      "headline": "AI advisor & investor • ex-AWS, ex-IBM",
      "location": "New York, United States",
      "image_url": None,
      "position": {
        "company_name": "Self-employed",
        "title": "AI Advisor & Investor",
        "profile_url": "https://www.linkedin.com/company/—",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2023-01-01T00:00:00Z",
        "started_at_company": "2023-01-01T00:00:00Z"
      }
    },
    {
      "full_name": "Lisa Seacat DeLuca",
      "profile_url": "https://www.linkedin.com/in/lisaseacat",
      "headline": "Product & Engineering Leader • Inventor",
      "location": "Boston, Massachusetts, United States",
      "image_url": None,
      "position": {
        "company_name": "Wayfair",
        "title": "Head of Customer Intelligence, Data & ML Platforms",
        "profile_url": "https://www.linkedin.com/company/wayfair",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2022-09-01T00:00:00Z",
        "started_at_company": "2022-09-01T00:00:00Z"
      }
    },
    {
      "full_name": "Laszlo Bock",
      "profile_url": "https://www.linkedin.com/in/laszlobock",
      "headline": "Co-founder & Chair at Gretel.ai • ex-Google SVP People",
      "location": "San Francisco Bay Area",
      "image_url": None,
      "position": {
        "company_name": "Gretel.ai",
        "title": "Co-founder & Chair",
        "profile_url": "https://www.linkedin.com/company/gretel-ai",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2023-01-01T00:00:00Z",
        "started_at_company": "2020-01-01T00:00:00Z"
      }
    },
    {
      "full_name": "Ronald van Loon",
      "profile_url": "https://www.linkedin.com/in/ronaldvanloon",
      "headline": "CEO & Principal Analyst at Intelligent World",
      "location": "Amsterdam, North Holland, Netherlands",
      "image_url": None,
      "position": {
        "company_name": "Intelligent World",
        "title": "CEO & Principal Analyst",
        "profile_url": "https://www.linkedin.com/company/intelligent-world",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2024-01-01T00:00:00Z",
        "started_at_company": "2024-01-01T00:00:00Z"
      }
    },
    {
      "full_name": "Antonio Grasso",
      "profile_url": "https://www.linkedin.com/in/antgrasso",
      "headline": "Founder & CEO at Digital Business Innovation",
      "location": "Naples, Campania, Italy",
      "image_url": None,
      "position": {
        "company_name": "Digital Business Innovation Srl",
        "title": "Founder & CEO",
        "profile_url": "https://www.linkedin.com/company/digital-business-innovation",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2017-01-01T00:00:00Z",
        "started_at_company": "2017-01-01T00:00:00Z"
      }
    },
    {
      "full_name": "Bill Schmarzo",
      "profile_url": "https://www.linkedin.com/in/schmarzo",
      "headline": "Customer Advocate, Data & Analytics • Author of Big Data MBA",
      "location": "San Francisco Bay Area",
      "image_url": None,
      "position": {
        "company_name": "Dell Technologies",
        "title": "Customer Advocate, Data Management Incubation",
        "profile_url": "https://www.linkedin.com/company/delltechnologies",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2022-01-01T00:00:00Z",
        "started_at_company": "2022-01-01T00:00:00Z"
      }
    },
    {
      "full_name": "Theodora Lau",
      "profile_url": "https://www.linkedin.com/in/theodoralau",
      "headline": "Founder at Unconventional Ventures • Fintech, Longevity",
      "location": "Washington, District of Columbia, United States",
      "image_url": None,
      "position": {
        "company_name": "Unconventional Ventures",
        "title": "Founder",
        "profile_url": "https://www.linkedin.com/company/unconventional-ventures",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2018-01-01T00:00:00Z",
        "started_at_company": "2018-01-01T00:00:00Z"
      }
    },
    {
      "full_name": "Jo Peterson",
      "profile_url": "https://www.linkedin.com/in/jopeterson",
      "headline": "VP, Cloud & Security Services at Clarify360",
      "location": "Greater Chicago Area",
      "image_url": None,
      "position": {
        "company_name": "Clarify360",
        "title": "Vice President, Cloud & Security Services",
        "profile_url": "https://www.linkedin.com/company/clarify360",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2017-06-01T00:00:00Z",
        "started_at_company": "2017-06-01T00:00:00Z"
      }
    },
    {
      "full_name": "Efi Pylarinou",
      "profile_url": "https://www.linkedin.com/in/efipylarinou",
      "headline": "Fintech & Tech Influencer • Advisor",
      "location": "Switzerland",
      "image_url": None,
      "position": {
        "company_name": "Efi Pylarinou Advisory",
        "title": "Founder",
        "profile_url": "https://www.linkedin.com/company/efi-pylarinou-advisory",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2019-01-01T00:00:00Z",
        "started_at_company": "2019-01-01T00:00:00Z"
      }
    },
    {
      "full_name": "Aisha Bowe",
      "profile_url": "https://www.linkedin.com/in/aishabowe",
      "headline": "Founder & CEO at STEMBoard • Former NASA Engineer",
      "location": "Washington, District of Columbia, United States",
      "image_url": None,
      "position": {
        "company_name": "STEMBoard",
        "title": "Founder & CEO",
        "profile_url": "https://www.linkedin.com/company/stemboard",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2013-01-01T00:00:00Z",
        "started_at_company": "2013-01-01T00:00:00Z"
      }
    },
    {
      "full_name": "Clement Mihailescu",
      "profile_url": "https://www.linkedin.com/in/clementmihailescu",
      "headline": "Co-founder & CEO at AlgoExpert • ex-Google, ex-Facebook",
      "location": "New York, United States",
      "image_url": None,
      "position": {
        "company_name": "AlgoExpert",
        "title": "Co-founder & CEO",
        "profile_url": "https://www.linkedin.com/company/algoexpert",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2018-01-01T00:00:00Z",
        "started_at_company": "2018-01-01T00:00:00Z"
      }
    },
    {
      "full_name": "Jerry (Je Hak) Lee",
      "profile_url": "https://www.linkedin.com/in/jerryjlee",
      "headline": "Co-founder & COO at Wonsulting • Forbes 30 Under 30",
      "location": "Los Angeles Metropolitan Area",
      "image_url": None,
      "position": {
        "company_name": "Wonsulting",
        "title": "Co-founder & COO",
        "profile_url": "https://www.linkedin.com/company/wonsulting",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2019-01-01T00:00:00Z",
        "started_at_company": "2019-01-01T00:00:00Z"
      }
    },
    {
      "full_name": "Martin Harbech",
      "profile_url": "https://www.linkedin.com/in/harbech/",
      "headline": "Group Director at Meta",
      "location": "Copenhagen, Capital Region, Denmark",
      "image_url": None,
      "position": {
        "company_name": "Meta",
        "title": "Group Director",
        "profile_url": "https://www.linkedin.com/company/meta",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2016-01-01T00:00:00Z",
        "started_at_company": "2016-01-01T00:00:00Z"
      }
    },
    {
      "full_name": "Tamara McCleary",
      "profile_url": "https://www.linkedin.com/in/tamaramccleary",
      "headline": "CEO at Thulium • Tech Futurist & Speaker",
      "location": "Austin, Texas, United States",
      "image_url": None,
      "position": {
        "company_name": "Thulium",
        "title": "Chief Executive Officer",
        "profile_url": "https://www.linkedin.com/company/thulium",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2014-01-01T00:00:00Z",
        "started_at_company": "2014-01-01T00:00:00Z"
      }
    },
    {
      "full_name": "Antonio Vieira Santos",
      "profile_url": "https://www.linkedin.com/in/antoniovieirasantos",
      "headline": "Co-founder at The Digital Transformation Lab",
      "location": "Lisbon, Lisbon, Portugal",
      "image_url": None,
      "position": {
        "company_name": "The Digital Transformation Lab",
        "title": "Co-founder",
        "profile_url": "https://www.linkedin.com/company/the-digital-transformation-lab",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2019-01-01T00:00:00Z",
        "started_at_company": "2019-01-01T00:00:00Z"
      }
    },
    {
      "full_name": "Sébastien Bourguignon",
      "profile_url": "https://www.linkedin.com/in/sbourguignon",
      "headline": "Consulting Director at Sopra Steria • Speaker",
      "location": "Paris, Île-de-France, France",
      "image_url": None,
      "position": {
        "company_name": "Sopra Steria",
        "title": "Consulting Director",
        "profile_url": "https://www.linkedin.com/company/sopra-steria",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2016-01-01T00:00:00Z",
        "started_at_company": "2016-01-01T00:00:00Z"
      }
    },
    {
      "full_name": "Giuliano Liguori",
      "profile_url": "hhttps://www.linkedin.com/in/ingliguori/",
      "headline": "Founder at Digital Leaders • Innovation Manager",
      "location": "Rome, Lazio, Italy",
      "image_url": None,
      "position": {
        "company_name": "Digital Leaders",
        "title": "Founder",
        "profile_url": "https://www.linkedin.com/company/digital-leaders",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2018-01-01T00:00:00Z",
        "started_at_company": "2018-01-01T00:00:00Z"
      }
    },
    {
      "full_name": "Paula Piccard",
      "profile_url": "https://www.linkedin.com/in/paulapiccard",
      "headline": "Digital Influencer & Social Media Manager",
      "location": "United States",
      "image_url": None,
      "position": {
        "company_name": "Self-employed",
        "title": "Consultant",
        "profile_url": "https://www.linkedin.com/company/—",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2020-01-01T00:00:00Z",
        "started_at_company": "2020-01-01T00:00:00Z"
      }
    },
    {
      "full_name": "Rob Crasco",
      "profile_url": "https://www.linkedin.com/in/robcrasco",
      "headline": "Owner at CyberAdept • VR/AR & AI Evangelist",
      "location": "United States",
      "image_url": None,
      "position": {
        "company_name": "CyberAdept",
        "title": "Owner",
        "profile_url": "https://www.linkedin.com/company/cyberadept",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2015-01-01T00:00:00Z",
        "started_at_company": "2015-01-01T00:00:00Z"
      }
    },
    {
      "full_name": "Glen Gilmore",
      "profile_url": "https://www.linkedin.com/in/glengilmore",
      "headline": "Digital Marketing Strategist • Author & Speaker",
      "location": "United States",
      "image_url": None,
      "position": {
        "company_name": "Gilmore Business Network",
        "title": "Principal",
        "profile_url": "https://www.linkedin.com/company/gilmore-business-network",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2012-01-01T00:00:00Z",
        "started_at_company": "2012-01-01T00:00:00Z"
      }
    },
    {
      "full_name": "Satya Nadella",
      "profile_url": "https://www.linkedin.com/in/satyanadella",
      "headline": "Chairman & CEO at Microsoft",
      "location": "Seattle, Washington, United States",
      "image_url": None,
      "position": {
        "company_name": "Microsoft",
        "title": "Chairman & CEO",
        "profile_url": "https://www.linkedin.com/company/microsoft",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2014-02-04T00:00:00Z",
        "started_at_company": "1992-01-01T00:00:00Z"
      }
    },
    {
      "full_name": "Mary Barra",
      "profile_url": "https://www.linkedin.com/in/mary-barra",
      "headline": "Chair & CEO at General Motors",
      "location": "Detroit, Michigan, United States",
      "image_url": None,
      "position": {
        "company_name": "General Motors",
        "title": "Chair & CEO",
        "profile_url": "https://www.linkedin.com/company/general-motors",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2014-01-15T00:00:00Z",
        "started_at_company": "1980-01-01T00:00:00Z"
      }
    },
    {
      "full_name": "Sundar Pichai",
      "profile_url": "https://www.linkedin.com/in/sundarpichai",
      "headline": "CEO at Google & Alphabet",
      "location": "Mountain View, California, United States",
      "image_url": None,
      "position": {
        "company_name": "Google",
        "title": "CEO",
        "profile_url": "https://www.linkedin.com/company/google",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2015-10-02T00:00:00Z",
        "started_at_company": "2004-01-01T00:00:00Z"
      }
    },
    {
      "full_name": "Andy Jassy",
      "profile_url": "https://www.linkedin.com/in/andy-jassy-8b1615/",
      "headline": "President & CEO at Amazon",
      "location": "Seattle, Washington, United States",
      "image_url": None,
      "position": {
        "company_name": "Amazon",
        "title": "President & CEO",
        "profile_url": "https://www.linkedin.com/company/amazon",
        "image_url": None,
        "company_id": "",
        "is_current": True,
        "started_at_position": "2021-07-05T00:00:00Z",
        "started_at_company": "1997-01-01T00:00:00Z"
      }
    }
  ]
}


        # print("[Debug] Response api status code",response.status_code )

        #mockup response for testing
        # if response.status_code == 402111 or response.status_code == 400111:
        #     data = {
        #         "profiles": [
        #             {
        #                 "name": "Fiona Carney",
        #                 "headline": "COO at Microsoft EMEA",
        #                 "current_company": "Microsoft",
        #                 "current_title": "Chief Operating Officer, EMEA",
        #                 "linkedin_profile_url": "https://www.linkedin.com/in/fionacarney"
        #             },
        #             {
        #                 "name": "Rajesh Patel",
        #                 "headline": "CTO • Google Cloud APAC",
        #                 "current_company": "Google",
        #                 "current_title": "Chief Technology Officer, APAC",
        #                 "linkedin_profile_url": "https://www.linkedin.com/in/rajesh-patel-cto"
        #             },
        #             {
        #                 "name": "María González",
        #                 "headline": "VP Sales LATAM at Salesforce",
        #                 "current_company": "Salesforce",
        #                 "current_title": "Vice President, Sales – LATAM",
        #                 "linkedin_profile_url": "https://www.linkedin.com/in/mariagonzalez-sales"
        #             },
        #             {
        #                 "name": "Chloé Dubois",
        #                 "headline": "Head of AI Research at DeepMind",
        #                 "current_company": "DeepMind",
        #                 "current_title": "Head of AI Research, Europe",
        #                 "linkedin_profile_url": "https://www.linkedin.com/in/chloedubois-ai"
        #             },
        #             {
        #                 "name": "Lukas Müller",
        #                 "headline": "CFO at Siemens Mobility",
        #                 "current_company": "Siemens",
        #                 "current_title": "Chief Financial Officer, Mobility",
        #                 "linkedin_profile_url": "https://www.linkedin.com/in/lukas-mueller-cfo"
        #             },
        #             {
        #                 "name": "Amina Hassan",
        #                 "headline": "Director of Product • Spotify MENA",
        #                 "current_company": "Spotify",
        #                 "current_title": "Director of Product, MENA",
        #                 "linkedin_profile_url": "https://www.linkedin.com/in/amina-hassan-product"
        #             },
        #             {
        #                 "name": "Jacob Lee",
        #                 "headline": "Senior Data Scientist at Netflix",
        #                 "current_company": "Netflix",
        #                 "current_title": "Senior Data Scientist",
        #                 "linkedin_profile_url": "https://www.linkedin.com/in/jacoblee-datascience"
        #             },
        #             {
        #                 "name": "Sofia Rossi",
        #                 "headline": "CMO at Gucci",
        #                 "current_company": "Gucci",
        #                 "current_title": "Chief Marketing Officer",
        #                 "linkedin_profile_url": "https://www.linkedin.com/in/sofiarossi-cmo"
        #             },
        #             {
        #                 "name": "Daniel Kim",
        #                 "headline": "Global Head of Strategy • Tesla",
        #                 "current_company": "Tesla",
        #                 "current_title": "Global Head of Strategy",
        #                 "linkedin_profile_url": "https://www.linkedin.com/in/danielkim-strategy"
        #             },
        #             {
        #                 "name": "Laura Smith",
        #                 "headline": "VP People Ops at Airbnb",
        #                 "current_company": "Airbnb",
        #                 "current_title": "Vice President, People Operations",
        #                 "linkedin_profile_url": "https://www.linkedin.com/in/laurasmith-peopleops"
        #             }
        #         ],
        #         "total_display_count": 78000
        #     }
        #        # Save profiles to separate columns if we have profiles in the response
        #     profiles_saved_count = 0
        #     if 'profiles' in data and data['profiles']:
        #         save_success, save_error = save_linkedin_profiles_to_separate_columns(table_id, data['profiles'], column_ids)
        #         if save_error:
        #             print(f"Warning: Failed to save profiles to columns: {save_error}")
        #         else:
        #             profiles_saved_count = len(data['profiles'])
        #             print(f"Successfully saved {profiles_saved_count} profiles to separate columns")


        #     # Construct informative response for the agent (without returning actual profiles)
        #     total_display_count = data.get('total_count', 0)  #total_display_count

        #     response_data = {
        #         "message": f"LinkedIn search completed successfully. Found {total_display_count} total profiles matching your criteria. Saved {profiles_saved_count} profiles to the table across 4 columns (Full Name, Job Title, Company Name, LinkedIn URL).",
        #         "total_display_count": total_display_count,
        #         "profiles_saved_to_table": profiles_saved_count,
        #         "columns_created": list(column_ids.keys()),
        #         "column_ids": column_ids
        #     }

        #     return response_data, None
