"""Utility & helper functions."""

import os  # <-- Add this import
import json
from langchain.chat_models import init_chat_model
from langchain_core.language_models import BaseChatModel
from langchain_core.messages import BaseMessage, AIMessage, SystemMessage, HumanMessage
from langchain_core.runnables import Run<PERSON>bleConfig
from typing import Any, Dict, List
from src.outbond_ai_assistant.prompts import TABLE_SUMMARY_PROMPT


def load_chat_model(fully_specified_name: str) -> BaseChatModel:
    """Load a chat model from a fully specified name."""
    provider, model = fully_specified_name.split("/", maxsplit=1)
    
    # Explicitly pass region for Bedrock if provider is 'bedrock'
    if provider.lower() == "bedrock":
        region = os.environ.get("AWS_REGION") or os.environ.get("AWS_DEFAULT_REGION")
        if not region:
            # Raise an error or handle the missing region case appropriately
            # For now, we let init_chat_model potentially fail if it still can't find it.
            # Or you could raise: raise ValueError("AWS_REGION or AWS_DEFAULT_REGION environment variable not set.")
            pass # Let init_chat_model handle it or raise its own error
        
        # Pass region_name if found
        if region:
             return init_chat_model(
                 model, 
                 model_provider=provider, 
                 region_name=region,  # Explicitly pass region_name
                 temperature=1,  # Set temperature to 1
                 thinking={"type": "enabled", "budget_tokens": 4000},
                 max_tokens=131072,
             )
        else:
             # Fallback to default behavior if region is not in env vars
             return init_chat_model(model, model_provider=provider, temperature=0)
    else:
        # For other providers, use the original simple call
        return init_chat_model(model, model_provider=provider, temperature=0)


def generate_column_description(
    table_id: str,
    column_id: int,
    column_name: str,
    column_data: Dict[str, Any],
    config: RunnableConfig
) -> tuple[str, bool]:
    """Generate a description for a column using LLM analysis of sample data.
    
    Args:
        table_id: The table ID
        column_id: The column ID
        column_name: The column name
        column_data: The column data from get_column_data
        config: The runnable config
        
    Returns:
        Tuple of (description, success)
    """
    try:
        # Check if we have meaningful data
        sample_values = column_data.get('sample_values', [])
        if not sample_values:
            return f"Empty column for {column_name} data", False
        
        # Create a mock table structure for the TABLE_SUMMARY_PROMPT
        # Format it like the table data structure expected by the prompt
        mock_table_data = {
            "columns": [
                {
                    "column_id": column_id,
                    "column_name": column_name,
                    "cells": []
                }
            ]
        }
        
        # Add sample cell data
        for i, sample in enumerate(sample_values[:5]):  # Use first 5 samples
            cell = {
                "value": sample['value'],
                "row_id": sample.get('row_id', i + 1),
                "run_status": {"run": "completed", "message": "Sample data"}
            }
            mock_table_data["columns"][0]["cells"].append(cell)
        
        # Convert to JSON string for the prompt
        table_data_str = json.dumps(mock_table_data, indent=2, default=str)
        
        # Load model and generate description using TABLE_SUMMARY_PROMPT
        model = load_chat_model("openai/gpt-4o-mini")
        
        prompt_message = SystemMessage(TABLE_SUMMARY_PROMPT)
        data_message = HumanMessage(table_data_str)
        
        # Get the analysis from the model
        response = model.invoke([prompt_message, data_message], config)
        response_content = str(response.content).strip()
        
        # Parse the JSON response to extract the data_summary
        try:
            parsed_response = json.loads(response_content)
            if isinstance(parsed_response, list) and len(parsed_response) > 0:
                # Extract data_summary from the first (and only) column
                description = parsed_response[0].get('data_summary', f"Column containing {column_name} data")
            else:
                description = f"Column containing {column_name} data"
        except json.JSONDecodeError:
            # If JSON parsing fails, try to extract description from text response
            # Look for data_summary in the response
            lines = response_content.split('\n')
            description = f"Column containing {column_name} data"
            for line in lines:
                if 'data_summary' in line.lower() and ':' in line:
                    # Extract the text after the colon
                    summary_part = line.split(':', 1)[1].strip().strip('"\'')
                    if summary_part and len(summary_part) > 10:  # Basic validation
                        description = summary_part
                        break
        
        # Clean up the description
        description = description.strip('"\'').strip()
        
        return description, True
        
    except Exception as e:
        print(f"Error generating description for column {column_id}: {str(e)}")
        return f"Column containing {column_name} data", False


def clean_thinking_blocks_for_bedrock(messages: List[BaseMessage]) -> List[BaseMessage]:
    """Clean thinking blocks by removing internal metadata that AWS Bedrock doesn't accept.
    
    AWS Bedrock Claude models with extended thinking generate thinking blocks with internal
    metadata (like 'index') that cannot be passed back as input. This function removes
    the problematic fields while preserving the thinking content.
    
    Args:
        messages: List of messages to clean
        
    Returns:
        List of cleaned messages with thinking block metadata removed
    """
    cleaned_messages = []
    
    for message in messages:
        if isinstance(message, AIMessage) and isinstance(message.content, list):
            # Clean the content blocks
            cleaned_content = []
            
            for content_block in message.content:
                if isinstance(content_block, dict) and content_block.get("type") == "thinking":
                    # For thinking blocks, keep only the essential fields with correct field names
                    cleaned_thinking = {
                        "type": "thinking",
                        "thinking": content_block.get("text", "") or content_block.get("thinking", ""),
                        "signature": content_block.get("signature", "")
                    }
                    cleaned_content.append(cleaned_thinking)
                else:
                    # Keep other content blocks as is
                    cleaned_content.append(content_block)
            
            # Create new AI message with cleaned content
            cleaned_message = AIMessage(
                content=cleaned_content,
                tool_calls=getattr(message, 'tool_calls', []),
                id=getattr(message, 'id', None),
                name=getattr(message, 'name', None),
                additional_kwargs=getattr(message, 'additional_kwargs', {}),
                response_metadata=getattr(message, 'response_metadata', {}),
            )
            cleaned_messages.append(cleaned_message)
        else:
            # For non-AI messages or messages without list content, keep as is
            cleaned_messages.append(message)
    
    return cleaned_messages


def remove_keys_from_cell_details(data):
    """Remove specified keys from cell_details recursively.
    
    Args:
        data: The data structure to process (list, dict, or scalar value)
        
    Returns:
        The processed data structure with keys removed
    """
    # Keys to remove from cell_details objects
    keys_to_remove = ['type', 'type_id', 'is_brand']
    
    if isinstance(data, list):
        return [remove_keys_from_cell_details(item) for item in data]
    elif isinstance(data, dict):
        result = {}
        for key, value in data.items():
            # Skip keys that should be removed
            if key in keys_to_remove:
                continue
                
            # Special handling for cell_details
            if key == 'cell_details' and isinstance(value, list):
                # Process each item in cell_details
                cleaned_details = []
                for detail in value:
                    if isinstance(detail, dict):
                        # Remove the specified keys at this level
                        cleaned_detail = {k: v for k, v in detail.items() if k not in keys_to_remove}
                        # Recursively process the remaining values
                        for k, v in cleaned_detail.items():
                            cleaned_detail[k] = remove_keys_from_cell_details(v)
                        cleaned_details.append(cleaned_detail)
                    elif isinstance(detail, list):
                        # Handle nested lists within cell_details
                        cleaned_details.append(remove_keys_from_cell_details(detail))
                    else:
                        cleaned_details.append(detail)
                result[key] = cleaned_details
            else:
                # Recursively process all other values
                result[key] = remove_keys_from_cell_details(value)
            
        return result
    else:
        return data








def load_chat_model_non_thinking(fully_specified_name: str) -> BaseChatModel:
    """Load a chat model from a fully specified name."""
    provider, model = fully_specified_name.split("/", maxsplit=1)
    return init_chat_model(model, model_provider=provider)
