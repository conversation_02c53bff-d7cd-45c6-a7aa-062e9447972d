"""Pydantic models for smart column upsert operations."""

from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, ConfigDict


class InputMapping(BaseModel):
    """Model for input mapping configuration in smart columns.
    
    This is a flexible key-value mapping where the key represents the input field name
    and the value represents the data source or injection path.
    """
    model_config = ConfigDict(use_enum_values=True, extra="allow")



class Parameter(BaseModel):
    """Model for parameter configuration in smart columns.
    
    This is a flexible key-value mapping for various parameter types used in smart columns.
    """
    model_config = ConfigDict(use_enum_values=True, extra="allow")
    
    # Common parameter types found in tools.py:
    user_prompt: Optional[str] = Field(
        None,
        description="User prompt for AI-powered columns (AI Text, AI Message Copywriter, etc.)",
        examples=["<p>Write a personalized intro for {{name}}</p>"]
    )
    
    formula: Optional[str] = Field(
        None,
        description="Formula or condition for when the column should run",
        examples=["<p>{{company_name}} is not empty</p>"]
    )
    
    system_prompt: Optional[str] = Field(
        None,
        description="System prompt for AI Message Copywriter columns",
        examples=["<p>You are a professional copywriter...</p>"]
    )
    


class Provider(BaseModel):
    """Model for provider configuration in smart columns.
    
    This is a flexible mapping that can handle different provider configurations
    based on the service type.
    """
    model_config = ConfigDict(use_enum_values=True, extra="allow")
    
    # Common provider configurations found in tools.py:
    providers: Optional[List[str]] = Field(
        None,
        description="General providers list (e.g., for LinkedIn, phone)",
        examples=[["outbond"], ["leadmagic", "prospeo"]]
    )
    
    email_providers: Optional[List[str]] = Field(
        None,
        description="Email finding providers",
        examples=[["leadmagic", "findymail", "prospeo"]]
    )
    
    verify_providers: Optional[List[str]] = Field(
        None,
        description="Email verification providers",
        examples=[["millionverifier"]]
    )
    
    # Allow any additional fields for other provider types


class SmartColumnSettings(BaseModel):
    """Model for smart column settings configuration."""
    model_config = ConfigDict(use_enum_values=True)
    
    inputs: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="List of input mappings for the smart column. Can be empty for some column types.",
        examples=[
            [{"linkedin_profile_url": "linkedin_url_column"}],
            [{"full_name": "name_column"}, {"company_domain": "domain_column"}],
            []  # Empty for AI/Text columns
        ]
    )
    
    parameters: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="List of parameters for the smart column configuration. Often contains user_prompt, formula, etc.",
        examples=[
            [{"user_prompt": "<p>Generate text based on {{name}}</p>"}],
            [{"user_prompt": "<p>...</p>"}, {"formula": "<p>condition</p>"}, {"system_prompt": "<p>...</p>"}],
            [{}]  # Empty dict for simple columns
        ]
    )
    
    providers: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="List of provider configurations for the smart column. Can be empty for some column types.",
        examples=[
            [{"providers": ["outbond"]}],
            [{"email_providers": ["leadmagic", "findymail"]}, {"verify_providers": ["millionverifier"]}],
            []  # Empty for AI/Text columns
        ]
    )


class UpsertSmartColumnRequest(BaseModel):
    """Request model for creating or updating a smart column.
    
    This model defines the structure for upserting smart columns with comprehensive
    validation and documentation following Google style guidelines.
    """
    model_config = ConfigDict(use_enum_values=True)
    
    table_id: str = Field(
        ...,
        description="The unique identifier of the table to add/update the column in",
        min_length=1,
        examples=["tbl_c85117eb4106d464"]
    )
    
    column_name: str = Field(
        ...,
        description="Name of the smart column",
        min_length=1,
        max_length=255,
        examples=["LinkedIn Profile", "Linkedin Company Profile", "Company Email", "Phone Number", "AI Research", "Personalized Message"]
    )
    
    service_id: int = Field(
        ...,
        description="ID of the service to use for this smart column",
        ge=1,
        examples=[
            7,   # LinkedIn Person Profile
            8,   # LinkedIn Company Profile  
            10,  # Work Email
            11,  # Phone Number
            13,  # AI Message Copywriter
            14,  # AI Text
            15,  # Text/Formula
            22   # Bond AI Researcher
        ]
    )
    
    settings: SmartColumnSettings = Field(
        ...,
        description="Configuration settings for the smart column including inputs, parameters, and providers"
    )

    column_id: Optional[int] = Field(
        default=None,
        description="The ID of the column to update"
    )
    


class UpsertSmartColumnResponse(BaseModel):
    """Response model for smart column upsert operations."""
    model_config = ConfigDict(use_enum_values=True)
    
    column: Optional[Dict[str, Any]] = Field(
        default=None,
        description="The complete column data when operation is successful"
    )
    
    error_message: Optional[str] = Field(
        default=None,
        description="Error message when operation fails"
    )
    
    success: bool = Field(
        default=True,
        description="Indicates if the operation was successful"
    )




# Convenience functions for creating common configurations
def create_linkedin_profile_settings(linkedin_profile_url: str) -> SmartColumnSettings:
    """Create settings for LinkedIn Person Profile column."""
    return SmartColumnSettings(
        inputs=[{"linkedin_profile_url": linkedin_profile_url}],
        parameters=[{}],
        providers=[{"providers": ["outbond"]}]
    )


# Convenience functions for creating common configurations
def create_linkedin_company_profile_settings(linkedin_company_profile_url: str) -> SmartColumnSettings:
    """Create settings for LinkedIn Company Profile column."""
    return SmartColumnSettings(
        inputs=[{"linkedin_company_url": linkedin_company_profile_url}],
        parameters=[{}],
        providers=[{"providers": ["outbond"]}]
    )

def create_work_email_settings(full_name: str, company_domain: str) -> SmartColumnSettings:
    """Create settings for Work Email column."""
    return SmartColumnSettings(
        inputs=[{"full_name": full_name}, {"company_domain": company_domain}],
        parameters=[{}],
        providers=[
            {"email_providers": ["leadmagic", "findymail", "prospeo"]},
            {"verify_providers": ["millionverifier"]}
        ]
    )


def create_ai_text_settings(user_prompt: str, formula: Optional[str] = None) -> SmartColumnSettings:
    """Create settings for AI Text column."""
    parameters = [{"user_prompt": user_prompt}]
    if formula:
        parameters.append({"formula": formula})
    else:
        parameters.append({})
    
    return SmartColumnSettings(
        inputs=[],
        parameters=parameters,
        providers=[]
    )
