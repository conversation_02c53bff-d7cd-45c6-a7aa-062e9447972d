from typing import List, Optional, Union
from pydantic import BaseModel, ConfigDict
from src.schemas.linkedin import (
    LinkedinPersonProfileUrl,
    LinkedinCompanyProfileUrl,
    LinkedinSchoolProfileUrl,
)


# Location Model
class Location(BaseModel):
    model_config = ConfigDict(extra="ignore")
    city: Optional[str] = None
    state: Optional[str] = None
    country: Optional[str] = None


# Role Model (for nested roles within experiences)
class Role(BaseModel):
    model_config = ConfigDict(extra="ignore")
    title: Optional[str] = None
    location: Optional[str] = None
    starts_at: Optional[str] = None
    ends_at: Optional[str] = None
    description: Optional[str] = None


# Experience Model
class Experience(BaseModel):
    model_config = ConfigDict(extra="ignore")
    company: Optional[str] = None
    title: Optional[str] = None
    location: Optional[str] = None
    starts_at: Optional[str] = None
    ends_at: Optional[str] = None
    company_id: Optional[str] = None
    profile_url: Optional[str] = None
    logo_url: Optional[str] = None
    description: Optional[str] = None
    roles: Optional[List[Role]] = None


# Education Model
class Education(BaseModel):
    model_config = ConfigDict(extra="ignore")
    school: Optional[str] = None
    degree_name: Optional[str] = None
    starts_at: Optional[str] = None
    ends_at: Optional[str] = None
    school_id: Optional[str] = None
    logo_url: Optional[str] = None
    profile_url: Optional[str] = None
    description: Optional[str] = None


# Skill Model
class Skill(BaseModel):
    model_config = ConfigDict(extra="ignore")
    name: Optional[str] = None


# Language Model
class Language(BaseModel):
    model_config = ConfigDict(extra="ignore")
    name: Optional[str] = None


# Recommendation Model
class Recommendation(BaseModel):
    model_config = ConfigDict(extra="ignore")
    type: Optional[str] = None  # "given" or "received"
    name: Optional[str] = None
    profile_url: Optional[str] = None
    person_headline: Optional[str] = None
    caption: Optional[str] = None
    image_url: Optional[str] = None
    text: Optional[str] = None


# Image Model
class Image(BaseModel):
    model_config = ConfigDict(extra="ignore")
    url: Optional[str] = None


# LinkedIn Profile Response Model
class PersonProfileOutput(BaseModel):
    model_config = ConfigDict(extra="ignore")
    full_name: Optional[str] = None
    profile_url: Optional[str] = None
    urn: Optional[str] = None
    website: Optional[str] = None
    connections: Optional[int] = None
    followers: Optional[int] = None
    headline: Optional[str] = None
    about: Optional[str] = None
    location: Optional[Location] = None
    profile_image_url: Optional[str] = None
    cover_image_url: Optional[str] = None
    experiences: Optional[List[Experience]] = None
    educations: Optional[List[Education]] = None
    skills: Optional[List[Skill]] = None
    languages: Optional[List[Language]] = None
    recommendations: Optional[List[Recommendation]] = None
    all_images: Optional[List[Image]] = None


class PersonProfileResponse(BaseModel):
    model_config = ConfigDict(extra="ignore")
    profile: PersonProfileOutput
    credit_cost: int


# Legacy models for backward compatibility (if needed)
class DateObject(BaseModel):
    day: Optional[int] = None
    month: Optional[int] = None
    year: Optional[int] = None


# Legacy Certification Model
class Certification(BaseModel):
    model_config = ConfigDict(extra="ignore")
    name: Optional[str] = None
    authority: Optional[str] = None
    license_number: Optional[str] = None
    starts_at: Optional[DateObject] = None
    ends_at: Optional[DateObject] = None


# Legacy Project Model
class Project(BaseModel):
    model_config = ConfigDict(extra="ignore")
    title: Optional[str] = None
    description: Optional[str] = None
    url: Optional[str] = None
    starts_at: Optional[DateObject] = None
    ends_at: Optional[DateObject] = None


# Legacy Activity Model
class Activity(BaseModel):
    model_config = ConfigDict(extra="ignore")
    title: Optional[str] = None
    link: Optional[str] = None
    activity_status: Optional[str] = None


# Legacy Similar Profile Model
class SimilarProfile(BaseModel):
    model_config = ConfigDict(extra="ignore")
    name: Optional[str] = None
    link: Optional[str] = None
    summary: Optional[str] = None
    location: Optional[str] = None


# Legacy People Also Viewed Model
class PeopleAlsoViewed(BaseModel):
    model_config = ConfigDict(extra="ignore")
    name: Optional[str] = None
    link: Optional[str] = None
    summary: Optional[str] = None
    location: Optional[str] = None


# Legacy Salary Range Model
class SalaryRange(BaseModel):
    model_config = ConfigDict(extra="ignore")
    min: Optional[int] = None
    max: Optional[int] = None




class LimaPersonLookupOutput(BaseModel):
    model_config = ConfigDict(extra="ignore")
    linkedin_url: Optional[str] = None
    github_url: Optional[str] = None
    x_url: Optional[str] = None
    facebook_url: Optional[str] = None

class LimaPersonLookupResponse(BaseModel):
    model_config = ConfigDict(extra="ignore")
    profile: LimaPersonLookupOutput
    credit_cost: int

