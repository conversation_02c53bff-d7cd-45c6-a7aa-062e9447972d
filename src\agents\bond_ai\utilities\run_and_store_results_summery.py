"""Utility that kicks off a run and polls results until completion for 5 rows."""

from typing import Optional, Dict, Any, Tu<PERSON>, List
import time

from typing import Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg

from ..supabase.run_table import run_table as supabase_run_table
from ..supabase.run_table import RunTableRequest
from ..supabase.read_table.read_table import get_table_data
from ..supabase.read_table.models import TableDataRequest
from ..tools.read_table_tools.read_table import read_table_data_tool
from ..supabase.table.update_columns import update_column_by_name


TERMINAL_STATUSES = {"completed", "failed", "awaiting_input", "invalid_input"}


def run_and_store_results_summary(
    config: Annotated[RunnableConfig, InjectedToolArg],
    request: RunTableRequest,
    poll_interval_seconds: float = 5.0,
    max_attempts: int = 10,
) -> Tu<PERSON>[Optional[Dict[str, Any]], Optional[str]]:
    """Initiate a run for 5 rows and wait until all 5 cells reach a terminal status.

    Steps:
    - Start run via `run_table` overriding rows_limit to 5
    - Determine the target column name from the request or the RPC response
    - Poll `get_table_data` with that column and apply_table_filters=True
      until all 5 cells are in a terminal status
    - Return the final `get_table_data` payload

    Args:
        config: Runnable configuration (must provide table_id via Configuration)
        request: RunTableRequest parameters (column_names, row_ids, etc.)
        poll_interval_seconds: Delay between polls
        max_attempts: Max polling attempts before timing out

    Returns:
        (data, error): data is the final table data from get_table_data; error is a string on failure
    """
    try:
        # Ensure rows_limit is 5 for this utility
        effective_request = request
        try:
            # Prefer non-mutating copy when available (Pydantic v2)
            if hasattr(request, "model_copy"):
                effective_request = request.model_copy(update={"rows_limit": 5})  # type: ignore[attr-defined]
            else:
                # Fallback for older Pydantic versions
                effective_request = RunTableRequest(
                    table_id=request.table_id,
                    column_names=request.column_names,
                    apply_table_view=request.apply_table_view,
                    rows_limit=5
                )
        except Exception:
            # If anything goes wrong, enforce rows_limit directly
            effective_request.rows_limit = 5  # type: ignore[assignment]

        run_data = supabase_run_table(config=config, request=effective_request)

        # Determine target column name for polling
        target_column_name: Optional[str] = None
        if effective_request.column_names and len(effective_request.column_names) > 0:
            target_column_name = effective_request.column_names[0]
        elif isinstance(run_data, dict):
            cols = run_data.get("columns_processed")
            if isinstance(cols, list) and len(cols) > 0 and isinstance(cols[0], str):
                target_column_name = cols[0]

        if not target_column_name:
            return None, "Unable to determine target column name for polling"

        attempts = 0
        last_data: Optional[Dict[str, Any]] = None

        def _extract_cells_from_column(col: Dict[str, Any], col_name: str) -> Tuple[List[Any], str]:
            cells_list: List[Any] = col.get("cells", []) or []
            source = "cells"
            if not cells_list and col_name in col and isinstance(col[col_name], list):
                cells_list = col[col_name]
                source = col_name
            if not cells_list:
                for k, v in col.items():
                    if isinstance(v, list):
                        cells_list = v
                        source = k
                        break
            return cells_list, source

        while attempts < max_attempts:
            # Fetch up to 5 rows for the target column, applying table-level filters
            table_req = TableDataRequest(
                table_id=effective_request.table_id,
                max_rows=5,
                column_names=[target_column_name],
                apply_table_filters=True,
                apply_table_sorts=True,
            )
            data, data_error = get_table_data(config=config, request=table_req)

            if data_error is not None:
                return None, data_error

            last_data = data
            # Expect a single column back (we requested one); fall back to first
            columns: List[Dict[str, Any]] = []
            if isinstance(data, dict) and isinstance(data.get("columns"), list):
                columns = data["columns"]
            if not columns:
                time.sleep(poll_interval_seconds)
                attempts += 1
                continue

            target_column: Dict[str, Any] = columns[0]
            cells, _cells_source = _extract_cells_from_column(target_column, target_column_name)
            statuses = []
            for cell in cells[:5]:
                run_val = None
                if isinstance(cell, dict):
                    if isinstance(cell.get("run_status"), dict):
                        run_val = cell["run_status"].get("run")
                    else:
                        run_val = cell.get("run")
                statuses.append(run_val)

            terminal_count = sum(1 for s in statuses if s in TERMINAL_STATUSES)
            target_count = min(5, len(cells))

            # Consider done when all of the first min(5, len(cells)) are terminal
            if target_count > 0 and terminal_count >= target_count:
                # Generate summary and schema for the completed column
                try:
                    summary_request = TableDataRequest(
                        table_id="",
                        max_rows=10,
                        column_names=[target_column_name],
                        row=False,
                        apply_table_filters=True,
                        apply_table_sorts=True,
                        include_summary=True
                    )
                    summary_response = read_table_data_tool.invoke({"request": summary_request}, config)
                    column_summary_text = None
                    column_schema = None
                    if getattr(summary_response, "success", False) and getattr(summary_response, "columns", None):
                        first_col = summary_response.columns[0]
                        first_col_dict = first_col.model_dump() if hasattr(first_col, "model_dump") else first_col
                        standard_keys = {"is_runnable", "column_run_status", "column_data_summary"}
                        # Prefer nested schema under the target column name
                        column_schema = first_col_dict.get(target_column_name)
                        if column_schema is None:
                            # Fallback: find the first non-standard key as schema container
                            for k, v in first_col_dict.items():
                                if k not in standard_keys:
                                    column_schema = v
                                    break
                        column_summary_text = first_col_dict.get("column_data_summary")

                    # Persist summary to DB (agent_description) for this column name
                    if column_summary_text:
                        _, _ = update_column_by_name(
                            config=config,
                            column_name=target_column_name,
                            fields={"agent_description": column_summary_text}
                        )

                    # Build minimal payload for the agent (no raw column data)
                    result_payload: Dict[str, Any] = {"column_name": target_column_name}
                    if column_summary_text:
                        result_payload["column_summary"] = column_summary_text
                    if column_schema is not None:
                        result_payload["column_schema"] = column_schema
                except Exception:
                    # Do not fail the run if summarization/storage hits an error
                    pass
                # Always return only the minimal payload (avoid raw columns)
                return result_payload, None

            time.sleep(poll_interval_seconds)
            attempts += 1

        return None, "Timeout: cells did not reach terminal status within the allotted attempts"

    except Exception as e:
        return None, str(e)


