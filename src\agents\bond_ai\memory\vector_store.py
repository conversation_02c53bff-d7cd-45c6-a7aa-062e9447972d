from __future__ import annotations

"""Simple in-memory vector store for agent memories.

This utility provides minimal read and write helpers that agents or the
supervisor can use to persist short summaries or embeddings.  It is intentionally
lightweight and does not perform any similarity search.  Entries are keyed by a
unique reference ID which can later be stored on the workflow state.
"""
# TODO: DOES IS PERSIST IN PROD? CAN WE USE A DB LIKE REDIS? DO WE NEED A FLUSH POLICY?
from dataclasses import dataclass
from typing import Dict, List, Optional
import uuid


@dataclass
class MemoryEntry:
    """Container for a single memory item."""
    agent: str
    content: str
    embedding: Optional[List[float]] = None


class MemoryVectorStore:
    """Very small in-memory store for agent memories.

    The store keeps memories in a dictionary keyed by a unique reference
    string.  It exposes ``write`` and ``read`` helpers so agents can persist and
    retrieve past context.  ``embedding`` is accepted for completeness but is
    unused by the current implementation.
    """

    def __init__(self) -> None:
        self._store: Dict[str, MemoryEntry] = {}

    # ------------------------------------------------------------------
    # Write API
    # ------------------------------------------------------------------
    def write(
        self, agent: str, content: str, embedding: Optional[List[float]] = None
    ) -> str:
        """Persist a memory for ``agent`` and return its reference ID."""
        ref = str(uuid.uuid4())
        self._store[ref] = MemoryEntry(agent=agent, content=content, embedding=embedding)
        return ref

    # ------------------------------------------------------------------
    # Read API
    # ------------------------------------------------------------------
    def read(self, agent: str, refs: Optional[List[str]] = None) -> List[str]:
        """Retrieve memory snippets for ``agent``.

        Args:
            agent: Name of the agent requesting memory.
            refs: Optional list of reference IDs to filter by.  If ``None`` all
                memories for the agent are returned.
        """
        if refs is None:
            refs = list(self._store.keys())

        snippets: List[str] = []
        for ref in refs:
            entry = self._store.get(ref)
            if entry and entry.agent == agent:
                snippets.append(entry.content)
        return snippets


# Global shared store used by the supervisor
memory_store = MemoryVectorStore()

__all__ = ["MemoryVectorStore", "memory_store", "MemoryEntry"]
