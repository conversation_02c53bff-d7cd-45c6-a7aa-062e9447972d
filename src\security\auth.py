import os
import logging
from typing import Dict, Any
from dotenv import load_dotenv
from supabase import create_client
from fastapi import Depends, HTTPException, Request
from fastapi.security import OAuth2PasswordBearer
from langgraph_sdk import Auth

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger("auth")

# Supabase configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_ANON_KEY = os.getenv("SUPABASE_KEY")  # This is the anon key
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")


oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


auth = Auth()


@auth.authenticate
async def get_supabase_user(authorization: str | None) -> Auth.types.MinimalUserDict:
    """Authenticate the user using Supabase and return the client if authorized."""

    if not authorization:
        raise Auth.exceptions.HTTPException(
            status_code=401, detail="Authorization header missing"
        )

    try:
        # Extract the token from the authorization header
        scheme, token = authorization.split()
        if scheme.lower() != "bearer":
            raise Auth.exceptions.HTTPException(
                status_code=401, detail="Invalid authorization format"
            )

        # Check if it's a service role key
        if token == SUPABASE_SERVICE_ROLE_KEY:
            # Create client with service role key for admin access
            supabase = create_client(SUPABASE_URL, token)
            return {"identity": "service_role", "supabase_client": supabase}

        # Create a Supabase client with the anon key
        supabase = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)

        # Verify the token by getting the user
        try:
            # This will throw an error if the token is invalid
            user_response = supabase.auth.get_user(token)
        except Exception as e:
            # If the error is about missing sub claim, provide a more helpful message
            error_str = str(e)
            if "missing sub claim" in error_str:
                logger.warning("Token missing sub claim")
                raise Auth.exceptions.HTTPException(
                    status_code=401,
                    detail="Invalid token format: missing required user identifier. Please use a valid Supabase JWT token.",
                )
            logger.error(f"Error validating token: {str(e)}")
            raise Auth.exceptions.HTTPException(
                status_code=401, detail=f"Invalid token: {str(e)}"
            )

        # Check if we got a valid user response
        if not user_response or not user_response.user:
            logger.warning("User response is empty")
            raise Auth.exceptions.HTTPException(
                status_code=401, detail="Invalid user token"
            )

        # Set the session with the user's token
        supabase.auth.set_session(token)

        # Return the user identity and the supabase client
        return {
            "identity": user_response.user.id,
            "supabase_client": supabase,
            "user": user_response.user,
        }

    except Auth.exceptions.HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        raise Auth.exceptions.HTTPException(
            status_code=500, detail=f"Authentication error: {str(e)}"
        )


async def get_current_user(
    request: Request, token: str = Depends(oauth2_scheme)
) -> Dict[str, Any]:
    """Authenticate the user using Supabase and return the client if authorized.

    This function is used as a FastAPI dependency to authenticate requests.

    Args:
        token: JWT token from the Authorization header

    Returns:
        Dict containing user identity and authenticated Supabase client

    Raises:
        HTTPException: If authentication fails
    """

    try:

        # Check if it's a service role key
        if token == SUPABASE_SERVICE_ROLE_KEY:
            # Create client with service role key for admin access
            supabase = create_client(SUPABASE_URL, token)
            request.state.user = {
                "identity": "service_role",
                "supabase_client": supabase,
            }
            return {"identity": "service_role", "supabase_client": supabase}

        # Create a Supabase client with the anon key
        supabase = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)

        # Verify the token by getting the user
        try:

            # This will throw an error if the token is invalid
            try:
                user_response = supabase.auth.get_user(token)
            except Exception as e:
                # If the error is about missing sub claim, provide a more helpful message
                error_str = str(e)
                if "missing sub claim" in error_str:
                    logger.warning("Token missing sub claim")
                    raise HTTPException(
                        status_code=401,
                        detail="Invalid token format: missing required user identifier. Please use a valid Supabase JWT token.",
                    )
                logger.error(f"Error validating token: {str(e)}")
                raise HTTPException(status_code=401, detail=f"Invalid token: {str(e)}")

            # Check if we got a valid user response
            if not user_response or not user_response.user:
                logger.warning("User response is empty")
                raise HTTPException(status_code=401, detail="Invalid user token")

            # Set the session with the user's token
            supabase.auth.set_session(token)
            request.state.user = {
                "identity": user_response.user.id,
                "user": user_response.user,
                "supabase_client": supabase,
            }
            # Return user info and the authenticated client
            return {
                "identity": user_response.user.id,
                "user": user_response.user,
                "supabase_client": supabase,
            }

        except Exception as e:
            logger.error(f"Token validation error: {str(e)}")
            raise HTTPException(status_code=401, detail=f"Invalid token: {str(e)}")

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Authentication error: {str(e)}")
