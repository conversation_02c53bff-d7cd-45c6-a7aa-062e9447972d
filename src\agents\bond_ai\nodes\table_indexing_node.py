
import json



from langchain_core.runnables import RunnableConfig

from bond_ai.configuration import Configuration
from bond_ai.state import BondAIWorkflowState


from ..tools.read_table_tools.read_table import read_table_data_tool
from ..supabase.read_table.models import TableDataRequest
from ..supabase.table.update_columns import update_column_by_id
from ..supabase.table.get_columns import get_columns_by_table_id



def table_indexing_node(state: BondAIWorkflowState, config: RunnableConfig):
    """Node that indexes table data and creates a summary for the agent."""
    configuration = Configuration.from_runnable_config(config)
 
    def update_column_description(column_id: str, description: str) -> bool:
        """Helper to update column description in database."""
       
        success, _ = update_column_by_id(config=config, column_id=int(column_id), fields={"agent_description": description})
        return success is not None
    
    def _normalize_key(name: str) -> str:
        return name.strip().lower().replace(" ", "_") if isinstance(name, str) else name
    
    try:
        # Get columns data
        columns_data, error = get_columns_by_table_id(configuration.table_id)
        
        #print(f"[Debug] Columns data: {columns_data}")
        if error:
            raise Exception(error)
        
        # Handle case when there are no columns
        if not columns_data:
            return {"table_summary": "There is no columns in the table yet"}
        
        # Find columns needing descriptions (only those without summaries)
        columns_needing_descriptions = [
            col for col in columns_data 
            if not col.get('agent_description') or not str(col.get('agent_description', '')).strip()
        ]
        
        # Process only columns that need descriptions
        if columns_needing_descriptions:
            column_names = [col['name'] for col in columns_data]
            norm_to_orig_summary = {_normalize_key(name): name for name in column_names}
            
            # Get structured analysis with summaries
            request = TableDataRequest(
                table_id="", max_rows=10, column_names=column_names, 
                row=False, apply_table_filters=True, apply_table_sorts=True, include_summary=True
            )
            response = read_table_data_tool.invoke({"request": request}, config)
            
            if not response.success or not response.columns:
                raise Exception(f"Failed to analyze table data: {response.error_message or 'No column data returned'}")
            
            # Create name-to-id mapping
            name_to_id = {col['name']: col['id'] for col in columns_data}
            
            # Process each column from response
            for idx, column_summary in enumerate(response.columns):
                summary_dict = column_summary.model_dump() if hasattr(column_summary, 'model_dump') else column_summary
                
                # Find column name (exclude standard keys)
                standard_keys = {'is_runnable', 'column_run_status', 'column_data_summary'}
                column_name = next((key for key in summary_dict.keys() if key not in standard_keys), 
                                 column_names[idx] if idx < len(column_names) else None)
                
                original_name = norm_to_orig_summary.get(column_name, column_name) if column_name else None
                column_id = name_to_id.get(original_name) if original_name else None
                
                if not column_id:
                    raise Exception(f"Could not match column '{column_name}' to database ID")
                
                # Extract only the summary from the column data
                summary_text = summary_dict.get('column_data_summary', 'Summary not available')
                
                if not update_column_description(column_id, summary_text):
                    raise Exception(f"Failed to update description for column '{original_name}' (ID: {column_id})")
                
                # Update local data with just the summary
                for col in columns_data:
                    if col['id'] == column_id:
                        col['agent_description'] = summary_text
                        break
        
        # Always fetch fresh schema data for the final table summary
        column_names = [col['name'] for col in columns_data]
        request = TableDataRequest(
            table_id="", max_rows=10, column_names=column_names, 
            row=False, apply_table_filters=True, apply_table_sorts=True, include_summary=False
        )
        fresh_response = read_table_data_tool.invoke({"request": request}, config)
        
        if not fresh_response.success or not fresh_response.columns:
            raise Exception(f"Failed to get fresh table data: {fresh_response.error_message or 'No column data returned'}")
        
        # Build structured output: table_metadata and table_schema
        name_to_description = {col['name']: col.get('agent_description', '') for col in columns_data}
        name_to_id = {col['name']: col['id'] for col in columns_data}
        # Map normalized -> original names to align schema keys (normalized) with DB metadata (original)
        norm_to_orig = {_normalize_key(name): name for name in column_names}

        table_metadata = []
        table_schema = {}

        for idx, column_schema in enumerate(fresh_response.columns):
            schema_dict = column_schema.model_dump() if hasattr(column_schema, 'model_dump') else column_schema

            # Find column name (exclude standard keys)
            standard_keys = {'is_runnable', 'column_run_status', 'column_data_summary'}
            column_name = next((key for key in schema_dict.keys() if key not in standard_keys), 
                             column_names[idx] if idx < len(column_names) else None)

            if not column_name:
                continue

            # Extract only nested schema under the column key
            column_nested_schema = schema_dict.get(column_name, {})

            # Build metadata for this column
            original_name = norm_to_orig.get(column_name, column_name)
            metadata_entry = {
                'column_name': original_name,
                'column_id': name_to_id.get(original_name),
                'data_summary': name_to_description.get(original_name, 'No summary available'),
                'is_runnable': schema_dict.get('is_runnable', False)
            }

            if metadata_entry['is_runnable'] and 'column_run_status' in schema_dict:
                metadata_entry['column_run_status'] = schema_dict['column_run_status']

            table_metadata.append(metadata_entry)
            table_schema[column_name] = column_nested_schema

        # Ensure all columns have descriptions
        for col in columns_data:
            if not col.get('agent_description'):
                raise Exception(f"Column '{col['name']}' (ID: {col['id']}) has no description after processing")
        
        return {"table_summary": {"table_metadata": table_metadata, "table_schema": table_schema}}
        
    except Exception as e:
        raise Exception(f"Table indexing failed: {str(e)}")
