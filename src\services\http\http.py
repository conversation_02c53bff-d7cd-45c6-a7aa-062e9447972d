"""HTTP service implementation following Lima architecture patterns."""
import logging
from typing import Optional, Dict, Any
import httpx
from src.core.config import get_settings
from src.services.http.models import HTTPRequest, HTTPResponse, HTTPMethod

logger = logging.getLogger(__name__)


class HTTPServiceException(Exception):
    """Base exception for HTTP service errors."""
    
    def __init__(self, message: str, status_code: Optional[int] = None):
        self.message = message
        self.status_code = status_code
        super().__init__(message)


class HTTPTimeoutException(HTTPServiceException):
    """Exception for HTTP timeout errors."""
    pass


class HTTPRateLimitException(HTTPServiceException):
    """Exception for HTTP rate limit errors."""
    
    def __init__(self, message: str, retry_after: Optional[str] = None):
        super().__init__(message, status_code=429)
        self.retry_after = retry_after


async def make_http_request(request: HTTPRequest) -> HTTPResponse:
    """
    Make HTTP request following Lima service patterns.
    
    Args:
        request: HTTPRequest model with URL, method, params, body, headers
        
    Returns:
        HTTPResponse model with status code, headers, and body/json
        
    Raises:
        HTTPServiceException: For HTTP errors
        HTTPTimeoutException: For timeout errors
        HTTPRateLimitException: For rate limit errors
    """
    settings = get_settings()
    
    # Handle stress testing mode like Lima service
    if settings.IS_STRESS_TESTING:
        logger.info("Stress testing mode - returning mock HTTP response")
        import asyncio
        await asyncio.sleep(1)  # Shorter delay than Lima's 30s
        return HTTPResponse(
            status_code=200,
            headers={"content-type": "application/json"},
            json={"message": "Mock HTTP response for testing"}
        )
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            # Prepare request kwargs
            request_kwargs = {
                "url": str(request.url),
                "method": request.method.value,
                "headers": request.headers or {},
            }
            
            # Add params if provided
            if request.params:
                request_kwargs["params"] = request.params
                
            # Add body/json if provided
            if request.body:
                if request.method in [HTTPMethod.POST, HTTPMethod.PUT, HTTPMethod.PATCH]:
                    # Determine content type
                    content_type = request.headers.get("content-type", "") if request.headers else ""
                    if "application/json" in content_type.lower():
                        request_kwargs["json"] = request.body
                    else:
                        request_kwargs["data"] = request.body
                else:
                    logger.warning(f"Body provided for {request.method} request, ignoring")
            
            logger.info(f"Making HTTP {request.method} request to {request.url}")
            
            # Make the HTTP request
            response = await client.request(**request_kwargs)
            
            # Parse response headers
            response_headers = dict(response.headers)
            
            # Parse response body
            response_body = None
            response_json = None
            
            if response.content:
                response_body = response.text
                
                # Try to parse as JSON if content-type suggests it
                content_type = response_headers.get("content-type", "")
                if "application/json" in content_type.lower():
                    try:
                        response_json = response.json()
                    except Exception as e:
                        logger.warning(f"Failed to parse JSON response: {e}")
            
            # Handle different status codes like Lima service
            if response.status_code == 429:
                retry_after = response_headers.get("retry-after")
                raise HTTPRateLimitException(
                    f"Rate limit exceeded for {request.url}",
                    retry_after=retry_after
                )
            elif response.status_code == 404:
                raise HTTPServiceException(
                    f"Resource not found: {request.url}",
                    status_code=404
                )
            elif response.status_code >= 400:
                raise HTTPServiceException(
                    f"HTTP {response.status_code} error for {request.url}: {response_body}",
                    status_code=response.status_code
                )
            
            logger.info(f"HTTP request successful: {response.status_code}")
            
            return HTTPResponse(
                status_code=response.status_code,
                headers=response_headers,
                body=response_body,
                json=response_json
            )
            
    except httpx.TimeoutException as e:
        logger.error(f"HTTP timeout for {request.url}: {e}")
        raise HTTPTimeoutException(f"Request timeout for {request.url}")
    except httpx.RequestError as e:
        logger.error(f"HTTP request error for {request.url}: {e}")
        raise HTTPServiceException(f"Request failed for {request.url}: {str(e)}")
    except HTTPServiceException:
        # Re-raise our custom exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error making HTTP request to {request.url}: {e}")
        raise HTTPServiceException(f"Unexpected error: {str(e)}")


async def get_request(url: str, headers: Optional[Dict[str, str]] = None, 
                     params: Optional[Dict[str, Any]] = None) -> HTTPResponse:
    """Convenience function for GET requests."""
    request = HTTPRequest(
        url=url,
        method=HTTPMethod.GET,
        headers=headers,
        params=params
    )
    return await make_http_request(request)


async def post_request(url: str, body: Optional[Dict[str, Any]] = None,
                      headers: Optional[Dict[str, str]] = None) -> HTTPResponse:
    """Convenience function for POST requests."""
    # Set JSON content type if body provided and no content-type set
    if body and headers and "content-type" not in [k.lower() for k in headers.keys()]:
        headers["content-type"] = "application/json"
    elif body and not headers:
        headers = {"content-type": "application/json"}
        
    request = HTTPRequest(
        url=url,
        method=HTTPMethod.POST,
        headers=headers,
        body=body
    )
    return await make_http_request(request)


async def put_request(url: str, body: Optional[Dict[str, Any]] = None,
                     headers: Optional[Dict[str, str]] = None) -> HTTPResponse:
    """Convenience function for PUT requests."""
    if body and headers and "content-type" not in [k.lower() for k in headers.keys()]:
        headers["content-type"] = "application/json"
    elif body and not headers:
        headers = {"content-type": "application/json"}
        
    request = HTTPRequest(
        url=url,
        method=HTTPMethod.PUT,
        headers=headers,
        body=body
    )
    return await make_http_request(request)


async def delete_request(url: str, headers: Optional[Dict[str, str]] = None) -> HTTPResponse:
    """Convenience function for DELETE requests."""
    request = HTTPRequest(
        url=url,
        method=HTTPMethod.DELETE,
        headers=headers
    )
    return await make_http_request(request)