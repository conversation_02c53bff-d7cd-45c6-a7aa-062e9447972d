"""Database operations for fetching table metadata."""

from typing import Dict, Optional, Tuple, Any
from ..client import supabase


def get_table_metadata_by_id(table_id: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
    """Fetch table metadata by table ID from Supabase.
    
    Args:
        table_id (str): The unique identifier of the table to retrieve metadata for
        
    Returns:
        Tuple[Optional[Dict[str, Any]], Optional[str]]: Tuple containing (data, error)
        where data is the table metadata if successful, None if failed
        and error is the error message if failed, None if successful
        
    Example:
        ```python
        metadata, error = get_table_metadata_by_id("tbl_c85117eb4106d464")
        if error:
            print(f"Error: {error}")
        else:
            print(f"Table name: {metadata['name']}")
        ```
    """
    try:
        # Validate input
        if not table_id or not isinstance(table_id, str):
            return None, "Invalid table_id: must be a non-empty string"
        
        # Query the tables table with filter by table ID
        query = supabase.table('tables').select("*").eq('id', table_id)

        response = query.execute()
        
        # Check if response has data
        if not hasattr(response, 'data') or response.data is None:
            return None, "Server Error: Data not available"
        
        # Check if table was found
        if len(response.data) == 0:
            return None, f"Table with ID '{table_id}' not found"
        
        # Return the first (and should be only) result
        table_metadata = response.data[0]
        return table_metadata, None
            
    except Exception as e:
        error_msg = f"Error fetching table metadata: {str(e)}"
        print(error_msg)
        return None, error_msg