from src.schemas.requests import ServiceRequest
from src.db.utils import CellStatus, RealtimeEvent, get_supabase_client, get_cell, CellId, send_realtime_broadcast, update_cell
from supabase import AsyncClient
import logging
from src.services.llm.generation import _handle_formula_condition

logger = logging.getLogger("backend_api.services.outbond.invite")
async def invite(request):
    try:
        supabase_client = await get_supabase_client()
        logger.info(f"Inviting user: {request['value']['email']}")
        cell = await get_cell(
            supabase_client,
            CellId(
                column_id=request["column_id"],
                row_id=request["row_id"],
                table_id=request["table_id"],
            ),
        )
        if not await _handle_formula_condition(ServiceRequest(**request), cell, supabase_client):
            return
        cell.run_status = CellStatus(run="processing", message="Sending Invitation...")
        await send_realtime_broadcast(
            supabase_client,
            request["table_id"],
            RealtimeEvent.CELL_UPDATE,
            cell.model_dump(),
        )
        response = await supabase_client.auth.admin.invite_user_by_email(
            request["value"]["email"],
            options={"data": {"display_name": request["value"].get("display_name")}},
        )
        # message with success emoji
        cell.run_status = CellStatus(run="success", message="✅ Invitation sent successfully")
        await send_realtime_broadcast(
            supabase_client,
            request["table_id"],
            RealtimeEvent.CELL_UPDATE,
            cell.model_dump(),
        )
        await update_cell(supabase_client, cell)

    except Exception as e:
        error_message = str(e)
        # Check if user already exists
        if "User already registered" in error_message:
            cell.run_status = CellStatus(run="failed", message="❌ User already exists")
            await send_realtime_broadcast(
                supabase_client,
                request["table_id"],
                RealtimeEvent.CELL_UPDATE,
                cell.model_dump(),
            )
            await update_cell(supabase_client, cell)
            return
        cell.run_status = CellStatus(run="failed", message="❌ Error inviting user")
        await send_realtime_broadcast(
            supabase_client,
            request["table_id"],
            RealtimeEvent.CELL_UPDATE,
            cell.model_dump(),
        )
        await update_cell(supabase_client, cell)
        raise
