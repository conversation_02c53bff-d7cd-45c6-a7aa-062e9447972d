from langchain_core.messages import HumanMessage, AIMessage, BaseMessage


def get_newest_conversation_messages_filtered_ai(
    state: dict,
    n_human: int = 3,
    n_ai: int = 1,
    ai_name: str | None = None,
) -> list[BaseMessage]:
    """Get the most recent human + (optionally filtered) AI messages, newest first.

        Example:
            Messages in state (chronological):
                Human1   (oldest)
                AI1
                Human2
                Human3
                AI2
                Human4
                AI3     (newest)

            get_newest_conversation_messages_filtered_ai(state, n_human=3, n_ai=1)
            returns:
                [AI3, Human4, Human3, Human2]

            get_newest_conversation_messages_filtered_ai(state, n_human=3, n_ai=2)
            returns:
                [AI3, Human4, AI2, Human3, Human2]
        """

    messages = state.get("messages") or []
    res: list[BaseMessage] = []
    h = a = 0

    for m in reversed(messages):
        if isinstance(m, HumanMessage) and h < n_human:
            res.append(m); h += 1
        elif isinstance(m, AIMessage) and a < n_ai:
            if ai_name is None or getattr(m, "name", None) == ai_name:
                res.append(m); a += 1
        if h >= n_human and a >= n_ai:
            break

    return res  # keep newest → oldest (no reverse)




def get_newest_n_human_messages(state: dict, n: int = 3) -> list[HumanMessage]:
    """Extract the n most recent human messages, newest first."""
    human_messages = [m for m in (state.get("messages") or []) if isinstance(m, HumanMessage)]
    return human_messages[-n:][::-1]

def get_newest_ai_messages(state: dict, n: int = 1) -> list[AIMessage]:
    """Extract the n most recent AI messages, newest first."""
    ai_messages = [m for m in (state.get("messages") or []) if isinstance(m, AIMessage)]
    return ai_messages[-n:][::-1]

def get_newest_conversation_messages(state: dict, n_human: int = 3, n_ai: int = 1) -> list[BaseMessage]:
    """Get the most recent human + AI messages, newest first."""
    messages = state.get("messages") or []
    res: list[BaseMessage] = []
    h = a = 0

    for msg in reversed(messages):
        if isinstance(msg, HumanMessage) and h < n_human:
            res.append(msg); h += 1
        elif isinstance(msg, AIMessage) and a < n_ai:
            res.append(msg); a += 1
        if h >= n_human and a >= n_ai:
            break

    return res  # newest → oldest