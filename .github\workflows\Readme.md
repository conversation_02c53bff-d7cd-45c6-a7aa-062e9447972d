# GitHub Actions Workflow for AWS ECS Deployment

## Overview
This repository contains a GitHub Actions workflow that automates building Docker images, pushing them to AWS Elastic Container Registry (ECR), and deploying services to AWS Elastic Container Service (ECS).

## Workflow Triggers
The workflow is triggered on push to the following branches:
- `c303-multidocker-dev` (deploys to **Development** environment)
- `dev` (deploys to **Development** environment)
- `main` (deploys to **Production** environment)

## ECS Services Deployed
The workflow deploys the following components using separate Docker images:

1. A **FastAPI** backend (using `Dockerfile.API`)
2. Three **Celery workers** (using `Dockerfile.WORKERS`):
   - `worker-default`
   - `worker-enrichment`
   - `worker-llm`
3. A **Flower** dashboard for monitoring Celery (using the same worker image)

## Workflow Steps

1. **Checkout Code**
   - Uses `actions/checkout@v3` to pull the latest code.

2. **Set Environment Variables**
   - Based on the branch, sets environment-specific values:
     - If `dev` or `c303-multidocker-dev` branch → Production environment
     - If `main` branch → Production environment

3. **AWS Authentication**
   - Uses `aws-actions/configure-aws-credentials@v2`
   - Requires secrets:
     - `AWS_ACCESS_KEY_ID`
     - `AWS_SECRET_ACCESS_KEY`

4. **Login to Amazon ECR**
   - Authenticates Docker with ECR using `aws-actions/amazon-ecr-login@v2`.

5. **FastAPI Service Deployment**
   - Builds Docker image using `Dockerfile.API`
   - Tags with:
     - `latest`
     - `release-<short-commit-hash>`
   - Pushes both tags to ECR repository: `outbond-ecr-fastapi`
   - Updates FastAPI service task definition and deployment
   - Waits for FastAPI service to stabilize

6. **Workers Service Deployment**
   - Builds Docker image using `Dockerfile.WORKERS`
   - Tags with:
     - `latest`
     - `release-<short-commit-hash>`
   - Pushes both tags to ECR repository: `outbond-ecr-workers`
   - Updates all Celery worker services with the new image
   - Waits for all worker services to stabilize

7. **Flower Dashboard Deployment**
   - Uses the same worker image built in the previous step
   - Updates Flower dashboard task definition
   - Updates ECS Flower service
   - Waits for Flower service to stabilize

## Environment Variables Used

| Variable               | Dev/c303-multidocker           | Main (Production)              |
|------------------------|---------------------------------|-------------------------------|
| `ECS_CLUSTER`          | `outbond-dev-ecs-cluster`     | `outbond-prod-ecs-cluster`    |
| `AWS_REGION`           | `us-east-1`                    | `us-east-1`                   |
| `ECR_REPOS`            | `outbond-ecr-fastapi`          | `outbond-ecr-fastapi`         |
| `ECR_REPOS_WORKERS`    | `outbond-ecr-workers`          | `outbond-ecr-workers`         |
| `ECS_SERVICES_API`     | `fastapi`                      | `fastapi`                     |
| `ECS_TASK_DEF_API`     | `fastapi-service`              | `fastapi-service`             |
| `ECS_SERVICES_WORKERS` | `worker-default,worker-enrichment,worker-llm` | `worker-default,worker-enrichment,worker-llm` |
| `ECS_TASK_DEF_WORKERS` | `worker-default-service,worker-enrichment-service,worker-llm-service` | `worker-default-service,worker-enrichment-service,worker-llm-service` |
| `ECS_SERVICE_FLOWER`   | `flower`                       | `flower`                      |
| `ECS_TASK_DEF_FLOWER`  | `flower-service`               | `flower-service`              |
| `DESIRED_TASKS`        | `1`                            | `1`                           |
| `DESIRED_TASKS_CELERY` | `1`                            | `1`                           |
| `DESIRED_TASKS_FLOWER` | `1`                            | `1`                           |
| `AWS_ACCOUNT_ID`       | `************`                 | `************`                |

## Required GitHub Secrets

- `AWS_ACCESS_KEY_ID`
- `AWS_SECRET_ACCESS_KEY`

## Prerequisites

- Docker installed
- AWS CLI installed and configured
- `jq` installed for JSON processing

## Deployment Guide

1. Push changes to one of the designated branches:
   - `dev` or `c303-multidocker-dev` → deploys to Production
   - `main` → deploys to Production
2. GitHub Actions will:
   - Build the FastAPI image using `Dockerfile.API`
   - Push it to ECR repository: `outbond-ecr-fastapi`
   - Deploy FastAPI service and wait for stability
   - Build the Workers image using `Dockerfile.WORKERS`
   - Push it to ECR repository: `outbond-ecr-workers`
   - Deploy Celery workers and wait for stability
   - Deploy Flower dashboard using the same workers image
   - Ensure all services are stable