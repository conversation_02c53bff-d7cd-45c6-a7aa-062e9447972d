"""Define the state structures for the agent."""

from __future__ import annotations
from typing import Annotated, List, Sequence, TypedDict, Optional
from langgraph.prebuilt.chat_agent_executor import AgentState
from bond_ai.models.planner_model import Task
# State reducers
def tasks_reducer(current: List[Task], new: List[Task]) -> List[Task]:
    """Reducer for tasks list - merge by ID, keeping latest version."""
    if not current:
        return new
    if not new:
        return current
    
    # Create a dict for efficient lookup
    current_dict = {task.id: task for task in current}
    
    # Update with new tasks
    for task in new:
        current_dict[task.id] = task
    
    return list(current_dict.values())

def add_tool_calls(existing: Optional[List[dict]], new: Optional[List[dict]]) -> List[dict]:
    """Reducer that accumulates tool calls instead of replacing them."""
    if existing is None:
        existing = []
    if new is None:
        new = []
    return existing + new

def preserve_table_summary(existing: Optional[str], new: Optional[str]) -> Optional[str]:
    """Preserve existing table summary unless explicitly updated."""
    return new if new is not None else existing


def add_memory_refs(existing: Optional[List[str]], new: Optional[List[str]]) -> List[str]:
    """Reducer to accumulate memory reference IDs.

    Ensures that previously stored references are preserved and that new
    references are appended without duplication."""
    if existing is None:
        existing = []
    if not new:
        return existing
    # maintain insertion order while avoiding duplicates
    existing_set = set(existing)
    existing.extend([ref for ref in new if ref not in existing_set and not existing_set.add(ref)])
    return existing




class BondAIWorkflowState(AgentState):
    """The state of the workflow.
    This defines data persistent trought the entire workdlow
    """
   # messages: Annotated[Sequence[BaseMessage], add_messages]   using Agent State
    table_summary: Annotated[Optional[str], preserve_table_summary]
    mode: Optional[str]
    selected_row_ids: Optional[int]
    selected_column_ids: Optional[str]
    intents: Optional[List[str]]
    tool_calls: Annotated[Optional[List[dict]], add_tool_calls]
    
    # Planning and execution
    plan_tasks: Annotated[List[Task], tasks_reducer]
    #plan: Optional[Plan]
    active_task_id: Optional[str]   # ID of currently executing task
    
    #supervisor
    next: Optional[str]   # Next node to run

    #error handling
    last_error_message: Optional[str]

    ##agent direct response (TODO to implement the case a subagent respose to the user directly)
    sender_agent_name: Optional[str]
    sender_message: Optional[str]
    # conversation_context: dict[str, RunningSummary] 
    # Memory tracking - references to stored memory entries
    memory_refs: Annotated[List[str], add_memory_refs] = []  # Remove Optional, default to empty list

    summary: str  ## Conversation summary