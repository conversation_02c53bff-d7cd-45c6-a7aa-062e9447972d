FROM langchain/langgraph-api:3.11



# -- Adding local package . --
ADD . /deps/outbond-fastapi
# -- End of local package . --

# -- Installing all local dependencies --
RUN PYTHONDONTWRITEBYTECODE=1 pip install --no-cache-dir -c /api/constraints.txt -e /deps/*
# -- End of local dependencies install --
ENV LANGGRAPH_HTTP='{"app": "/deps/outbond-fastapi/src/api/main.py:app"}'
ENV LANGSERVE_GRAPHS='{"bond_ai_v_0_1": "/deps/outbond-fastapi/src/agents/bond_ai/graph.py:graph", "agent": "/deps/outbond-fastapi/src/agent/graph.py:graph", "outbond_ai_assistant": "/deps/outbond-fastapi/src/outbond_ai_assistant/graph.py:graph"}'



# -- Ensure user deps didn't inadvertently overwrite langgraph-api
RUN mkdir -p /api/langgraph_api /api/langgraph_runtime /api/langgraph_license &&     touch /api/langgraph_api/__init__.py /api/langgraph_runtime/__init__.py /api/langgraph_license/__init__.py
RUN PYTHONDONTWRITEBYTECODE=1 pip install --no-cache-dir --no-deps -e /api
# -- End of ensuring user deps didn't inadvertently overwrite langgraph-api --
# -- Removing pip from the final image ~<:===~~~ --
RUN pip uninstall -y pip setuptools wheel &&     rm -rf /usr/local/lib/python*/site-packages/pip* /usr/local/lib/python*/site-packages/setuptools* /usr/local/lib/python*/site-packages/wheel* &&     find /usr/local/bin -name "pip*" -delete
# -- End of pip removal --

WORKDIR /deps/outbond-fastapi