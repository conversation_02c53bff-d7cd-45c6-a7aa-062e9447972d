"""Define the state structures for the agent."""

from __future__ import annotations

from typing import Annotated, Sequence, TypedDict, Optional

from langchain_core.messages import BaseMessage
from langgraph.graph.message import add_messages


def preserve_table_summary(left: Optional[str], right: Optional[str]) -> Optional[str]:
    """Preserve table_summary, prioritizing the most recent non-None value."""
    if right is not None:
        return right
    return left


class AgentState(TypedDict):
    """The state of the agent.
    
    This defines the structure of data flowing through the ReAct agent.
    """
    # add_messages is a reducer that combines message lists
    # See https://langchain-ai.github.io/langgraph/concepts/low_level/#reducers
    messages: Annotated[Sequence[BaseMessage], add_messages]
    
    # Table summary information for quick reference - preserved across nodes
    table_summary: Annotated[Optional[str], preserve_table_summary]
    mode: Optional[str]
    selected_row_ids: Optional[int]
    selected_column_ids: Optional[str]
