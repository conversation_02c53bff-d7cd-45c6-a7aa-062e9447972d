version: "3.8"

# Define common environment variables as a YAML anchor
x-common-env: &common-env
  environment:
    - ENV=${ENV}
    - DEBUG=${DEBUG}
    - LANGSMITH_PROJECT=${LANGSMITH_PROJECT}
    - LANGSMITH_API_KEY=${LANGSMITH_API_KEY}
    - SUPABASE_URL=${SUPABASE_URL}
    - SUPABASE_KEY=${SUPABASE_KEY}
    - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
    - REDIS_URL=${REDIS_URL}
    - CELERY_BROKER_URL=${CELERY_BROKER_URL}
    - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
    - POSTGRES_URL=${POSTGRES_URL}
    - OPENAI_API_KEY=${OPENAI_API_KEY}
    - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    - FIREWORKS_API_KEY=${FIREWORKS_API_KEY}
    - PROXYCURL_API_KEY=${PROXYCURL_API_KEY}
    - LEADMAGIC_API_KEY=${LEADMAGIC_API_KEY}
    - PROSPEO_API_KEY=${PROSPEO_API_KEY}
    - FINDYMAIL_API_KEY=${FINDYMAIL_API_KEY}
    - MILLIONVERIFIER_API_KEY=${MILLIONVERIFIER_API_KEY}
    - RUN_ONLY_IF_MODEL=${RUN_ONLY_IF_MODEL}
    - AI_COLUMN_MODEL=${AI_COLUMN_MODEL}
    - AI_FORMULA_MODEL=${AI_FORMULA_MODEL}
    - AI_RESEARCH_MODEL=${AI_RESEARCH_MODEL}
    - TAVILY_API_KEY=${TAVILY_API_KEY}
    - FIRECRAWL_API_KEY=${FIRECRAWL_API_KEY}
    - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
    - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}

# Worker services common configuration
x-worker-service: &worker-service
  build:
    context: .
    dockerfile: Dockerfile.WORKERS
  networks:
    - outbond-network
  env_file:
    - .env
  <<: *common-env
  restart: unless-stopped

services:
  # FastAPI Application with LangGraph
  api:
    build:
      context: .
      dockerfile: Dockerfile.API
    networks:
      - outbond-network
    env_file:
      - .env
    environment:
      REDIS_URI: ${REDIS_URL}
      DATABASE_URI: ${POSTGRES_URL}
      LANGSMITH_API_KEY: ${LANGSMITH_API_KEY}
      LANGSMITH_PROJECT: ${LANGSMITH_PROJECT}
    restart: unless-stopped
    ports:
      - "8001:8000"

  # Celery Worker - Default Queue
  worker-default:
    <<: *worker-service
    command: worker-default

  # Celery Worker - Enrichment Queue
  worker-enrichment:
    <<: *worker-service
    command: worker-enrichment

  # Celery Worker - LLM Queue
  worker-llm:
    <<: *worker-service
    command: worker-llm

  # Flower - Celery Monitoring
  flower:
    <<: *worker-service
    command: flower
    ports:
      - "5555:5555"
    depends_on:
      - worker-default
      - worker-enrichment
      - worker-llm

networks:
  outbond-network:
    driver: bridge
