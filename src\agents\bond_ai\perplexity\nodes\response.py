"""
The summarize node is responsible for summarizing the information.
"""

import json
from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.runnables import RunnableConfig
from langchain.tools import tool
from pydantic import BaseModel, Field
from bond_ai.perplexity.state import AgentState
from bond_ai.perplexity.model import get_model

class Reference(BaseModel):
    """Model for a reference"""

    title: str = Field(description="The title of the reference.")
    url: str = Field(description="The url of the reference.")

class SummarizeInput(BaseModel):
    """Input for the summarize tool"""
    markdown: str = Field(description="""
                          The markdown formatted summary of the final result.
                          If you add any headings, make sure to start at the top level (#).
                          """)
    references: list[Reference] = Field(description="A list of references.")

@tool(args_schema=SummarizeInput)
def ResponseTool(summary: str, references: list[Reference]): # pylint: disable=invalid-name,unused-argument
    """
    Prepare the final result. Make sure that the summary is complete and 
    includes all relevant information and reference links.
    """


async def response_node(state: AgentState, config: RunnableConfig):
    """
    The response node is responsible for summarizing and formatting the information.
    """


    system_message = f"""
The system has performed a series of steps to answer the user's query.
These are all of the steps: {json.dumps(state["steps"])}

Create a comprehensive Sales Research Document (SRD) summary based on the research conducted.

IF the research focused on ICP (Ideal Customer Profile) analysis, use this format:
- Pain points
- Cost of inaction
- Objections
- Social Proof
- Competitive advantages
- Solutions

FOR other SRD activities, organize findings under relevant sections such as:
- Market Intelligence (industry trends, market size, growth)
- Buyer Persona Insights (decision makers, buying behaviors)
- Competitive Analysis (market positioning, competitors)
- Market Signals (funding, expansions, technology adoptions)
- Company Intelligence (firmographics, recent developments)
- Opportunity Assessment (sales potential, timing indicators)

Analyze the research steps to determine the appropriate format and structure your summary accordingly. Include all relevant information and reference links.
"""

    response = await get_model(state).bind_tools(
        [ResponseTool],
        tool_choice="ResponseTool"
    ).ainvoke([
        HumanMessage(
            content=system_message
        ),
    ], config)

    return {
        "messages": [AIMessage(content=response.tool_calls[0]["args"]["markdown"], name="perplexity_agent")],
        "answer": response.tool_calls[0]["args"],
        "next": "FINISH"
    }
