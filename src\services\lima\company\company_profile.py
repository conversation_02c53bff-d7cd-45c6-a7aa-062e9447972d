import asyncio
from typing import List, Dict, Any
from src.core.config import get_settings
import httpx
import logging
from src.db.utils import get_type_id, get_type_name, format_date
from src.services.lima.company.schemas import (
    LimaCompanyProfileOutput,
    LimaCompanyProfileResponse,
    LimaCompanyProfileInput,
    LimaCompanyHeadquarters,
)

settings = get_settings()
logger = logging.getLogger(__name__)


# Custom exceptions
class RateLimitException(Exception):
    def __init__(self, retry_after=None, message="Rate limit exceeded"):
        self.retry_after = retry_after
        self.message = message
        super().__init__(self.message)



async def get_company_profile(input: LimaCompanyProfileInput) -> LimaCompanyProfileResponse:
    """Fetch LinkedIn company profile data from the Lima API

    Args:
        input: LimaCompanyProfileInput with linkedin_company_url

    Returns:
        LimaCompanyProfileResponse with profile data and credit cost
    """
    try:
        if settings.IS_STRESS_TESTING:
            # wait for 30 seconds
            await asyncio.sleep(30)
            return LimaCompanyProfileResponse(
                profile=LimaCompanyProfileOutput(
                    id="1234567890",
                    name="Outbond",
                    profile_url="https://linkedin.com/company/outbond",
                    website="https://outbond.io",
                    tagline="Outbond",
                    description="Outbond",
                    employee_count=100,
                    employee_count_range="51-200",
                    follower_count=1000,
                    founded_year="2020",
                    industries=["Technology"],
                    specialities=["AI", "Automation"],
                    profile_image_url=settings.TESTING_LINKEDIN_PROFILE_PICTURE_URL,
                    cover_image_url="https://example.com/cover.jpg",
                    headquarters=LimaCompanyHeadquarters(
                        city="San Francisco",
                        state="CA",
                        country="US"
                    )
                ),
                credit_cost=0,
            )
        async with httpx.AsyncClient() as client:
            response = await client.get(
                "https://api.limadata.com/api/v1/company",
                params={
                    "url": input.linkedin_company_url
                },
                headers={
                    "X-API-KEY": f"{settings.LIMA_API_KEY.get_secret_value()}"
                },
                timeout=60.0,  # 60 second timeout
            )
            response.raise_for_status()

            # Extract credit cost from headers
            credit_cost = 0
            if "x-credits-cost" in response.headers:
                try:
                    credit_cost = int(response.headers["x-credits-cost"])
                except (ValueError, TypeError):
                    # If header exists but can't be converted to int
                    logger.warning(
                        f"Could not parse credit cost from header: {response.headers.get('x-credits-cost')}"
                    )

            # Parse the response JSON
            profile_data = response.json()

            # Create the CompanyProfileOutput object
            profile = LimaCompanyProfileOutput(**profile_data)

            # Return the response with profile and credit cost
            return LimaCompanyProfileResponse(
                profile=profile,
                credit_cost=credit_cost,
            )

    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            logger.warning("Company profile not found")
            raise ValueError("Company profile not found")
        elif e.response.status_code == 429:
            logger.warning("Rate limit exceeded")
            # Extract retry-after header if available
            retry_after = e.response.headers.get("Retry-After")
            raise RateLimitException(
                retry_after=retry_after, message="Rate limit exceeded. Try again later."
            )
        else:
            logger.error(f"HTTP error: {e.response.status_code} - {e.response.text}")
            raise e
    except Exception as e:
        logger.error(f"Error fetching company profile: {str(e)}")
        raise e

