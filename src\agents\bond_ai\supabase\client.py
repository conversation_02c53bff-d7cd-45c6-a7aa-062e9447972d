"""Supabase client initialization and configuration."""

from supabase import Client, create_client
import os
from dotenv import load_dotenv

# Load environment variables from .env file, force override existing ones
load_dotenv(override=True)

# Initialize Supabase client
url = os.environ.get("SUPABASE_URL")
key = os.environ.get("SUPABASE_SERVICE_ROLE_KEY")

if not url or not key:
    raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set in environment variables")

try:
    supabase: Client = create_client(url, key)
    print("Supabase client created successfully")
except Exception as e:
    print(f"Error creating Supabase client: {str(e)}")
    raise
