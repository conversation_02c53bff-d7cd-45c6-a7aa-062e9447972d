from .celery_app import celery_app
from src.backend_server.db.cells import get_supabase_client
from src.backend_server.models.requests import (
    AIColumnRequest,
    FormulaRequest,
    ResearchAgentRequest,
    LinkedinProfileRequest,
)
from src.backend_server.services.llm import (
    run_ai_formula,
    run_ai_column,
    run_research_agent,
)
from supabase import Client
from typing import Union, Dict, Any
import logging

# Import centralized configuration
from src.config import get_settings

# Get application settings
settings = get_settings()

logger = logging.getLogger("backend_api.tasks.openai")


@celery_app.task(
    name="src.backend_server.tasks.openai.run_openai_task",
    ignore_result=True,
    max_retries=3,
    rate_limit="10000/m",
    bind=True,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_kwargs={"max_retries": 3},
)
def run_openai_task(self, request_dict):
    """Celery task to run OpenAI requests.

    Note: Celery doesn't support async functions directly as tasks.
    We convert the dict back to the appropriate request object.

    Args:
        self: The Celery task instance
        request_dict: Dictionary representation of the request
    """
    try:
        # Convert dict back to appropriate request object
        if request_dict.get("service_id") == 13:
            request = AIColumnRequest(**request_dict)
        elif request_dict.get("service_id") == 14:
            request = FormulaRequest(**request_dict)
        elif request_dict.get("service_id") == 21:
            request = ResearchAgentRequest(**request_dict)
        else:
            raise ValueError(f"Unknown service_id: {request_dict.get('service_id')}")

        # Get Supabase client
        supabase_client: Client = get_supabase_client()

        # Use asyncio to run the async functions
        if request.service_id == 13:
            asyncio.run(run_ai_column(request, supabase_client))
        elif request.service_id == 14:
            asyncio.run(run_ai_formula(request, supabase_client))
        elif request.service_id == 21:
            asyncio.run(run_research_agent(request, supabase_client))
        else:
            logger.error(f"Unknown service_id: {request.service_id}")

    except Exception as e:
        logger.error(f"Error in run_openai_task: {str(e)}")
        # raise  # Re-raise to trigger Celery retry


async def process_linkedin_profile(
    request: LinkedinProfileRequest, supabase_client: Client
) -> None:
    """
    Process a LinkedIn profile request using ProxyCurl API.

    This function handles the entire lifecycle of a LinkedIn profile request:
    1. Evaluates any conditional formula to determine if processing should continue
    2. If condition fails, updates cell status and reimburses tokens
    3. If condition passes, fetches LinkedIn profile data and updates the cell
    4. Stores detailed results in the cell_details table

    Args:
        request: The LinkedinProfileRequest containing all parameters
        supabase_client: An authenticated Supabase client for database operations

    Returns:
        None: This function operates asynchronously and doesn't return a value
    """
    try:
        # Get the cell to update
        cell = (
            supabase_client.table("cells")
            .select("*")
            .eq("table_id", request.table_id)
            .eq("row_id", request.row_id)
            .eq("column_id", request.column_id)
            .execute()
            .data[0]
        )

        # Update cell status to processing
        run_status = {"run": "processing", "error": None}
        cell["run_status"] = run_status
        await send_supabase_broadcast(request.table_id, cell)

        # Check formula condition if present
        if request.formula:
            try:
                from src.backend_server.services.llm import run_only_run_if

                run_status = {"run": "validating", "error": None}
                cell["run_status"] = run_status
                await send_supabase_broadcast(request.table_id, cell)

                result = await run_only_run_if(request.formula)
                if not result["is_valid"]:
                    # Condition not met - update cell and reimburse tokens
                    run_status = {
                        "run": "condition_not_met",
                        "error": "Condition not met",
                    }
                    cell["run_status"] = run_status
                    await send_supabase_broadcast(request.table_id, cell)

                    await update_cell(
                        supabase_client,
                        request.column_id,
                        request.row_id,
                        request.table_id,
                        None,
                        {"run": "condition_not_met", "error": "Condition not met"},
                    )

                    # Reimburse tokens if credits are available
                    if request.credits > 0:
                        await reimburse_tokens(
                            organization_id=request.organization_id,
                            tokens=request.credits,
                            supabase=supabase_client,
                        )
                    return

            except Exception as e:
                logger.error(f"Error evaluating formula condition: {str(e)}")
                run_status = {
                    "run": "failed",
                    "error": f"Formula evaluation error: {str(e)}",
                }
                cell["run_status"] = run_status
                await send_supabase_broadcast(request.table_id, cell)

                await update_cell(
                    supabase_client,
                    request.column_id,
                    request.row_id,
                    request.table_id,
                    None,
                    {"run": "failed", "error": f"Formula evaluation error: {str(e)}"},
                )

                if request.credits > 0:
                    await reimburse_tokens(
                        organization_id=request.organization_id,
                        tokens=request.credits,
                        supabase=supabase_client,
                    )
                return

        # Formula passed or not present - fetch LinkedIn profile data
        try:
            run_status = {"run": "generating", "error": None}
            cell["run_status"] = run_status
            await send_supabase_broadcast(request.table_id, cell)

            # Call ProxyCurl API to get LinkedIn profile data
            profile_data = await enrich_linkedin_profile(request.linkedin_profile_url)

            if profile_data["status"] == "success":
                # Extract key information for cell value display
                profile = profile_data["data"]

                # Create a summary of the profile for display in the cell
                summary = {
                    "name": profile.get("full_name", ""),
                    "headline": profile.get("headline", ""),
                    "location": profile.get("location", {}).get("country", ""),
                    "company": (
                        profile.get("experiences", [])[0].get("company", "")
                        if profile.get("experiences")
                        else ""
                    ),
                    "title": (
                        profile.get("experiences", [])[0].get("title", "")
                        if profile.get("experiences")
                        else ""
                    ),
                }

                # Format the summary for display
                display_value = f"{summary['name']} - {summary['headline']}\n{summary['title']} at {summary['company']}\n{summary['location']}"

                # Update cell with success status
                run_status = {"run": "completed", "error": None}
                cell["run_status"] = run_status
                cell["value"] = display_value
                await send_supabase_broadcast(request.table_id, cell)

                # Update cell with result
                await update_cell(
                    supabase_client,
                    request.column_id,
                    request.row_id,
                    request.table_id,
                    display_value,
                    run_status,
                )

                # Store detailed results including full profile data
                await upsert_cell_details(
                    supabase_client,
                    request.table_id,
                    request.column_id,
                    request.row_id,
                    [{"LinkedInProfile": json.dumps(profile)}],
                )
            else:
                # Handle API error
                error_message = profile_data.get(
                    "message", "Unknown error fetching LinkedIn profile"
                )
                run_status = {"run": "failed", "error": error_message}
                cell["run_status"] = run_status
                await send_supabase_broadcast(request.table_id, cell)

                await update_cell(
                    supabase_client,
                    request.column_id,
                    request.row_id,
                    request.table_id,
                    None,
                    run_status,
                )

                # Reimburse tokens if credits are available
                if request.credits > 0:
                    await reimburse_tokens(
                        organization_id=request.organization_id,
                        tokens=request.credits,
                        supabase=supabase_client,
                    )
        except Exception as e:
            logger.error(f"Error fetching or storing LinkedIn profile: {str(e)}")
            run_status = {
                "run": "failed",
                "error": f"LinkedIn profile fetch error: {str(e)}",
            }
            cell["run_status"] = run_status
            await send_supabase_broadcast(request.table_id, cell)

            await update_cell(
                supabase_client,
                request.column_id,
                request.row_id,
                request.table_id,
                None,
                run_status,
            )

            if request.credits > 0:
                await reimburse_tokens(
                    organization_id=request.organization_id,
                    tokens=request.credits,
                    supabase=supabase_client,
                )
    except Exception as e:
        # Catch-all for any unexpected errors
        logger.error(f"Unexpected error in process_linkedin_profile: {str(e)}")
    return
