"""Pydantic models for database operations."""

from typing import List, Optional, Union
from pydantic import BaseModel, Field, ConfigDict
from enum import Enum


class Operator(str, Enum):
    """Filter operators for table data queries."""
    EQ = "eq"
    """Equal to"""
    
    NEQ = "neq"
    """Not equal to"""
    
    LT = "lt"
    """Less than"""
    
    LTE = "lte"
    """Less than or equal to"""
    
    GT = "gt"
    """Greater than"""
    
    GTE = "gte"
    """Greater than or equal to"""
    
    CONTAINS = "contains"
    """Contains substring"""
    
    NCONTAINS = "ncontains"
    """Does not contain substring"""
    
    EMPTY = "empty"
    """Is empty"""
    
    NEMPTY = "nempty"
    """Is not empty"""
    
    ERROR = "error"
    """Has error from a runnable column"""
    
    NERROR = "nerror"
    """Does not have error from a runnable column"""
    
    RESULT = "result"
    """Has result from a runnable column"""
    
    NRESULT = "nresult"
    """Does not have result from a runnable column"""
    
    RUN = "run"
    """Is running"""
    
    NRUN = "nrun"
    """Has not run"""
    
    AWAITING_INPUT = "awaiting_input"
    """Awaiting input"""
    
    QUEUED = "queued"
    """Is queued"""
    
    FAILED = "failed"
    """Has failed"""

    COMPLETED = "completed"
    """Has completed"""


class GroupOperator(str, Enum):
    """Operators for combining filter rules."""
    AND = "AND"
    """All conditions must be true"""
    
    OR = "OR"
    """At least one condition must be true"""





class Filter(BaseModel):
    """Filter model for table data queries."""
    model_config = ConfigDict(use_enum_values=True)
    
    column_name: str = Field(
        ..., 
        description="Name of the column to filter."
    )
    operator: Operator = Field(
        ..., 
        description="Filter operator such as 'eq', 'neq', 'lt', 'lte', etc."
    )
    value: Optional[str] = Field(
        None, 
        description="Value to compare against. Not required for some operators like 'empty'."
    )


class FilterGroup(BaseModel):
    """Group of filters that can be combined with AND/OR operators.
    
    Supports nested filter groups for complex query conditions.
    """
    model_config = ConfigDict(use_enum_values=True)
    
    operator: GroupOperator = Field(
        ..., 
        description="Logical operator ('AND' or 'OR') to combine the rules in this group."
    )
    rules: List[Union['Filter', 'FilterGroup']] = Field(
        ..., 
        min_length=1,
        description="List of filters or nested filter groups to apply. Must contain at least one rule. For nested groups, each can have its own operator."
    )




class SortDirection(str, Enum):
    """Sort direction options."""
    ASC = "asc"
    """Ascending order"""
    
    DESC = "desc"
    """Descending order"""

class Sort(BaseModel):
    """Sort model for table data queries."""
    model_config = ConfigDict(use_enum_values=True)
    
    column_name: str = Field(
        ..., 
        description="Name of the column to sort by."
    )
    direction: SortDirection = Field(
        ..., 
        description="Sort direction, either 'asc' (ascending) or 'desc' (descending)."
    )



class CreateTableRequest(BaseModel):
    """Request model for creating a new table.
    
    This model defines the structure for creating a new table with basic
    properties like name, and optional description.
    """
    model_config = ConfigDict(use_enum_values=True)
    
    organization_id: str = Field(
        ...,
        description="The unique identifier of the organization",
        min_length=1,
        examples=["org_c85117eb4106d464"]
    )
    
    name: str = Field(
        ...,
        description="Name of the normal column",
        min_length=1,
        max_length=255,
        examples=["Name", "Email", "Phone", "Company", "Title", "Notes"]
    )

    description: Optional[str] = Field(
        default=None,
        description="Description of the table",
        min_length=1,
        max_length=255,
        examples=["This is a table for storing customer information"]
    )
    


class UpdateTableRequest(BaseModel):
    """Request model for updating a table.
    
    This model defines the structure for updating a table with basic
    properties like name, and optional description.
    """
    model_config = ConfigDict(use_enum_values=True)

    table_id: str = Field(
        ...,
        description="The unique identifier of the table to update",
        min_length=1,
        examples=["tbl_c85117eb4106d464"]
    )
    
    name: Optional[str] = Field(
        default=None,
        description="Name of the table",
        min_length=1,
        max_length=255,
        examples=["Lead Generation Table", "Emails Table", "Phone Table", "Company Table", "Title Table", "Notes Table"]
    )

    filters: Optional[FilterGroup] = Field(
        default=None,
        description="Filters to apply to the table",
        examples=[{
            "operator": "GroupOperator.AND", "rules": [
                {
                    "column_name": "name",
                    "operator": "Operator.EQ",
                    "value": "John Doe"
                }
            ]
        }]
    )

    sorts: Optional[List[Sort]] = Field(
        default=None,
        description="Sorts to apply to the table",
        examples=[{"name": "John Doe", "direction": "SortDirection.ASC"}]
    )

    description: Optional[str] = Field(
        default=None,
        description="Description of the table",
        min_length=1,
        max_length=255,
        examples=["This is a table for storing customer information"]
    )



    

class DeleteTableRequest(BaseModel):
    """Request model for deleting a table.
    
    This model defines the structure for deleting a table.
    """
    model_config = ConfigDict(use_enum_values=True)
    
    table_id: str = Field(
        ...,
        description="The unique identifier of the table to delete",
        min_length=1,
        examples=["tbl_c85117eb4106d464"]
    )




class TableBaseModel(BaseModel):
    """Response model for a table.
    
    This model defines the structure for a table with basic
    properties like name, and optional description.
    """
    model_config = ConfigDict(use_enum_values=True, extra="ignore")
    
    organization_id: str = Field(
        ...,
        description="The unique identifier of the organization",
        min_length=1,
        examples=["org_c85117eb4106d464"]
    )
    
    name: str = Field(
        ...,
        description="Name of the normal column",
        min_length=1,
        max_length=255,
        examples=["Name", "Email", "Phone", "Company", "Title", "Notes"]
    )

    description: Optional[str] = Field(
        default=None,
        description="Description of the table",
        min_length=1,
        max_length=255,
        examples=["This is a table for storing customer information"]
    )

    filters: Optional[FilterGroup] = Field(
        default=None,
        description="Filters to apply to the table",
        examples=[{
            "operator": "GroupOperator.AND", "rules": [
                {
                    "column_name": "name",
                    "operator": "Operator.EQ",
                    "value": "John Doe"
                }
            ]
        }]
    )

    sorts: Optional[List[Sort]] = Field(
        default=None,
        description="Sorts to apply to the table",
        examples=[{"name": "John Doe", "direction": "SortDirection.ASC"}]
    )

    success: bool = Field(
        default=True,
        description="Indicates if the table was updated successfully"
    )
    
    error_message: Optional[str] = Field(
        default=None,
        description="Error message when update fails"
    )


class TableResponse(BaseModel):
    """Response model for a table.
    
    This model defines the structure for a table with basic
    properties like name, and optional description.
    """
    model_config = ConfigDict(use_enum_values=True)
    
    table: Optional[TableBaseModel] = Field(
        default=None,
        description="The complete table data when operation is successful"
    )

    success: bool = Field(
        default=True,
        description="Indicates if the table was updated successfully"
    )
    
    error_message: Optional[str] = Field(
        default=None,
        description="Error message when update fails"
    )