import httpx
import asyncio

GET_EMIAL_REQUEST_TEMPLATE = {
    "organization_id": "org_64bd7f51c1450d62",
    "table_id": "tbl_3655e99e74169376",
    "column_id": 7,
    "row_id": 1,
    "service_id": 10,  # Formula service ID
    "run_id": 1,
    "credits": 10,
    "formula": None,
    "providers": {
        "email_providers": ["prospeo", "leadmagic"],
        "verify_providers": ["millionverifier"],
    },
    "value": {"full_name": "<PERSON>", "company_domain": "rf-frontend.de"},
}


async def test_email():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:2024/run-cell",
            json=GET_EMIAL_REQUEST_TEMPLATE,
        )
        print(response.json())


if __name__ == "__main__":
    # Run the async function using asyncio
    asyncio.run(test_email())
