"""Database read operations for table data."""

from typing import Dict, List, Optional, Tuple, Any
from ..client import supabase
from .models import FilterGroup, Sort, TableDataRequest
from typing import Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg
from ...configuration import Configuration


def get_table_data(
    config: Annotated[RunnableConfig, InjectedToolArg],
    request: TableDataRequest,
) -> <PERSON>ple[Optional[Dict[str, Any]], Optional[str]]:
    """Fetch data from a specific table using Supabase RPC.
    
    Args:
        config: Runnable configuration containing table_id and other settings
        request: TableDataRequest object containing all query parameters
        
    Returns:
        Tuple[Optional[List[Dict]], Optional[str]]: Tuple containing (data, error)
        where data is the table data if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id

        # Convert pydantic models to dictionaries for the RPC call
        filters_dict = None
        if request.filters is not None:
            if hasattr(request.filters, 'model_dump'):
                filters_dict = request.filters.model_dump(mode='json')  # Use mode='json' to convert enums to values
            else:
                filters_dict = request.filters  # Already a dictionary
        
        sorts_dict = None
        if request.sorts is not None:
            if all(hasattr(sort, 'model_dump') for sort in request.sorts):
                sorts_dict = [sort.model_dump(mode='json') for sort in request.sorts]  # Use mode='json' to convert enums to values
            else:
                sorts_dict = request.sorts  # Already a list of dictionaries
        
        response = supabase.rpc(
            'agent_get_table_columns_with_cells_v3',
            {
                'p_filters': filters_dict,
                'p_limit': request.max_rows,
                'p_search': request.search,
                'p_sorts': sorts_dict,
                'p_table_id': table_id,
                'p_column_names': request.column_names,  
                'p_apply_table_filters': request.apply_table_filters,  
                'p_apply_table_sorts': request.apply_table_sorts 
            }
        ).execute()
        
        if not hasattr(response, 'data') or response.data is None:
            return None, "Server Error: Data not available"
            
        return response.data, None
            
    except Exception as e:
        error_msg = f"Error fetching table data: {str(e)}"
        print(error_msg)
        return None, error_msg
