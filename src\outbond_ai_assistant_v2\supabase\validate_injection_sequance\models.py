"""Pydantic models for injection sequence validation operations."""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, ConfigDict, field_validator
from enum import Enum
import re


class TargetDataType(str, Enum):
    """Enum for supported target data types for validation."""
    LINKEDIN_PROFILE_URL = "linkedin_profile_url"
    LINKEDIN_COMPANY_URL = "linkedin_company_url"
    FULL_NAME = "full_name"
    DOMAIN = "domain"


class ValidateInjectionSequenceRequest(BaseModel):
    """Request model for validating injection sequences.
    
    This model defines the structure for validating injection sequences with comprehensive
    validation and documentation following Google style guidelines.
    """
    model_config = ConfigDict(use_enum_values=True)
    
    injection_sequence: str = Field(
        ...,
        description="The injection sequence to validate (must be wrapped in double curly braces {{}})",
        min_length=4,
        examples=[
            "{{linkedin profile.cell_details.first_name}}",
            "{{company.cell_details.website}}",
            "{{email_column.value}}",
            "{{research_column.cell_details.response}}",
            "{{linkedin profile.cell_details.experience.0.job_title}}",
            "{{user_data.cell_details.skills.2.name}}"
        ]
    )
    
    
    target_data_type: Optional[TargetDataType] = Field(
        default=None,
        description="The target data type to validate values against",
        examples=["linkedin_profile_url", "linkedin_company_url", "full_name", "domain"]
    )
    
    @field_validator('injection_sequence')
    @classmethod
    def validate_injection_sequence_format(cls, v: str) -> str:
        """Validate that injection sequence is wrapped in double curly braces."""
        if not isinstance(v, str):
            raise ValueError('injection_sequence must be a string')
        
        # Check if it starts with {{ and ends with }}
        if not (v.startswith('{{') and v.endswith('}}')):
            raise ValueError('injection_sequence must be wrapped in double curly braces {{}}')
        
        # Check that there's content between the braces
        content = v[2:-2].strip()
        if not content:
            raise ValueError('injection_sequence cannot be empty between the curly braces')
        
        return v



class ValidateInjectionSequenceResponse(BaseModel):
    """Response model for injection sequence validation operations.
    
    This model defines the structure of successful validation responses,
    providing clear typing and documentation.
    """
    model_config = ConfigDict(use_enum_values=True)
    
    injection_sequence: Optional[str] = Field(
        default=None,
        description="The injection sequence that was validated"
    )

    error_message: Optional[str] = Field(
        default=None,
        description="Human-readable message describing the validation result"
    )
    
    success: bool = Field(
        default=True,
        description="Indicates if the validation operation was successful"
    )

