"""LinkedIn profile column creation and editing functionality."""

from typing import Union
import src.outbond_ai_assistant_v2.supabase.table.models as supabase_models
import src.outbond_ai_assistant_v2.supabase.table.update_table as supabase_update_table
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool
from typing import Annotated
from .models import (
    UpdateTableRequest,
    UpdateTableRequestWithNotifyFE,
    UpdateTableResponse,
)
from src.outbond_ai_assistant_v2.configuration import Configuration


@tool
def update_table_tool(
    config: Annotated[RunnableConfig, InjectedToolArg],
    request: UpdateTableRequestWithNotifyFE
) -> Union[UpdateTableResponse, supabase_models.TableResponse]:
    """Update a table with optional name, description, filters, and sorts.

    This function provides a complete workflow for table update:
    1. Updates the table with optional name, description, filters, and sorts
    2. Returns detailed success or error information

    Args:
        config (RunnableConfig): The runnable configuration containing context and metadata.
        request (UpdateTableRequestWithNotifyFE): The validated request model containing
            all necessary parameters for table update including optional name, description,
            filters, sorts, and notification preferences.

    Returns:
        Union[UpdateTableResponse, supabase_models.TableResponse]: Response model containing either
            success data (UpdateTableResponse) or error information from the supabase operation.

    Raises:
        Exception: For any unexpected errors during the table update process.
    """
    try:
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id


        
        # Step 4: Create upsert request
        update_table_request = supabase_models.UpdateTableRequest(
            table_id=table_id,
            **request.table.model_dump(exclude_unset=True)
        )
        
        # Step 5: Execute column creation/update
        update_table_result = supabase_update_table.update_table(config=config, request=update_table_request, is_notify_fe=request.is_notify_fe)
        
        # If upsert failed, return the upsert response directly
        if not update_table_result.success:
            return update_table_result
        
        return UpdateTableResponse(
            table=update_table_result.table,
            success=True
        )
        
    except Exception as e:
        error_msg = f"Unexpected error in row creation: {str(e)}"
        return UpdateTableResponse(
            error_message=error_msg,
            success=False
        )