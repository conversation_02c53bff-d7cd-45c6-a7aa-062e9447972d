#!/usr/bin/env python3
"""
Test script for LinkedIn profile integration with ProxyCurl API.
This script submits a LinkedIn profile request to the API and monitors the result.
"""

import os
import sys
import json
import time
import uuid
import requests
import argparse
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Load environment variables
load_dotenv()

# API configuration
API_URL = os.getenv("API_URL", "http://localhost:8000")
API_TOKEN = os.getenv("API_TOKEN")

# Default test parameters
DEFAULT_ORG_ID = os.getenv("TEST_ORG_ID", "test-org")
DEFAULT_TABLE_ID = os.getenv("TEST_TABLE_ID", "test-table")
DEFAULT_COLUMN_ID = int(os.getenv("TEST_COLUMN_ID", "1"))
DEFAULT_ROW_ID = int(os.getenv("TEST_ROW_ID", "1"))
DEFAULT_CREDITS = int(os.getenv("TEST_CREDITS", "10"))
DEFAULT_SERVICE_ID = 21  # LinkedIn Profile service ID


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Test LinkedIn profile integration")
    parser.add_argument("--url", required=True, help="LinkedIn profile URL to test")
    parser.add_argument("--org", default=DEFAULT_ORG_ID, help="Organization ID")
    parser.add_argument("--table", default=DEFAULT_TABLE_ID, help="Table ID")
    parser.add_argument("--column", type=int, default=DEFAULT_COLUMN_ID, help="Column ID")
    parser.add_argument("--row", type=int, default=DEFAULT_ROW_ID, help="Row ID")
    parser.add_argument("--credits", type=int, default=DEFAULT_CREDITS, help="Credits to use")
    parser.add_argument("--formula", help="Optional formula condition")
    return parser.parse_args()


def submit_linkedin_profile_request(args):
    """Submit a LinkedIn profile request to the API."""
    if not API_TOKEN:
        print("Error: API_TOKEN environment variable not set")
        sys.exit(1)

    headers = {
        "Authorization": f"Bearer {API_TOKEN}",
        "Content-Type": "application/json"
    }

    # Generate a unique run ID
    run_id = int(uuid.uuid4().int % 1000000)

    # Prepare the request payload
    payload = {
        "organization_id": args.org,
        "table_id": args.table,
        "column_id": args.column,
        "row_id": args.row,
        "run_id": run_id,
        "service_id": DEFAULT_SERVICE_ID,
        "credits": args.credits,
        "linkedin_profile_url": args.url
    }

    # Add formula if provided
    if args.formula:
        payload["formula"] = args.formula

    print(f"Submitting LinkedIn profile request for: {args.url}")
    print(f"Request payload: {json.dumps(payload, indent=2)}")

    try:
        response = requests.post(
            f"{API_URL}/services/linkedin-profile",
            headers=headers,
            json=payload,
            timeout=10
        )

        if response.status_code == 200:
            result = response.json()
            print(f"Request submitted successfully: {result}")
            return run_id
        else:
            print(f"Error submitting request: {response.status_code}")
            print(response.text)
            return None
    except Exception as e:
        print(f"Exception during request: {str(e)}")
        return None


def main():
    """Main function to run the test."""
    args = parse_args()
    
    # Submit the LinkedIn profile request
    run_id = submit_linkedin_profile_request(args)
    
    if run_id:
        print(f"Request submitted with run_id: {run_id}")
        print("Check your database or application UI to see the results")
        print("The Celery worker will process the request asynchronously")


if __name__ == "__main__":
    main()
