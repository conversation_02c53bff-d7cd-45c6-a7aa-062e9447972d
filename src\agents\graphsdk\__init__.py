"""Utility functions for graph SDK."""

## TODO Move out in sdk.py and setup pyproject.toml for package

import logging
from typing import Callable
from bond_ai.registry import supervisor_sub_agent_registry
from bond_ai.registry.registry import SupervisorSubAgents
from bond_ai.state import Bond<PERSON><PERSON>WorkflowState
from bond_ai.utilities.debug import _log_agent_io
from bond_ai.utils import clean_thinking_blocks_for_bedrock
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.runnables import RunnableConfig
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
from src.agents.bond_ai.registry.registry import AgentInfo
from langgraph.config import get_stream_writer
from langgraph.prebuilt import create_react_agent
from langgraph.prebuilt.interrupt import HumanInterrupt, HumanInterruptConfig
from langgraph.types import interrupt
from langchain_core.tools import BaseTool
from langchain_core.tools import tool as create_tool


## TODO WIP Check requirements for hitl
def inject_hitl_wrapper(
    tool: Callable | BaseTool,
    *,
    interrupt_config: HumanInterruptConfig | None = None,
    review_prompt: str = "Please review the tool call",
    accepted_responses: list[str] | None = None,
    allow_deny: bool | None = None,
    allow_skip: bool | None = None,
) -> BaseTool:
    """Wrap a tool to support human-in-the-loop review.

    Args:
        tool: The tool or callable to wrap.
        interrupt_config: Base :class:`HumanInterruptConfig` values to merge
            with defaults.
        review_prompt: Text shown to the human reviewer describing the
            pending tool call.
        accepted_responses: List of responses that will approve execution.
            Defaults to common confirmation keywords.
        allow_deny: Override whether the human can deny execution.
        allow_skip: Override whether the human can skip execution.

    Returns:
        A :class:`BaseTool` that will request human approval before running.
        Any provided ``tool_call_id`` is echoed in the resulting ``ToolMessage``
        and ``tool_calls`` entries.
    """
    if not isinstance(tool, BaseTool):
        tool = create_tool(tool)

    default_config: HumanInterruptConfig = {
        "allow_accept": True,
        "allow_edit": True,
        "allow_respond": True,
    }
    if allow_deny is not None:
        default_config["allow_deny"] = allow_deny
    else:
        default_config.setdefault("allow_deny", True)

    if allow_skip is not None:
        default_config["allow_skip"] = allow_skip
    else:
        default_config.setdefault("allow_skip", True)

    if interrupt_config:
        default_config.update(interrupt_config)
    interrupt_config = default_config

    if accepted_responses is None:
        accepted_responses = ["yes", "y", "confirm", "proceed", "ok", "run"]

    skip_responses = ["skip", "s"]

    @create_tool(
        tool.name,
        description=tool.description,
        args_schema=tool.args_schema,
    )
    def call_tool_with_interrupt(config: RunnableConfig, **tool_input):
        tool_call_id = None
        if isinstance(config, dict):
            tool_call_id = (
                config.get("tool_call_id")
                or config.get("configurable", {}).get("tool_call_id")
            )
        if tool_call_id is None:
            tool_call_id = tool_input.get("tool_call_id")

        request: HumanInterrupt = {
            "action_request": {"action": tool.name, "args": tool_input},
            "config": interrupt_config,
            "description": review_prompt,
        }
        user_response = interrupt([request])
        logging.info(f"**{tool.name} - {tool_call_id}** HITL User response: {user_response}")

        # determine path based on user response
        normalized = str(user_response).lower() if user_response is not None else ""

        if normalized in skip_responses:
            return {
                "messages": ToolMessage(
                    content=f"{tool.name} execution skipped by user.",
                    name=tool.name,
                    tool_call_id=tool_call_id,
                ),
                "tool_calls": [],
            }

        if normalized not in [r.lower() for r in accepted_responses]:
            return {
                "messages": ToolMessage(
                    content=f"{tool.name} execution cancelled by user. Reason: {user_response}.",
                    name=tool.name,
                    tool_call_id=tool_call_id,
                ),
                "tool_calls": [],
            }

        try:
            tool_response = tool.invoke(tool_input, config)
            logging.info(f"**{tool.name} - {tool_call_id}**  HITL Tool response: {tool_response}")
        except Exception as e:
            tool_response = f"Error executing tool '{tool.name}': {str(e)}"
            logging.error(f"**{tool.name} - {tool_call_id}**  HITL Tool error: {tool_response}")

        

        tool_call_record = {
            "name": tool.name,
            "status": "completed",
        }
        if tool_call_id is not None:
            tool_call_record["id"] = tool_call_id

        # Return the tool outputs to be added to messages
        return {
            "messages": ToolMessage(
                content=str(tool_response),  # Convert to string to ensure JSON serialization
                name=tool.name,
                tool_call_id=tool_call_id,
            ),
            "tool_calls": [tool_call_record],
        }

    return call_tool_with_interrupt



def add_agent_prebuid_react(
    agent_params: AgentInfo,
    enabled: bool = True,
) -> None:
    """Add a new ReAct agent to the registry."""
    
    name = agent_params.name
    system_prompt = agent_params.system_prompt
    tools = agent_params.tools
    description = agent_params.description
    prompt_injections = agent_params.prompt_injections
    supervisor_sub_agent_registry.register_react_prebuild_agent(
        name=name,
        system_prompt=system_prompt,
        description=description,
        tools=tools,
        enabled=enabled,
        prompt_injections=prompt_injections or {},
    )
    logging.info(f"[DEBUG] ✓ Added ReAct agent: {name}")


def add_node(name: str, node_function, description: str, enabled: bool = True, **kwargs) -> None:
    """Add a pre-built node function to the registry."""
    supervisor_sub_agent_registry.register_custom_agent(name, node_function, description, enabled, **kwargs)
    logging.info(f"[DEBUG] ✓ Added custom node: {name}")


def enable_agent(name: str) -> None:
    """Enable an agent or node."""
    supervisor_sub_agent_registry.enable_agent(name)
    logging.info(f"[DEBUG] ✓ Enabled: {name}")


def disable_agent(name: str) -> None:
    """Disable an agent or node."""
    supervisor_sub_agent_registry.disable_agent(name)
    logging.info(f"[DEBUG] ✓ Disabled: {name}")


def list_agents() -> None:
    """List all agents and nodes with their status."""
    logging.info("\n=== AGENT REGISTRY ===")
    for name, config in supervisor_sub_agent_registry.agents.items():
        status = "[DEBUG] ✓ ENABLED" if config.get("enabled", True) else "✗ DISABLED"
        agent_type = config.get("type", "unknown")

        if agent_type == "react_agent":
            tools_count = len(config.get("tools", []))
            logging.info(f"{status} | {name} | Type: ReAct Agent | Tools: {tools_count}")
        elif agent_type == "custom_node":
            func_name = config.get("node_function", {}).get("__name__", "Unknown")
            logging.info(f"{status} | {name} | Type: Custom Node | Function: {func_name}")
        else:
            logging.info(f"{status} | {name} | Type: {agent_type}")
    logging.info("")


def rebuild_workflow():
    """Rebuild the workflow with current registry state."""
    from bond_ai.graph import create_workflow_from_registry  # noqa: E402

    global graph
    logging.info("Rebuilding workflow with current agent registry...")
    current_agents = supervisor_sub_agent_registry.get_agent_names()
    logging.info(f"Active agents: {current_agents}")
    graph = create_workflow_from_registry()
    logging.info("✓ Workflow rebuilt successfully")
    return graph


def agent_node(state, agent, name):
    """Invoke an agent node and return its latest message."""
    result = agent.invoke(state)
    return {"messages": [HumanMessage(content=result["messages"][-1].content, name=name)]}


def _get_nested_state_value(state, path, default=""):
    """Return a nested value from state using dot notation."""
    try:
        value = state
        for key in path.split("."):
            value = value.get(key) if isinstance(value, dict) else getattr(value, key, None)
        return value if value is not None else default
    except Exception:
        return default


def _escape_braces_in_content(content: str) -> str:
    """Escape single curly braces in content to prevent LangGraph template errors."""
    return content.replace("{", "{{").replace("}", "}}")


##########################################################################
###################### ASYNC AGENT CREATION FUNCTIONS ####################
##########################################################################

DEBUG_MODE = False

def create_async_react_agent(name: str, system_prompt: str, tools: list, prompt_injections: dict | None = None, llm=None):
    """Create an async ReAct agent with optional state injection and LangStudio visibility."""
    if llm is None:
        llm = ChatOpenAI(model="gpt-4o-mini")

    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        MessagesPlaceholder(variable_name="messages"),
    ])

    react_agent = create_react_agent(
        model=llm,
        tools=tools,
        prompt=prompt,
        state_schema=BondAIWorkflowState,
        name=name,
        debug=DEBUG_MODE,
    
    )

    async def async_agent_wrapper_node(state: BondAIWorkflowState, config: RunnableConfig):
        # Clean and normalize messages for Bedrock/Anthropic compliance
        cleaned = clean_thinking_blocks_for_bedrock(list(state["messages"]))
        from bond_ai.utils import split_out_system_messages_for_bedrock
        non_system_messages, extra_system_text = split_out_system_messages_for_bedrock(cleaned)
        # Build history without any SystemMessage objects (system will come from the prompt)
        history = [*non_system_messages]
        # print(f"[Debug] non_system_messages from {name}", non_system_messages)
        # print(f"[Debug] extra_system_text from {name}", extra_system_text)
        #print(f"[Debug] history from {name}", history)

        # Prepare input payload
        input_invoke = {
            "messages": history,
            "table_summary": state.get("table_summary")
        }

        # Clean input messages to prevent Bedrock validation errors
        if "messages" in input_invoke and isinstance(input_invoke["messages"], list):
            for msg in input_invoke["messages"]:
                if hasattr(msg, 'content') and isinstance(msg.content, str):
                    original_content = msg.content
                    stripped_content = msg.content.rstrip()
                    if original_content != stripped_content:
                        print(f"[DEBUG] Input message from {name} had trailing whitespace: {repr(original_content[-10:])}")
                    msg.content = stripped_content

        # Choose the agent/prompt to use. If we have prompt injections or extra system text,
        # we create a dynamic prompt so that ONLY a single SystemMessage is emitted by the prompt.
        agent = react_agent
        # IMPORTANT: Escape braces in inherited extra_system_text so ChatPromptTemplate
        # does not attempt to treat JSON-like content (e.g., {"foo": ...}) as
        # format placeholders. We only escape the inherited text; the base
        # system_prompt and any explicit prompt_injections keep their intended
        # placeholders intact.
        escaped_extra = _escape_braces_in_content(extra_system_text) if extra_system_text else ""
        effective_system_prompt = (
            f"{system_prompt}\n\n{escaped_extra}" if escaped_extra else system_prompt
        )

        if prompt_injections:
            print(f"[Debug] prompt_injections from {name}", prompt_injections)
            injected_prompt = system_prompt
            for placeholder, state_path in prompt_injections.items():
                value = _get_nested_state_value(state, state_path)
                escaped_value = _escape_braces_in_content(str(value))
                injected_prompt = injected_prompt.replace(f"{{{{{placeholder}}}}}", escaped_value)
            # Merge any extra system text extracted from the history (escaped)
            effective_injected_prompt = (
                f"{injected_prompt}\n\n{escaped_extra}" if escaped_extra else injected_prompt
            )

            dynamic_prompt = ChatPromptTemplate.from_messages([
                ("system", effective_injected_prompt),
                MessagesPlaceholder(variable_name="messages"),
            ])

            agent = create_react_agent(
                name=name,
                model=llm,
                tools=tools,
                prompt=dynamic_prompt,
                state_schema=BondAIWorkflowState,
                debug=DEBUG_MODE,
            )
        elif extra_system_text:
            # No prompt injections, but we still need to include extra system context
            dynamic_prompt = ChatPromptTemplate.from_messages([
                ("system", effective_system_prompt),
                MessagesPlaceholder(variable_name="messages"),
            ])
            agent = create_react_agent(
                name=name,
                model=llm,
                tools=tools,
                prompt=dynamic_prompt,
                state_schema=BondAIWorkflowState,
                debug=DEBUG_MODE,
            )

        # Invoke agent with sanitized history (no SystemMessage objects) so that
        # the ChatPromptTemplate-provided system prompt remains the first and only
        # SystemMessage in the conversation (required by Bedrock/Anthropic).
        cleaned_state = dict(state)
        cleaned_state["messages"] = history
        result = await agent.ainvoke(cleaned_state, config)
        #print(f"[Debug] result from {name}", result)

        # Strip trailing whitespace from all messages to prevent Bedrock validation errors
        if "messages" in result and isinstance(result["messages"], list):
            for msg in result["messages"]:
                if hasattr(msg, 'content') and isinstance(msg.content, str):
                    original_content = msg.content
                    stripped_content = msg.content.rstrip()
                    if original_content != stripped_content:
                        print(f"[DEBUG] React agent {name} found trailing whitespace: {repr(original_content[-10:])}")
                    msg.content = stripped_content

        try:
            last_message = result["messages"][-1]
            print(f"[Debug] last_message {name}", last_message)
            out = {k: v for k, v in result.items() if k != "messages"}
            out["messages"] = [AIMessage(content=last_message, name=name)]
        except Exception:
            out = result
        #print(f"[Debug] out from {name}", out)
        return out

    async_agent_wrapper_node._agent_name = name
    async_agent_wrapper_node._react_agent = react_agent
    async_agent_wrapper_node._system_prompt = system_prompt
    async_agent_wrapper_node._tools = tools
    async_agent_wrapper_node._has_prompt_injections = bool(prompt_injections)
    async_agent_wrapper_node._prompt_injections = prompt_injections or {}
    async_agent_wrapper_node._is_async = True


    return async_agent_wrapper_node

