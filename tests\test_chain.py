import httpx
import asyncio

GET_PROFILE_REQUEST_TEMPLATE = {
    "organization_id": "org_64bd7f51c1450d62",
    "table_id": "tbl_3655e99e74169376",
    "column_id": 7,
    "row_id": 1,
    "service_id": 7,  # Formula service ID
    "run_id": 1,
    "credits": 10,
    "formula": None,
    "value": {
        "linkedin_profile_url": "https://de.linkedin.com/in/rina-gracic-9b86952a4",
    },
}

LOOKUP_PROFILE_REQUEST_TEMPLATE = {
    "organization_id": "org_64bd7f51c1450d62",
    "table_id": "tbl_3655e99e74169376",
    "column_id": 7,
    "row_id": 1,
    "service_id": 16,  # Formula service ID
    "run_id": 1,
    "credits": 10,
    "formula": None,
    "value": {
        "company_domain": "rf-frontend.de",
        "full_name": "<PERSON>",
    },
}

GET_COMPANY_REQUEST_TEMPLATE = {
    "organization_id": "org_64bd7f51c1450d62",
    "table_id": "tbl_3655e99e74169376",
    "column_id": 7,
    "row_id": 1,
    "service_id": 8,  # Formula service ID
    "run_id": 1,
    "credits": 10,
    "formula": None,
    "value": {
        "linkedin_company_url": "https://www.linkedin.com/company/microsoft",
    },
}

LOOKUP_COMPANY_REQUEST_TEMPLATE = {
    "organization_id": "org_64bd7f51c1450d62",
    "table_id": "tbl_3655e99e74169376",
    "column_id": 7,
    "row_id": 1,
    "service_id": 17,  # Formula service ID
    "run_id": 1,
    "credits": 10,
    "formula": "always valid",
    "value": {
        "company_domain": "microsoft.com",
    },
}


async def test_get_person_profile():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:2024/run-cell",
            json=GET_PROFILE_REQUEST_TEMPLATE,
        )
        print(response.json())


async def test_lookup_person_profile():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:2024/run-cell",
            json=LOOKUP_PROFILE_REQUEST_TEMPLATE,
        )
        print(response.json())


async def test_get_company_profile():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:2024/run-cell",
            json=GET_COMPANY_REQUEST_TEMPLATE,
        )
        print(response.json())


async def test_lookup_company_profile():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:2024/run-cell",
            json=LOOKUP_COMPANY_REQUEST_TEMPLATE,
        )
        print(response.json())


async def test_chaining():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:2024/chaining",
            json={},
        )
        print(response.json())


if __name__ == "__main__":
    # Run the async function using asyncio
    asyncio.run(test_chaining())
