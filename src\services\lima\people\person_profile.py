from typing import List, Dict, Any, Optional
from fastapi.security import api_key
from pydantic import BaseModel, Field, ConfigDict
from datetime import datetime
from src.schemas.linkedin import LinkedinPersonProfileUrl
from .schemas import (
    PersonProfileOutput,
    PersonProfileResponse,
    Experience,
    DateObject,
    Location,
    LimaPersonLookupOutput,
    LimaPersonLookupResponse,
)
from src.db.utils import get_type_id, get_type_name, format_date
import httpx
from src.core.config import get_settings
import asyncio
import logging
import os

settings = get_settings()

logger = logging.getLogger(__name__)


# Custom exceptions
class RateLimitException(Exception):
    """Exception raised when API rate limit is exceeded"""

    def __init__(self, message: str, retry_after: Optional[str] = None):
        self.message = message
        self.retry_after = retry_after
        super().__init__(self.message)


# Input Model
class PersonProfileInput(BaseModel):
    linkedin_profile_url: LinkedinPersonProfileUrl


class LookupPersonProfileInput(BaseModel):
    model_config = ConfigDict(extra="ignore")
    company_name: str
    full_name: str


async def get_linkedin_person_profile(
    input: PersonProfileInput,
) -> PersonProfileResponse:
    """Fetch LinkedIn person profile data from the Lima API

    Returns:
        tuple: (PersonProfileOutput, credit_cost)
    """
    try:
        if settings.IS_STRESS_TESTING:
            # wait for 30 seconds
            logger.info("stress")
            await asyncio.sleep(30)
            return PersonProfileResponse(
                profile=PersonProfileOutput(
                    profile_image_url=settings.TESTING_LINKEDIN_PROFILE_PICTURE_URL,
                    full_name="Abudi Mohamed",
                    headline="CTO",
                    about="CTO of Outbond",
                    location=Location(country="Germany", city="Berlin"),
                    experiences=[
                        Experience(
                            title="CTO",
                            company="Outbond",
                            profile_url="https://www.linkedin.com/company/outbond",
                            location="Berlin",
                            starts_at=datetime(2020, 1, 1).isoformat(),
                            ends_at=datetime(2024, 1, 1).isoformat(),
                        )
                    ],
                    educations=[],
                    skills=[],
                    languages=[],
                    recommendations=[],
                ),
                credit_cost=0,
            )
        async with httpx.AsyncClient() as client:
            response = await client.get(
                "https://api.limadata.com/api/v1/person",
                params={"url": input.linkedin_profile_url},
                headers={"X-API-KEY": f"{settings.LIMA_API_KEY.get_secret_value()}"},
                timeout=60.0,  # 60 second timeout
            )
            response.raise_for_status()

            # Extract credit cost from headers
            credit_cost = 0
            if "x-credits-cost" in response.headers:
                try:
                    credit_cost = int(response.headers["x-credits-cost"])
                except (ValueError, TypeError):
                    # If header exists but can't be converted to int
                    logger.warning(
                        f"Could not parse credit cost from header: {response.headers.get('x-credits-cost')}"
                    )

            return PersonProfileResponse(
                profile=PersonProfileOutput(**response.json()), credit_cost=credit_cost
            )
    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            logger.warning("Profile not found")
            raise ValueError("Profile not found")
        elif e.response.status_code == 429:
            logger.warning("Rate limit exceeded")
            # Extract retry-after header if available
            retry_after = e.response.headers.get("Retry-After")
            raise RateLimitException(
                retry_after=retry_after, message="Rate limit exceeded. Try again later."
            )
        else:
            logger.error(f"HTTP error: {e.response.status_code} - {e.response.text}")
            raise e
    except Exception as e:
        logger.error(f"Error fetching person profile: {str(e)}")
        raise e


async def lookup_linkedin_person_profile(
    input: LookupPersonProfileInput,
) -> LimaPersonLookupResponse:
    """Fetch LinkedIn person profile data from the Lima API

    Returns:
        tuple: (PersonProfileOutput, credit_cost)
    """
    try:
        if settings.IS_STRESS_TESTING:
            # wait for 30 seconds
            await asyncio.sleep(30)
            return LimaPersonLookupResponse(
                profile=LimaPersonLookupOutput(
                    linkedin_url="https://www.linkedin.com/in/abudi-mohamed",
                    github_url="https://github.com/abudi-mohamed",
                    x_url="https://x.com/abudi_mohamed",
                    facebook_url="https://www.facebook.com/abudi.mohamed",
                ),
                credit_cost=0,
            )
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://api.limadata.com/api/v1/find/profiles_person",
                json={"full_name": input.full_name, "company_name": input.company_name},
                headers={"X-API-KEY": f"{settings.LIMA_API_KEY.get_secret_value()}"},
                timeout=60.0,  # 60 second timeout
            )
            response.raise_for_status()

            # Extract credit cost from headers
            credit_cost = 0
            if "x-credits-cost" in response.headers:
                try:
                    credit_cost = int(response.headers["x-credits-cost"])
                except (ValueError, TypeError):
                    # If header exists but can't be converted to int
                    logger.warning(
                        f"Could not parse credit cost from header: {response.headers.get('x-credits-cost')}"
                    )

            return LimaPersonLookupResponse(
                profile=LimaPersonLookupOutput(**response.json()),
                credit_cost=credit_cost,
            )
    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            logger.warning("Profile not found")
            raise ValueError("Profile not found")
        elif e.response.status_code == 429:
            logger.warning("Rate limit exceeded")
            # Extract retry-after header if available
            retry_after = e.response.headers.get("Retry-After")
            raise RateLimitException(
                retry_after=retry_after, message="Rate limit exceeded. Try again later."
            )
        else:
            logger.error(f"HTTP error: {e.response.status_code} - {e.response.text}")
            raise e
    except Exception as e:
        logger.error(f"Error fetching person profile: {str(e)}")
        raise e
