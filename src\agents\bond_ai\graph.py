"""Define a ReAct agent using LangGraph.

This agent follows the ReAct pattern (Reasoning and Acting) to solve tasks
by thinking step by step and using tools when needed.
"""


import logging
from typing import List

from bond_ai.configuration import Configuration
from bond_ai.memory.vector_store import memory_store

from bond_ai.prompts_v1 import SUPERVISOR_AGENT_PROMPT
from bond_ai.registry import supervisor_sub_agent_registry
from bond_ai.registry.registry import SupervisorSubAgents
from bond_ai.state import BondAIWorkflowState

from dotenv import load_dotenv
from src.agents.graphsdk import (
    create_async_react_agent,
    add_agent_prebuid_react,
)
from langchain_core.messages import (
    AIMessage,
    HumanMessage,
    RemoveMessage,
    SystemMessage,
)
from langchain_core.runnables import RunnableConfig
from langchain_openai import ChatOpenAI
from langgraph.graph import END, START, StateGraph
from bond_ai.nodes import (
    task_completion_node,
    error_response_node,
    planner_agent,
    table_indexing_node,
    summarize_conversation,
    supervisor_node,
  
)
from bond_ai.utilities.llm_factory import llm_thinking, llm_no_thinking

load_dotenv()







##########################################################################
######################  custom nodes BUILDER ##########################
##########################################################################
try:
    supervisor_sub_agent_registry.register_custom_agent(
        name = SupervisorSubAgents.planner_agent.value.name,
        node_function = planner_agent,
        description = SupervisorSubAgents.planner_agent.value.description
    )
    logging.info("✓ Registered Planner node")
except ImportError as e:
    logging.error(f"⚠ Could not import planner_agent: {e}")




# Import the ICP subgraph directly for LangStudio visibility
from bond_ai.perplexity.graph import icp_persona_graph

# Create async wrapper that preserves subgraph reference
async def icp_agent_wrapper(state: BondAIWorkflowState, config: RunnableConfig):
    """Async wrapper for ICP subgraph with state transformation"""
    import logging

    try:
        logging.info("🎯 Starting ICP analysis...")

        # Call subgraph directly (preserves LangStudio visibility)
        result = await icp_persona_graph.ainvoke(state, config)

        return {
            "messages": [AIMessage(content=result['messages'][-1].content, name="icp_agent")],
            "next": "FINISH"  # Signal completion for direct invocation
        }

    except Exception as e:
        logging.error(f"❌ ICP analysis failed: {e}")
        return {
            "messages": [AIMessage(content=f"ICP analysis encountered an error: {str(e)}", name="icp_agent")],
            "last_error_message": f"ICP analysis failed: {str(e)}"
        }

def icp_agent_wrapper_sync(state: BondAIWorkflowState, config: RunnableConfig):
    """Sync wrapper for ICP subgraph with state transformation"""
    import logging

    try:
        logging.info("🎯 Starting ICP analysis (sync mode)...")
        result = icp_persona_graph.invoke(state, config)

        return {
            "messages": [AIMessage(content=result['messages'][-1].content, name="icp_agent")],
            "next": "FINISH"  # Signal completion for direct invocation
        }

    except Exception as e:
        logging.error(f"❌ ICP analysis failed: {e}")
        return {
            "messages": [AIMessage(content=f"ICP analysis encountered an error: {str(e)}", name="icp_agent")],
            "last_error_message": f"ICP analysis failed: {str(e)}"
        }

# Register the wrapper (sync version for regular workflow)
supervisor_sub_agent_registry.register_custom_agent(
    name = SupervisorSubAgents.icp_and_persona_agent.value.name,
    node_function = icp_agent_wrapper_sync,
    description = SupervisorSubAgents.icp_and_persona_agent.value.description
)


##########################################################################
######################  react default nodes BUILDER ##########################
##########################################################################
# ADD REACT AGENTS TO SUPERVISOR
# BY DEFAULT ARE ALL ENABLED
# if you want to disable an agent:
# 1) pass  ENABLED = False
# 2) call disable_agent(name)
add_agent_prebuid_react(SupervisorSubAgents.build_list_agent.value)
add_agent_prebuid_react(SupervisorSubAgents.linkedin_enrichment_agent.value)
add_agent_prebuid_react(SupervisorSubAgents.table_action_agent.value)
add_agent_prebuid_react(SupervisorSubAgents.run_column_cell_agent.value)
add_agent_prebuid_react(SupervisorSubAgents.email_phone_agent.value)








##########################################################################
###################### BOND AI WORKFLOW BUILDER ##########################
##########################################################################

def create_async_workflow_from_registry():
    """Create async workflow using the agent registry."""
    workflow = StateGraph(BondAIWorkflowState)



    # Add async nodes
    workflow.add_node("summarize_conversation", summarize_conversation)
    
    workflow.add_node("table_indexing", table_indexing_node)  # Can be async too
    
    workflow.add_edge(START, "table_indexing")
   
    # Add async supervisor, error response, and task completion nodes
    workflow.add_node("supervisor", supervisor_node)  # Now async
    workflow.add_edge("table_indexing", "supervisor")


    
    llm_agent = llm_no_thinking
    # Create async ReAct agents
    react_agents = supervisor_sub_agent_registry.get_react_agents()
    for name, config in react_agents.items():
        agent_params = {
            "name": name,
            "system_prompt": config["system_prompt"],
            "tools": config["tools"],
            "llm": llm_agent
        }

        # Check if agent has prompt_injections injections
        if config.get("prompt_injections"):
            agent_params["prompt_injections"] = config["prompt_injections"]
          
        node = create_async_react_agent(**agent_params)

        workflow.add_node(name, node)
        logging.info(f"✓ Added async ReAct agent: {name}")

    # Add custom nodes from registry (with async handling for ICP agent)
    custom_nodes = supervisor_sub_agent_registry.get_custom_nodes()
    for name, config in custom_nodes.items():
        node_function = config["node_function"]

        # Use async version for ICP agent in async workflow
        if name == "icp_agent":
            workflow.add_node(name, icp_agent_wrapper)  # Use async version
        else:
            workflow.add_node(name, node_function)

        logging.info(f"✓ Added custom node: {name}")


    
    workflow.add_node("error_response", error_response_node)
    workflow.add_node("task_completion", task_completion_node)

    # Connect all agents/nodes to supervisor
    current_members = supervisor_sub_agent_registry.get_agent_names()
    for member in current_members:
        workflow.add_edge(member, "supervisor")

  

    # Connect task completion node to supervisor
    workflow.add_edge("task_completion", "supervisor")

    # Create conditional routing
    conditional_map = {k: k for k in current_members}
    conditional_map["FINISH"] = "summarize_conversation"
    conditional_map["error_response"] = "error_response"
    conditional_map["task_completion"] = "task_completion"
    workflow.add_conditional_edges("supervisor", lambda x: x["next"], conditional_map)

    workflow.add_edge( "summarize_conversation", END)
    # TODO DEFINE RULES FOR ERROR HANDLING
    workflow.add_edge("error_response", "summarize_conversation")
    # With LangGraph API, persistence is handled automatically by the platform, so providing a custom checkpointer 
    # (type <class 'langgraph.checkpoint.memory.InMemorySaver'>) here isn't necessary and will be ignored when deployed.
    #from langgraph.checkpoint.memory import InMemorySaver
    
    return workflow.compile()


# Create async workflow instance
graph = create_async_workflow_from_registry()



##########################################################################
######################    UTILITY FOR PYTEST    ##########################
##########################################################################
def get_graph(async_mode: bool = False):
    """Get the appropriate graph based on async mode preference."""
    if async_mode:
        return graph
    return graph

# for pytest
def get_node_for_testing(node_name: str):
    """Get a node function that can be used for testing."""
    # Check if it's a ReAct agent
    react_agents = supervisor_sub_agent_registry.get_react_agents()
    if node_name in react_agents:
        config = react_agents[node_name]
        agent_params = {
            "name": node_name,
            "system_prompt": config["system_prompt"],
            "tools": config["tools"],
            "llm": llm_thinking
        }
        
        if config.get("prompt_injections"):
            agent_params["prompt_injections"] = config["prompt_injections"]
           
        return create_async_react_agent(**agent_params)
    
    # Check if it's a custom node
    custom_nodes = supervisor_sub_agent_registry.get_custom_nodes()
    if node_name in custom_nodes:
        return custom_nodes[node_name]["node_function"]
    
    # Check built-in nodes
    if node_name == "planner":
        return planner_agent
    elif node_name == "table_indexing":
        return table_indexing_node
    elif node_name == "supervisor":
        return supervisor_node
    elif node_name == "error_response":
        return error_response_node
    elif node_name == "task_completion":
        return task_completion_node
    
    raise ValueError(f"Node '{node_name}' not found in registry")
