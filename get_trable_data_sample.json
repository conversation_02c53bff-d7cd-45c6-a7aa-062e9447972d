[{"name": "LinkedIn Profile URL", "cells": [{"value": "https://www.linkedin.com/in/abudi-mohamed-846b67164/", "cell_details": {}, "injection_sequence": "{{1}}"}, {"value": null, "cell_details": {}, "injection_sequence": "{{1}}"}]}, {"name": "Linkedin Profile", "cells": [{"value": "<PERSON><PERSON>", "cell_details": [{"name": "LinkedIn Profile", "type": "linkedin_profile", "value": "<PERSON><PERSON>", "type_id": 15, "is_brand": true}, {"name": "Personal Info", "type": "object", "value": [{"name": "Full Name", "type": "full_name", "value": "<PERSON><PERSON>", "type_id": 12, "is_brand": false, "injection_sequence": "{{2.1.0}}"}, {"name": "First Name", "type": "first_name", "value": "<PERSON><PERSON>", "type_id": 13, "is_brand": false, "injection_sequence": "{{2.1.1}}"}, {"name": "Last Name", "type": "last_name", "value": "<PERSON>", "type_id": 14, "is_brand": false, "injection_sequence": "{{2.1.2}}"}, {"name": "Headline", "type": "job_title", "value": "Founder & Chief AI Officer.", "type_id": 25, "is_brand": false, "injection_sequence": "{{2.1.3}}"}, {"name": "Summary", "type": "text", "value": "I play around with AI to create cool tools.", "type_id": 1, "is_brand": false, "injection_sequence": "{{2.1.4}}"}, {"name": "Country", "type": "text", "value": "Germany", "type_id": 1, "is_brand": false, "injection_sequence": "{{2.1.5}}"}, {"name": "City", "type": "text", "value": "Berlin", "type_id": 1, "is_brand": false, "injection_sequence": "{{2.1.6}}"}, {"name": "Username", "type": "text", "value": "abu<PERSON>-mohamed-846b67164", "type_id": 1, "is_brand": false, "injection_sequence": "{{2.1.7}}"}], "type_id": 4, "is_brand": false, "injection_sequence": "{{2.1}}"}, {"name": "Experience", "type": "array", "value": [{"name": "Co-Founder", "type": "object", "value": [{"name": "Job Title", "type": "job_title", "value": "Co-Founder", "type_id": 25, "is_brand": false, "injection_sequence": "{{2.2.0.0}}"}, {"name": "Company Name", "type": "company_name", "value": "Outbond", "type_id": 24, "is_brand": false, "injection_sequence": "{{2.2.0.1}}"}, {"name": "Company URL", "type": "linkedin_company_url", "value": "https://www.linkedin.com/company/outbond", "type_id": 10, "is_brand": false, "injection_sequence": "{{2.2.0.2}}"}, {"name": "Location", "type": "text", "value": "United States", "type_id": 1, "is_brand": false, "injection_sequence": "{{2.2.0.3}}"}, {"name": "Start Date", "type": "date", "value": "2025-01-01", "type_id": 2, "is_brand": false, "injection_sequence": "{{2.2.0.4}}"}, {"name": "End Date", "type": "date", "value": "current", "type_id": 2, "is_brand": false, "injection_sequence": "{{2.2.0.5}}"}], "type_id": 4, "is_brand": false, "injection_sequence": "{{2.2.0}}"}, {"name": "AI Solution Architect & Technical Project Manager (AWS)", "type": "object", "value": [{"name": "Job Title", "type": "job_title", "value": "AI Solution Architect & Technical Project Manager (AWS)", "type_id": 25, "is_brand": false, "injection_sequence": "{{2.2.1.0}}"}, {"name": "Company Name", "type": "company_name", "value": "DCI Digital Career Institute", "type_id": 24, "is_brand": false, "injection_sequence": "{{2.2.1.1}}"}, {"name": "Company URL", "type": "linkedin_company_url", "value": "https://www.linkedin.com/school/dci-digital-career-institute", "type_id": 10, "is_brand": false, "injection_sequence": "{{2.2.1.2}}"}, {"name": "Location", "type": "text", "value": "Berlin, Germany", "type_id": 1, "is_brand": false, "injection_sequence": "{{2.2.1.3}}"}, {"name": "Start Date", "type": "date", "value": "2021-02-01", "type_id": 2, "is_brand": false, "injection_sequence": "{{2.2.1.4}}"}, {"name": "End Date", "type": "date", "value": "2024-08-31", "type_id": 2, "is_brand": false, "injection_sequence": "{{2.2.1.5}}"}], "type_id": 4, "is_brand": false, "injection_sequence": "{{2.2.1}}"}, {"name": "Automation Engineer", "type": "object", "value": [{"name": "Job Title", "type": "job_title", "value": "Automation Engineer", "type_id": 25, "is_brand": false, "injection_sequence": "{{2.2.2.0}}"}, {"name": "Company Name", "type": "company_name", "value": "heartbeat medical", "type_id": 24, "is_brand": false, "injection_sequence": "{{2.2.2.1}}"}, {"name": "Company URL", "type": "linkedin_company_url", "value": "https://www.linkedin.com/company/heartbeat-medical-solutions", "type_id": 10, "is_brand": false, "injection_sequence": "{{2.2.2.2}}"}, {"name": "Location", "type": "text", "value": "Berlin, Germany", "type_id": 1, "is_brand": false, "injection_sequence": "{{2.2.2.3}}"}, {"name": "Start Date", "type": "date", "value": "2020-02-01", "type_id": 2, "is_brand": false, "injection_sequence": "{{2.2.2.4}}"}, {"name": "End Date", "type": "date", "value": "2021-02-28", "type_id": 2, "is_brand": false, "injection_sequence": "{{2.2.2.5}}"}], "type_id": 4, "is_brand": false, "injection_sequence": "{{2.2.2}}"}, {"name": "Automation Engineer", "type": "object", "value": [{"name": "Job Title", "type": "job_title", "value": "Automation Engineer", "type_id": 25, "is_brand": false, "injection_sequence": "{{2.2.3.0}}"}, {"name": "Company Name", "type": "company_name", "value": "German Autolabs", "type_id": 24, "is_brand": false, "injection_sequence": "{{2.2.3.1}}"}, {"name": "Company URL", "type": "linkedin_company_url", "value": "https://www.linkedin.com/company/german-autolabs", "type_id": 10, "is_brand": false, "injection_sequence": "{{2.2.3.2}}"}, {"name": "Location", "type": "text", "value": "Berlin Area, Germany", "type_id": 1, "is_brand": false, "injection_sequence": "{{2.2.3.3}}"}, {"name": "Start Date", "type": "date", "value": "2019-01-01", "type_id": 2, "is_brand": false, "injection_sequence": "{{2.2.3.4}}"}, {"name": "End Date", "type": "date", "value": "2020-01-31", "type_id": 2, "is_brand": false, "injection_sequence": "{{2.2.3.5}}"}], "type_id": 4, "is_brand": false, "injection_sequence": "{{2.2.3}}"}, {"name": "Working Student Banking Operations", "type": "object", "value": [{"name": "Job Title", "type": "job_title", "value": "Working Student Banking Operations", "type_id": 25, "is_brand": false, "injection_sequence": "{{2.2.4.0}}"}, {"name": "Company Name", "type": "company_name", "value": "N26", "type_id": 24, "is_brand": false, "injection_sequence": "{{2.2.4.1}}"}, {"name": "Company URL", "type": "linkedin_company_url", "value": "https://www.linkedin.com/company/n26", "type_id": 10, "is_brand": false, "injection_sequence": "{{2.2.4.2}}"}, {"name": "Location", "type": "text", "value": "Berlin Area, Germany", "type_id": 1, "is_brand": false, "injection_sequence": "{{2.2.4.3}}"}, {"name": "Start Date", "type": "date", "value": "2018-01-01", "type_id": 2, "is_brand": false, "injection_sequence": "{{2.2.4.4}}"}, {"name": "End Date", "type": "date", "value": "2018-12-31", "type_id": 2, "is_brand": false, "injection_sequence": "{{2.2.4.5}}"}], "type_id": 4, "is_brand": false, "injection_sequence": "{{2.2.4}}"}], "type_id": 5, "is_brand": false, "injection_sequence": "{{2.2}}"}, {"name": "Education", "type": "array", "value": [{"name": "Hochschule für Technik und Wirtschaft Berlin", "type": "object", "value": [{"name": "School", "type": "company_name", "value": "Hochschule für Technik und Wirtschaft Berlin", "type_id": 24, "is_brand": false, "injection_sequence": "{{*******}}"}, {"name": "Field of Study", "type": "text", "value": "Computer Science", "type_id": 1, "is_brand": false, "injection_sequence": "{{*******}}"}, {"name": "Degree", "type": "text", "value": "Bachelor's degree", "type_id": 1, "is_brand": false, "injection_sequence": "{{*******}}"}, {"name": "Description", "type": "text", "value": null, "type_id": 1, "is_brand": false, "injection_sequence": "{{*******}}"}, {"name": "School URL", "type": "linkedin_company_url", "value": "https://www.linkedin.com/school/htwberlin", "type_id": 10, "is_brand": false, "injection_sequence": "{{*******}}"}, {"name": "Start Date", "type": "date", "value": "2018-01-01", "type_id": 2, "is_brand": false, "injection_sequence": "{{*******}}"}, {"name": "End Date", "type": "date", "value": "2021-01-31", "type_id": 2, "is_brand": false, "injection_sequence": "{{*******}}"}], "type_id": 4, "is_brand": false, "injection_sequence": "{{2.3.0}}"}, {"name": "Nahda University - NUB", "type": "object", "value": [{"name": "School", "type": "company_name", "value": "Nahda University - NUB", "type_id": 24, "is_brand": false, "injection_sequence": "{{*******}}"}, {"name": "Field of Study", "type": "text", "value": "Civil Engineering", "type_id": 1, "is_brand": false, "injection_sequence": "{{*******}}"}, {"name": "Degree", "type": "text", "value": "Bachelor's degree", "type_id": 1, "is_brand": false, "injection_sequence": "{{*******}}"}, {"name": "Description", "type": "text", "value": null, "type_id": 1, "is_brand": false, "injection_sequence": "{{*******}}"}, {"name": "School URL", "type": "linkedin_company_url", "value": "https://www.linkedin.com/school/nahda-university---nub", "type_id": 10, "is_brand": false, "injection_sequence": "{{*******}}"}, {"name": "Start Date", "type": "date", "value": "2012-01-01", "type_id": 2, "is_brand": false, "injection_sequence": "{{*******}}"}, {"name": "End Date", "type": "date", "value": "2015-01-31", "type_id": 2, "is_brand": false, "injection_sequence": "{{*******}}"}], "type_id": 4, "is_brand": false, "injection_sequence": "{{2.3.1}}"}], "type_id": 5, "is_brand": false, "injection_sequence": "{{2.3}}"}, {"name": "Certifications", "type": "array", "value": [{"name": "AWS re/Start Accredited Instructor", "type": "object", "value": [{"name": "Name", "type": "text", "value": "AWS re/Start Accredited Instructor", "type_id": 1, "is_brand": false, "injection_sequence": "{{*******}}"}, {"name": "Authority", "type": "text", "value": "Amazon Web Services (AWS)", "type_id": 1, "is_brand": false, "injection_sequence": "{{2.4.0.1}}"}, {"name": "Start Date", "type": "date", "value": "2022-06-01", "type_id": 2, "is_brand": false, "injection_sequence": "{{2.4.0.2}}"}, {"name": "End Date", "type": "date", "value": "2025-05-31", "type_id": 2, "is_brand": false, "injection_sequence": "{{2.4.0.3}}"}], "type_id": 4, "is_brand": false, "injection_sequence": "{{2.4.0}}"}, {"name": "AWS Certified Solutions Architect – Associate", "type": "object", "value": [{"name": "Name", "type": "text", "value": "AWS Certified Solutions Architect – Associate", "type_id": 1, "is_brand": false, "injection_sequence": "{{2.4.1.0}}"}, {"name": "Authority", "type": "text", "value": "Amazon Web Services (AWS)", "type_id": 1, "is_brand": false, "injection_sequence": "{{2.4.1.1}}"}, {"name": "Start Date", "type": "date", "value": "2022-05-01", "type_id": 2, "is_brand": false, "injection_sequence": "{{2.4.1.2}}"}, {"name": "End Date", "type": "date", "value": "2025-05-31", "type_id": 2, "is_brand": false, "injection_sequence": "{{2.4.1.3}}"}], "type_id": 4, "is_brand": false, "injection_sequence": "{{2.4.1}}"}, {"name": "AWS Certified Cloud Practitioner", "type": "object", "value": [{"name": "Name", "type": "text", "value": "AWS Certified Cloud Practitioner", "type_id": 1, "is_brand": false, "injection_sequence": "{{2.4.2.0}}"}, {"name": "Authority", "type": "text", "value": "Amazon Web Services (AWS)", "type_id": 1, "is_brand": false, "injection_sequence": "{{2.4.2.1}}"}, {"name": "Start Date", "type": "date", "value": "2022-02-01", "type_id": 2, "is_brand": false, "injection_sequence": "{{2.4.2.2}}"}, {"name": "End Date", "type": "date", "value": "2025-02-28", "type_id": 2, "is_brand": false, "injection_sequence": "{{2.4.2.3}}"}], "type_id": 4, "is_brand": false, "injection_sequence": "{{2.4.2}}"}], "type_id": 5, "is_brand": false, "injection_sequence": "{{2.4}}"}], "injection_sequence": "{{2}}"}, {"value": null, "cell_details": {}, "injection_sequence": "{{2}}"}]}, {"name": "Linkedin Company", "cells": [{"value": "Outbond", "cell_details": [{"name": "LinkedIn company profile", "type": "linkedin_company", "value": "Outbond", "type_id": 16, "is_brand": true}, {"name": "General info", "type": "object", "value": [{"name": "Name", "type": "company_name", "value": "Outbond", "type_id": 24, "is_brand": false, "injection_sequence": "{{3.1.0}}"}, {"name": "LinkedIn URL", "type": "linkedin_company_url", "value": "https://www.linkedin.com/company/outbond", "type_id": 10, "is_brand": false, "injection_sequence": "{{3.1.1}}"}, {"name": "Website", "type": "company_domain", "value": "https://www.outbond.io/", "type_id": 11, "is_brand": false, "injection_sequence": "{{3.1.2}}"}, {"name": "Tagline", "type": "text", "value": "Grow Your Revenue with Personalized AI Outreach", "type_id": 1, "is_brand": false, "injection_sequence": "{{3.1.3}}"}, {"name": "Description", "type": "text", "value": "Grow Your Revenue With Personalized AI Outreach", "type_id": 1, "is_brand": false, "injection_sequence": "{{3.1.4}}"}, {"name": "Type", "type": "text", "value": null, "type_id": 1, "is_brand": false, "injection_sequence": "{{3.1.5}}"}, {"name": "Phone", "type": "phone", "value": "", "type_id": 7, "is_brand": false, "injection_sequence": "{{3.1.6}}"}, {"name": "Staff count", "type": "number", "value": 2, "type_id": 3, "is_brand": false, "injection_sequence": "{{3.1.7}}"}, {"name": "Follower count", "type": "number", "value": 38, "type_id": 3, "is_brand": false, "injection_sequence": "{{3.1.8}}"}, {"name": "ID", "type": "text", "value": "*********", "type_id": 1, "is_brand": false, "injection_sequence": "{{3.1.9}}"}, {"name": "Founded year", "type": "number", "value": null, "type_id": 3, "is_brand": false, "injection_sequence": "{{3.1.10}}"}, {"name": "Crunchbase URL", "type": "text", "value": "", "type_id": 1, "is_brand": false, "injection_sequence": "{{3.1.11}}"}, {"name": "Logo", "type": "text", "value": "https://s3.us-west-000.backblazeb2.com/proxycurl/company/outbond/profile?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0004d7f56a0400b0000000001%2F20250421%2Fus-west-000%2Fs3%2Faws4_request&X-Amz-Date=20250421T145828Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=68b355cc5bdc51c1862de32f7259bff220f89c10309a89b5d0aaa96cb998898f", "type_id": 1, "is_brand": false, "injection_sequence": "{{3.1.12}}"}], "type_id": 4, "is_brand": false, "injection_sequence": "{{3.1}}"}, {"name": "Industry", "type": "text", "value": "Technology, Information and Internet", "type_id": 1, "is_brand": false, "injection_sequence": "{{3.2}}"}], "injection_sequence": "{{3}}"}, {"value": null, "cell_details": {}, "injection_sequence": "{{3}}"}]}, {"name": "Email", "cells": [{"value": "<EMAIL>", "cell_details": [{"name": "Email", "type": "email", "value": "<EMAIL>", "type_id": 8, "is_brand": true}, {"name": "Source", "type": "text", "value": "Prospeo", "type_id": 1, "is_brand": false}, {"name": "Prospeo Email Status", "type": "text", "value": "VALID", "type_id": 1, "is_brand": false}, [{"name": "Validation", "type": "object", "value": [{"name": "Source", "type": "text", "value": "Millionverifier", "type_id": 1, "is_brand": false}, {"name": "Quality", "type": "text", "value": "good", "type_id": 1, "is_brand": false}], "type_id": 4, "is_brand": false}]], "injection_sequence": "{{4}}"}, {"value": null, "cell_details": {}, "injection_sequence": "{{4}}"}]}, {"name": "Phone", "cells": [{"value": null, "cell_details": {}, "injection_sequence": "{{5}}"}, {"value": null, "cell_details": {}, "injection_sequence": "{{5}}"}]}]