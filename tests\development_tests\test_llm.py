from dotenv import load_dotenv
import json
import asyncio
import os
import sys
import httpx

# Add the project root to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

# Now use absolute imports instead of relative imports
from src.backend_server.services.llm import (
    run_only_run_if,
    run_ai_column,
    run_ai_formula,
)
from src.backend_server.models.requests import AIColumnRequest
from src.backend_server.db.cells import get_supabase_client

load_dotenv()


async def test_run_only_run_if():
    # Test formula similar to the one used in the LangSmith test
    test_formula = """
    <User formula>
    Only run if
    The company works in education sector {{var1}}
    </User formula>
    <Values from the backend>
    {{var1}} = Are you looking for a job with a strong social impact? 💙 We at the Digital Career Institute have been offering a wide range of training courses as a tech school since 2016.
    </Values from the backend>"""

    print("\n\nTesting run_only_run_if function:")
    try:
        result = await run_only_run_if(test_formula)
        print("Result type:", type(result))

        # Handle both dictionary and object return types
        if isinstance(result, dict):
            print("Result content:", json.dumps(result, indent=4))
        else:
            # Try to convert to dict if it's not already a dict
            try:
                result_dict = result.dict() if hasattr(result, "dict") else vars(result)
                print("Result content:", json.dumps(result_dict, indent=4))
            except:
                print("Result (not JSON serializable):", result)

    except Exception as e:
        print(f"Error testing run_only_run_if: {str(e)}")
        import traceback

        traceback.print_exc()


async def test_run_ai_column():
    """
    Test the run_ai_column function with a sample research request.
    This function creates a mock ResearchRequest and passes it to run_ai_column.
    """
    print("\n\nTesting run_ai_column function:")

    # Create a sample research request
    request = AIColumnRequest(
        table_id="tbl_3655e99e74169376",
        column_id=7,
        row_id=16,
        service_id=13,
        organization_id="org_64bd7f51c1450d62",
        user_prompt="Combine Abudi and Mohamed into full name",
        # system_prompt="You are a test AI assistant that always returns a response with reasoning so that you simulate your capabilities",
        formula=None,  # No conditional formula for this test
        credits=10,
        run_id=1,  # Adding the missing required field
        # Add any other required fields for ResearchRequest
    )

    try:
        # We'll mock the supabase client for testing purposes
        # In a real test, you might want to use a proper mock or test database
        mock_supabase = get_supabase_client()  # Replace with a proper mock if needed

        # Call the function
        await run_ai_formula(request, mock_supabase)

        # Since run_ai_column doesn't return a value but prints results,
        # we'll see the output in the console
        print("run_ai_column test completed - check console output for results")

    except Exception as e:
        print(f"Error testing run_ai_column: {str(e)}")
        import traceback

        traceback.print_exc()


# Run the tests
if __name__ == "__main__":
    # Run the run_only_run_if test
    # asyncio.run(test_run_only_run_if())

    # Run the run_ai_column test
    asyncio.run(test_run_ai_column())
