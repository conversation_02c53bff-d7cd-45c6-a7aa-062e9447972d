from typing import Optional
from pydantic import BaseModel, ConfigDict
from src.schemas.linkedin import (
    LinkedinPersonProfileUrl,
)


class PhoneFinderOutput(BaseModel):
    model_config = ConfigDict(extra="ignore")
    mobile_number: Optional[int] = None
    profile_url: LinkedinPersonProfileUrl

    # github_profile_id: Optional[str] = None
    # facebook_profile_id: Optional[str] = None
    # twitter_profile_id: Optional[str] = None


class PhoneFinderResponse(BaseModel):
    model_config = ConfigDict(extra="ignore")
    phone_number: int
    credit_cost: int


class PhoneFinderInput(BaseModel):
    model_config = ConfigDict(extra="ignore")
    linkedin_profile_url: LinkedinPersonProfileUrl
