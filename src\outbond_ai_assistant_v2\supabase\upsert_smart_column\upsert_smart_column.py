"""Database upsert operations for smart column management."""

from ..client import supabase
from .model import UpsertSmartColumnRequest, UpsertSmartColumnResponse
from typing import Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg
from ...configuration import Configuration


def upsert_smart_column(
    config: Annotated[RunnableConfig, InjectedToolArg],
    request: UpsertSmartColumnRequest
) -> UpsertSmartColumnResponse:
    """Create or update a smart column in the database."""
    try:
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id

        # Convert settings to dictionary for the RPC call
        settings_dict = request.settings.model_dump(mode='json') if request.settings else None
        
        response = supabase.rpc('upsert_smart_column', {
            "p_table_id": table_id,
            "p_name": request.column_name,
            "p_service_id": request.service_id,
            "p_settings": settings_dict,
            "p_id": request.column_id
        }).execute()
        
        # Success case: RPC returns data with column
        if hasattr(response, 'data') and response.data and 'column' in response.data:
            return UpsertSmartColumnResponse(
                column=response.data['column'],
                success=True
            )
        
        # Unexpected response format
        return UpsertSmartColumnResponse(
            error_message=f"Unexpected response from upsert service for column '{request.column_name}'",
            success=False
        )
            
    except Exception as error:
        # Error case
        return UpsertSmartColumnResponse(
            error_message=f"Failed to upsert column '{request.column_name}': {str(error)}",
            success=False
        )
