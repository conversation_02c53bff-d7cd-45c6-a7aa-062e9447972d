from typing import Literal
import uuid
import json
from datetime import datetime
import sys
import os

# Add the project root to path to fix imports
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from langchain_core.tools import tool
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.checkpoint.postgres import PostgresSaver
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver

# Now import should work with proper path
from src.outbond_ai_assistant.graph import graph
from src.outbond_ai_assistant.configuration import Configuration


DB_URI = "**************************************************************************/postgres"
connection_kwargs = {
    "autocommit": True,
    "prepare_threshold": 0,
}


from psycopg_pool import ConnectionPool

with ConnectionPool(
    conninfo=DB_URI,
    max_size=20,
    kwargs=connection_kwargs,
) as pool:
    checkpointer = PostgresSaver(pool)

    # NOTE: you need to call .setup() the first time you're using your checkpointer
    checkpointer.setup()

    # Use the provided thread_id
    thread_id = "843efded-e12a-478a-a921-9a47852fe602"
    
    # Customize graph to use our checkpointer
    graph.checkpointer = checkpointer
    
    # Create config with thread_id and other required fields
    config = {
        "configurable": {
            "thread_id": thread_id,
            "checkpoint_ns": "default",
            "model": "openai/gpt-4o-mini",
            "table_id": "tbl_03e5cbb7662489e9"
        }
    }
    
    # Invoke the graph with a message
    res = graph.invoke({"messages": [HumanMessage(content="What can you tell me about the data in this table?")]}, config)
    
    # Get checkpoint
    checkpoint = checkpointer.get(config)
    
    # Print conversation in a readable format
    print("\n=== CONVERSATION FROM RESULT ===")
    for msg in res["messages"]:
        role = msg.__class__.__name__.replace('Message', '')
        content = msg.content
        
        # Handle tool calls specifically
        if hasattr(msg, 'tool_calls') and msg.tool_calls:
            for tool_call in msg.tool_calls:
                print(f"{role}: Calling tool '{tool_call['name']}' with args: {tool_call['args']}")
        # Handle tool messages
        elif hasattr(msg, 'name') and msg.name:
            print(f"Tool({msg.name}): {content}")
        # Regular messages
        else:
            print(f"{role}: {content}")
    
    print("\n=== CONVERSATION FROM CHECKPOINT ===")
    if checkpoint is None:
        print("No checkpoint data available - checkpoint was None")
    else:
        for msg in checkpoint['channel_values']['messages']:
            role = msg.__class__.__name__.replace('Message', '')
            content = msg.content
            
            # Handle tool calls specifically
            if hasattr(msg, 'tool_calls') and msg.tool_calls:
                for tool_call in msg.tool_calls:
                    print(f"{role}: Calling tool '{tool_call['name']}' with args: {tool_call['args']}")
            # Handle tool messages
            elif hasattr(msg, 'name') and msg.name:
                print(f"Tool({msg.name}): {content}")
            # Regular messages
            else:
                print(f"{role}: {content}")