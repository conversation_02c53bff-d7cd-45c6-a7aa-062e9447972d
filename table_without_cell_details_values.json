[{"name": "LinkedIn Profile URL", "cells": [{"cell_details": {}, "injection_sequence": "{{1}}"}, {"value": null, "cell_details": {}, "injection_sequence": "{{1}}"}]}, {"name": "Linkedin Profile", "cells": [{"cell_details": [{"name": "LinkedIn Profile"}, {"name": "Personal Info", "value": [{"name": "Full Name", "injection_sequence": "{{2.1.0}}"}, {"name": "First Name", "injection_sequence": "{{2.1.1}}"}, {"name": "Last Name", "injection_sequence": "{{2.1.2}}"}, {"name": "Headline", "injection_sequence": "{{2.1.3}}"}, {"name": "Summary", "injection_sequence": "{{2.1.4}}"}, {"name": "Country", "injection_sequence": "{{2.1.5}}"}, {"name": "City", "injection_sequence": "{{2.1.6}}"}, {"name": "Username", "injection_sequence": "{{2.1.7}}"}], "injection_sequence": "{{2.1}}"}, {"name": "Experience", "value": [{"name": "Co-Founder", "value": [{"name": "Job Title", "injection_sequence": "{{2.2.0.0}}"}, {"name": "Company Name", "injection_sequence": "{{2.2.0.1}}"}, {"name": "Company URL", "injection_sequence": "{{2.2.0.2}}"}, {"name": "Location", "injection_sequence": "{{2.2.0.3}}"}, {"name": "Start Date", "injection_sequence": "{{2.2.0.4}}"}, {"name": "End Date", "injection_sequence": "{{2.2.0.5}}"}], "injection_sequence": "{{2.2.0}}"}, {"name": "AI Solution Architect & Technical Project Manager (AWS)", "value": [{"name": "Job Title", "injection_sequence": "{{2.2.1.0}}"}, {"name": "Company Name", "injection_sequence": "{{2.2.1.1}}"}, {"name": "Company URL", "injection_sequence": "{{2.2.1.2}}"}, {"name": "Location", "injection_sequence": "{{2.2.1.3}}"}, {"name": "Start Date", "injection_sequence": "{{2.2.1.4}}"}, {"name": "End Date", "injection_sequence": "{{2.2.1.5}}"}], "injection_sequence": "{{2.2.1}}"}, {"name": "Automation Engineer", "value": [{"name": "Job Title", "injection_sequence": "{{2.2.2.0}}"}, {"name": "Company Name", "injection_sequence": "{{2.2.2.1}}"}, {"name": "Company URL", "injection_sequence": "{{2.2.2.2}}"}, {"name": "Location", "injection_sequence": "{{2.2.2.3}}"}, {"name": "Start Date", "injection_sequence": "{{2.2.2.4}}"}, {"name": "End Date", "injection_sequence": "{{2.2.2.5}}"}], "injection_sequence": "{{2.2.2}}"}, {"name": "Automation Engineer", "value": [{"name": "Job Title", "injection_sequence": "{{2.2.3.0}}"}, {"name": "Company Name", "injection_sequence": "{{2.2.3.1}}"}, {"name": "Company URL", "injection_sequence": "{{2.2.3.2}}"}, {"name": "Location", "injection_sequence": "{{2.2.3.3}}"}, {"name": "Start Date", "injection_sequence": "{{2.2.3.4}}"}, {"name": "End Date", "injection_sequence": "{{2.2.3.5}}"}], "injection_sequence": "{{2.2.3}}"}, {"name": "Working Student Banking Operations", "value": [{"name": "Job Title", "injection_sequence": "{{2.2.4.0}}"}, {"name": "Company Name", "injection_sequence": "{{2.2.4.1}}"}, {"name": "Company URL", "injection_sequence": "{{2.2.4.2}}"}, {"name": "Location", "injection_sequence": "{{*******}}"}, {"name": "Start Date", "injection_sequence": "{{*******}}"}, {"name": "End Date", "injection_sequence": "{{*******}}"}], "injection_sequence": "{{2.2.4}}"}], "injection_sequence": "{{2.2}}"}, {"name": "Education", "value": [{"name": "Hochschule für Technik und Wirtschaft Berlin", "value": [{"name": "School", "injection_sequence": "{{*******}}"}, {"name": "Field of Study", "injection_sequence": "{{*******}}"}, {"name": "Degree", "injection_sequence": "{{*******}}"}, {"name": "Description", "injection_sequence": "{{*******}}"}, {"name": "School URL", "injection_sequence": "{{*******}}"}, {"name": "Start Date", "injection_sequence": "{{*******}}"}, {"name": "End Date", "injection_sequence": "{{*******}}"}], "injection_sequence": "{{2.3.0}}"}, {"name": "Nahda University - NUB", "value": [{"name": "School", "injection_sequence": "{{*******}}"}, {"name": "Field of Study", "injection_sequence": "{{*******}}"}, {"name": "Degree", "injection_sequence": "{{*******}}"}, {"name": "Description", "injection_sequence": "{{*******}}"}, {"name": "School URL", "injection_sequence": "{{*******}}"}, {"name": "Start Date", "injection_sequence": "{{*******}}"}, {"name": "End Date", "injection_sequence": "{{*******}}"}], "injection_sequence": "{{2.3.1}}"}], "injection_sequence": "{{2.3}}"}, {"name": "Certifications", "value": [{"name": "AWS re/Start Accredited Instructor", "value": [{"name": "Name", "injection_sequence": "{{*******}}"}, {"name": "Authority", "injection_sequence": "{{*******}}"}, {"name": "Start Date", "injection_sequence": "{{*******}}"}, {"name": "End Date", "injection_sequence": "{{*******}}"}], "injection_sequence": "{{2.4.0}}"}, {"name": "AWS Certified Solutions Architect – Associate", "value": [{"name": "Name", "injection_sequence": "{{*******}}"}, {"name": "Authority", "injection_sequence": "{{*******}}"}, {"name": "Start Date", "injection_sequence": "{{*******}}"}, {"name": "End Date", "injection_sequence": "{{*******}}"}], "injection_sequence": "{{2.4.1}}"}, {"name": "AWS Certified Cloud Practitioner", "type": "object", "value": [{"name": "Name", "type": "text", "value": "AWS Certified Cloud Practitioner", "type_id": 1, "is_brand": false, "injection_sequence": "{{2.4.2.0}}"}, {"name": "Authority", "type": "text", "value": "Amazon Web Services (AWS)", "type_id": 1, "is_brand": false, "injection_sequence": "{{2.4.2.1}}"}, {"name": "Start Date", "type": "date", "value": "2022-02-01", "type_id": 2, "is_brand": false, "injection_sequence": "{{2.4.2.2}}"}, {"name": "End Date", "type": "date", "value": "2025-02-28", "type_id": 2, "is_brand": false, "injection_sequence": "{{2.4.2.3}}"}], "type_id": 4, "is_brand": false, "injection_sequence": "{{2.4.2}}"}], "type_id": 5, "is_brand": false, "injection_sequence": "{{2.4}}"}], "injection_sequence": "{{2}}"}, {"value": null, "cell_details": {}, "injection_sequence": "{{2}}"}]}, {"name": "Linkedin Company", "cells": [{"value": "Outbond", "cell_details": [{"name": "LinkedIn company profile", "type": "linkedin_company", "value": "Outbond", "type_id": 16, "is_brand": true}, {"name": "General info", "type": "object", "value": [{"name": "Name", "type": "company_name", "value": "Outbond", "type_id": 24, "is_brand": false, "injection_sequence": "{{3.1.0}}"}, {"name": "LinkedIn URL", "type": "linkedin_company_url", "value": "https://www.linkedin.com/company/outbond", "type_id": 10, "is_brand": false, "injection_sequence": "{{3.1.1}}"}, {"name": "Website", "type": "company_domain", "value": "https://www.outbond.io/", "type_id": 11, "is_brand": false, "injection_sequence": "{{3.1.2}}"}, {"name": "Tagline", "type": "text", "value": "Grow Your Revenue with Personalized AI Outreach", "type_id": 1, "is_brand": false, "injection_sequence": "{{3.1.3}}"}, {"name": "Description", "type": "text", "value": "Grow Your Revenue With Personalized AI Outreach", "type_id": 1, "is_brand": false, "injection_sequence": "{{3.1.4}}"}, {"name": "Type", "type": "text", "value": null, "type_id": 1, "is_brand": false, "injection_sequence": "{{3.1.5}}"}, {"name": "Phone", "type": "phone", "value": "", "type_id": 7, "is_brand": false, "injection_sequence": "{{3.1.6}}"}, {"name": "Staff count", "type": "number", "value": 2, "type_id": 3, "is_brand": false, "injection_sequence": "{{3.1.7}}"}, {"name": "Follower count", "type": "number", "value": 38, "type_id": 3, "is_brand": false, "injection_sequence": "{{3.1.8}}"}, {"name": "ID", "type": "text", "value": "*********", "type_id": 1, "is_brand": false, "injection_sequence": "{{3.1.9}}"}, {"name": "Founded year", "type": "number", "value": null, "type_id": 3, "is_brand": false, "injection_sequence": "{{3.1.10}}"}, {"name": "Crunchbase URL", "type": "text", "value": "", "type_id": 1, "is_brand": false, "injection_sequence": "{{3.1.11}}"}, {"name": "Logo", "type": "text", "value": "https://s3.us-west-000.backblazeb2.com/proxycurl/company/outbond/profile?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0004d7f56a0400b0000000001%2F20250421%2Fus-west-000%2Fs3%2Faws4_request&X-Amz-Date=20250421T145828Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=68b355cc5bdc51c1862de32f7259bff220f89c10309a89b5d0aaa96cb998898f", "type_id": 1, "is_brand": false, "injection_sequence": "{{3.1.12}}"}], "type_id": 4, "is_brand": false, "injection_sequence": "{{3.1}}"}, {"name": "Industry", "type": "text", "value": "Technology, Information and Internet", "type_id": 1, "is_brand": false, "injection_sequence": "{{3.2}}"}], "injection_sequence": "{{3}}"}, {"value": null, "cell_details": {}, "injection_sequence": "{{3}}"}]}, {"name": "Email", "cells": [{"value": "<EMAIL>", "cell_details": [{"name": "Email", "type": "email", "value": "<EMAIL>", "type_id": 8, "is_brand": true}, {"name": "Source", "type": "text", "value": "Prospeo", "type_id": 1, "is_brand": false}, {"name": "Prospeo Email Status", "type": "text", "value": "VALID", "type_id": 1, "is_brand": false}, [{"name": "Validation", "type": "object", "value": [{"name": "Source", "type": "text", "value": "Millionverifier", "type_id": 1, "is_brand": false}, {"name": "Quality", "type": "text", "value": "good", "type_id": 1, "is_brand": false}], "type_id": 4, "is_brand": false}]], "injection_sequence": "{{4}}"}, {"value": null, "cell_details": {}, "injection_sequence": "{{4}}"}]}, {"name": "Phone", "cells": [{"value": null, "cell_details": {}, "injection_sequence": "{{5}}"}, {"value": null, "cell_details": {}, "injection_sequence": "{{5}}"}]}]