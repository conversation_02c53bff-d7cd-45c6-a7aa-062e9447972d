import requests
import os
from dotenv import load_dotenv
import json

# Load environment variables
load_dotenv()

def get_linkedin_post_and_comments(post_urn=None):
    """
    Fetch a LinkedIn post and its comments using the RapidAPI LinkedIn API.
    
    Args:
        post_urn (str, optional): The URN of the post to fetch. Defaults to None.
    
    Returns:
        dict: The JSON response from the API containing post data and comments
    """
    # Get API key from environment variables
    api_key = os.getenv("LINKEDIN_RAPIDAPI_KEY")
    
    if not api_key:
        raise ValueError("LINKEDIN_RAPIDAPI_KEY not found in environment variables")
    
    # Set default URN if none provided
    if post_urn is None:
        post_urn = "7323683950298632193"  # Default URN from example
    
    url = f"https://linkedin-api8.p.rapidapi.com/get-profile-post-and-comments"
    
    # Query parameters
    params = {"urn": post_urn}
    
    # Headers
    headers = {
        "x-rapidapi-host": "linkedin-api8.p.rapidapi.com",
        "x-rapidapi-key": api_key
    }
    
    # Make the request
    response = requests.get(url, headers=headers, params=params)
    
    # Check if request was successful
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error: {response.status_code}")
        print(response.text)
        return None

if __name__ == "__main__":
    # Example usage
    post_data = get_linkedin_post_and_comments()
    if post_data:
        print(json.dumps(post_data, indent=2))
