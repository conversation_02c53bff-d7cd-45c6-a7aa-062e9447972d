from pydantic import BaseModel, Field, ConfigDict
from typing import Optional
from typing import Dict, Optional, Any



class CreateNormalColumnRequest(BaseModel):
    """Request model for creating a new normal (non-smart) column.
    
    This model defines the structure for creating normal columns with basic
    properties like name, type, and optional index.
    """
    model_config = ConfigDict(use_enum_values=True)
    
    table_id: str = Field(
        ...,
        description="The unique identifier of the table to add the column to",
        min_length=1,
        examples=["tbl_c85117eb4106d464"]
    )
    
    column_name: str = Field(
        ...,
        description="Name of the normal column",
        min_length=1,
        max_length=255,
        examples=["Name", "Email", "Phone", "Company", "Title", "Notes"]
    )
    
    type_id: int = Field(
        ...,
        description="ID of the column type: 1=text, 2=date, 3=number",
        ge=1,
        le=3,
        examples=[
            1,  # Text: Default
            2,  # Date
            3   # Number
        ]
    )



class CreateNormalColumnResponse(BaseModel):
    """Response model for normal column creation operations."""
    model_config = ConfigDict(use_enum_values=True)
    
    column: Optional[Dict[str, Any]] = Field(
        default=None,
        description="The complete column data when creation is successful"
    )
    
    error_message: Optional[str] = Field(
        default=None,
        description="Error message when creation fails"
    )
    
    success: bool = Field(
        default=True,
        description="Indicates if the creation was successful"
    )




# Column type constants for better readability
class ColumnType:
    """Constants for column types."""
    TEXT = 1
    DATE = 2
    NUMBER = 3
