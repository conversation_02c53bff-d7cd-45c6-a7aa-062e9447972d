import re
from pydantic_core import core_schema
from pydantic import GetCoreSchemaHandler
from typing import Any


class LinkedinPersonProfileUrl(str):
    """
    A custom Pydantic data type for validating and normalizing LinkedIn profile URLs.

    Features:
    - Converts HTTP to HTTPS
    - Adds www if missing
    - Removes country-specific subdomains (e.g., fr.linkedin.com -> www.linkedin.com)
    - Ensures URL has /in/ path
    - Removes trailing slashes
    """

    @classmethod
    def __get_pydantic_core_schema__(
        cls, source_type: Any, handler: GetCoreSchemaHandler
    ) -> core_schema.CoreSchema:
        return core_schema.with_info_before_validator_function(
            cls._validate_and_normalize_url,
            core_schema.str_schema(),
        )

    @classmethod
    def _validate_and_normalize_url(
        cls, url: str, _info: core_schema.ValidationInfo
    ) -> str:
        """
        Validate and normalize LinkedIn profile URLs.

        The key pattern to match is 'linkedin.com/in/<username>'
        Automatically handles:
        - HTTP vs HTTPS (converts to HTTPS)
        - Missing www (adds it)
        - Country-specific subdomains (removes them)
        - Trailing slashes (removes them)
        """
        if not url:
            raise ValueError("LinkedIn URL cannot be empty")

        # Convert to string and strip whitespace
        url = str(url).strip()

        # Replace HTTP with HTTPS
        if url.startswith("http://"):
            url = "https://" + url[7:]
        elif not url.startswith("https://"):
            url = "https://" + url

        # Extract the core pattern: linkedin.com/in/<username>
        pattern = r"https://(?:www\.|([a-z]{2})\.)?linkedin\.com/in/([a-zA-Z0-9\-_.]+)"
        match = re.match(pattern, url)

        if not match:
            raise ValueError(
                f"Invalid LinkedIn profile URL: {url}. Must match pattern 'linkedin.com/in/<username>'"
            )

        # Get the username
        username = match.group(2)

        if not username:
            raise ValueError("LinkedIn URL must include a username after /in/")

        # Reconstruct the normalized URL with www and without trailing slash
        return f"https://www.linkedin.com/in/{username}"


class LinkedinCompanyProfileUrl(str):
    """
    A custom Pydantic data type for validating and normalizing LinkedIn company URLs.

    Features:
    - Converts HTTP to HTTPS
    - Adds www if missing
    - Removes country-specific subdomains (e.g., fr.linkedin.com -> www.linkedin.com)
    - Ensures URL has /in/ path
    - Removes trailing slashes
    """

    @classmethod
    def __get_pydantic_core_schema__(
        cls, source_type: Any, handler: GetCoreSchemaHandler
    ) -> core_schema.CoreSchema:
        return core_schema.with_info_before_validator_function(
            cls._validate_and_normalize_url,
            core_schema.str_schema(),
        )

    @classmethod
    def _validate_and_normalize_url(
        cls, url: str, _info: core_schema.ValidationInfo
    ) -> str:
        """
        Validate and normalize LinkedIn company URLs.

        The key pattern to match is 'linkedin.com/company/<company>'
        Automatically handles:
        - HTTP vs HTTPS (converts to HTTPS)
        - Missing www (adds it)
        - Country-specific subdomains (removes them)
        - Trailing slashes (removes them)
        """
        if not url:
            raise ValueError("LinkedIn URL cannot be empty")

        # Convert to string and strip whitespace
        url = str(url).strip()

        # Replace HTTP with HTTPS
        if url.startswith("http://"):
            url = "https://" + url[7:]
        elif not url.startswith("https://"):
            url = "https://" + url

        # Extract the core pattern: linkedin.com/company/<company>
        pattern = (
            r"https://(?:www\.|([a-z]{2})\.)?linkedin\.com/company/([a-zA-Z0-9\-_.]+)"
        )
        match = re.match(pattern, url)

        if not match:
            raise ValueError(
                f"Invalid LinkedIn company URL: {url}. Must match pattern 'linkedin.com/company/<company>'"
            )

        # Get the company name
        company_name = match.group(2)

        if not company_name:
            raise ValueError("LinkedIn URL must include a company name after /company/")

        # Reconstruct the normalized URL with www and without trailing slash
        return f"https://www.linkedin.com/company/{company_name}"


class LinkedinSchoolProfileUrl(str):
    """
    A custom Pydantic data type for validating and normalizing LinkedIn school URLs.

    Features:
    - Converts HTTP to HTTPS
    - Adds www if missing
    - Removes country-specific subdomains (e.g., fr.linkedin.com -> www.linkedin.com)
    - Ensures URL has /in/ path
    - Removes trailing slashes
    """

    @classmethod
    def __get_pydantic_core_schema__(
        cls, source_type: Any, handler: GetCoreSchemaHandler
    ) -> core_schema.CoreSchema:
        return core_schema.with_info_before_validator_function(
            cls._validate_and_normalize_url,
            core_schema.str_schema(),
        )

    @classmethod
    def _validate_and_normalize_url(
        cls, url: str, _info: core_schema.ValidationInfo
    ) -> str:
        """
        Validate and normalize LinkedIn school URLs.

        The key pattern to match is 'linkedin.com/school/<school>'
        Automatically handles:
        - HTTP vs HTTPS (converts to HTTPS)
        - Missing www (adds it)
        - Country-specific subdomains (removes them)
        - Trailing slashes (removes them)
        """
        if not url:
            raise ValueError("LinkedIn URL cannot be empty")

        # Convert to string and strip whitespace
        url = str(url).strip()

        # Replace HTTP with HTTPS
        if url.startswith("http://"):
            url = "https://" + url[7:]
        elif not url.startswith("https://"):
            url = "https://" + url

        # Extract the core pattern: linkedin.com/school/<school>
        pattern = (
            r"https://(?:www\.|([a-z]{2})\.)?linkedin\.com/school/([a-zA-Z0-9\-_.]+)"
        )
        match = re.match(pattern, url)

        if not match:
            raise ValueError(
                f"Invalid LinkedIn school URL: {url}. Must match pattern 'linkedin.com/school/<school>'"
            )

        # Get the school name
        school_name = match.group(2)

        if not school_name:
            raise ValueError("LinkedIn URL must include a school name after /school/")

        # Reconstruct the normalized URL with www and without trailing slash
        return f"https://www.linkedin.com/school/{school_name}"


# # Example usage with a Pydantic model
# from pydantic import BaseModel


# class UserProfile(BaseModel):
#     linkedin: LinkedinProfileUrl
#     company: LinkedinCompanyUrl


# # Valid cases
# valid_urls = [
#     "https://www.linkedin.com/in/john_doe123",
#     "https://www.linkedin.com/in/emma-watson",
#     "https://www.linkedin.com/in/james.smith",
#     "https://linkedin.com/in/johndoe",  # Missing 'www',
#     "https://de.linkedin.com/in/johndoe",  # Missing 'www'
#     "http://www.linkedin.com/in/johndoe",  # Not HTTPS
# ]

# valid_company_urls = [
#     "https://www.linkedin.com/company/google",
#     "https://www.linkedin.com/company/facebook",
#     "https://linkedin.com/company/google",  # Missing 'www',
#     "https://de.linkedin.com/company/google",  # Missing 'www'
#     "http://www.linkedin.com/company/google",  # Not HTTPS
# ]

# # Invalid cases (will raise validation errors)
# invalid_urls = [
#     "https://www.linkedin.com/invalid/",  # Incorrect path
#     "https://www.linkedin.com/in/",  # Missing username
# ]

# invalid_company_urls = [
#     "https://www.linkedin.com/company/",  # Missing company name
#     "https://www.linkedin.com/in/johndoe123/",  # Invalid username
# ]
# for url, company_url in zip(valid_urls, valid_company_urls):
#     try:
#         profile = UserProfile(linkedin=url, company=company_url)
#         print(f"✅ Valid: {profile.linkedin}, {profile.company}")
#     except Exception as e:
#         print(f"❌ Unexpected error for {url}: {e}")

# print("\nTesting invalid URLs:")
# for url, company_url in zip(invalid_urls, invalid_company_urls):
#     try:
#         profile = UserProfile(linkedin=url, company=company_url)
#         print(f"❌ Did not reject invalid URL: {url}")
#     except Exception as e:
#         print(f"✅ Correctly rejected {url}: {e}")
