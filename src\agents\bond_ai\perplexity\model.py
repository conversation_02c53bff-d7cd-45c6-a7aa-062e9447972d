from bond_ai.utils import load_chat_model, load_chat_model_non_thinking




"""
This module provides a function to get a model based on the configuration.
"""
import os
from bond_ai.perplexity.state import AgentState
from langchain_core.language_models.chat_models import BaseChatModel

def get_model(state: AgentState) -> BaseChatModel:
    """
    Get a model based on the environment variable.
    """

    # state_model = state.get("model")
    # model = os.getenv("MODEL", state_model)




    from langchain_openai import ChatOpenAI # pylint: disable=import-outside-toplevel
    return ChatOpenAI(temperature=0, model="gpt-4o-mini")
    

    raise ValueError("Invalid model specified")

#get_model =  load_chat_model_non_thinking("bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0")