"""Perplexity search tool for SDR intelligence gathering."""

import os
import logging
from datetime import datetime
from typing import Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool, InjectedToolArg
from langchain_perplexity import ChatPerplexity
from langchain_core.prompts import Chat<PERSON>rompt<PERSON>emplate
from dotenv import load_dotenv

from langgraph.types import Command
from langchain_core.messages import ToolMessage
from langchain_core.tools import InjectedToolCallId

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


@tool
async def perplexity_search(
    tool_call_id: Annotated[str, InjectedToolCallId],
    config: Annotated[RunnableConfig, InjectedToolArg],
    search_query: str,
) -> Command:
    """Search for SDR specific web results about company and people event and signals.

    This tool performs intelligent web search using Perplexity AI to gather sales intelligence
    about companies, people, events, and market signals that are relevant for SDR activities.

    Args:
        search_query: The search query to execute for gathering SDR intelligence

    Returns:
        Command with search results formatted for SDR use cases
    """
    try:
        # Get Perplexity API key from environment
        pplx_api_key = os.getenv("PPLX_API_KEY")
        if not pplx_api_key:
            error_msg = "PPLX_API_KEY environment variable not found"
            logger.error(error_msg)
            return Command(update={
                "last_error_message": error_msg,
                "messages": [
                    ToolMessage(
                        content=f"Error: {error_msg}",
                        tool_call_id=tool_call_id,
                    )
                ]
            })

        # Initialize Perplexity chat model
        chat = ChatPerplexity(
            temperature=0,
            pplx_api_key=pplx_api_key,
            model="sonar"  # Using sonar model for web search
        )

        # Create enhanced system prompt for SDR intelligence gathering
        system_prompt = """You are an expert SDR (Sales Development Representative) intelligence researcher.
Your role is to gather actionable sales intelligence from web sources to help sales teams identify opportunities,
understand prospects, and craft personalized outreach.

Focus on finding:
- Company news, funding, expansions, leadership changes
- Industry trends and market signals
- People movements, promotions, job changes
- Technology adoptions and business initiatives
- Pain points and business challenges
- Recent achievements and milestones

Provide concise, actionable insights that can be used for sales outreach and prospecting."""

        # Create enhanced search instructions
        current_date = datetime.now().strftime("%Y-%m-%d")
        enhanced_instructions = f"""
Execute a targeted web search for sales intelligence research.

SEARCH QUERY: {search_query}
DATE: {current_date}

Please search for recent, credible information that would be valuable for sales development activities.
Focus on actionable insights that can help with prospecting, lead qualification, and personalized outreach.

Prioritize:
1. Recent news and developments (last 6 months)
2. Credible business sources
3. Specific, actionable information
4. Context that enables personalized sales conversations

Format your response with clear, concise insights that a sales professional can immediately use.
"""

        # Create prompt template and chain
        prompt = ChatPromptTemplate([
            ("system", system_prompt),
            ("user", "{input}")
        ])

        chain = prompt | chat

        # Execute the search
        response = await chain.ainvoke({"input": enhanced_instructions})

        search_result = response.content

        logger.info(f"Perplexity search completed for query: {search_query[:100]}...")

        return Command(update={
            "last_error_message": None,
            "messages": [
                ToolMessage(
                    content=search_result,
                    tool_call_id=tool_call_id,
                )
            ]
        })

    except Exception as e:
        error_msg = f"Error during Perplexity search: {str(e)}"
        logger.error(error_msg, exc_info=True)

        return Command(update={
            "last_error_message": error_msg,
            "messages": [
                ToolMessage(
                    content=f"Search failed: {error_msg}",
                    tool_call_id=tool_call_id,
                )
            ]
        })
