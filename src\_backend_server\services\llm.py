from src.db.cells import (
    update_cell,
    reimburse_tokens,
    upsert_cell_details,
)
from src.backend_server.models.requests import AIColumnRequest, ResearchAgentRequest
from src.backend_server.models.llm import RunOnlyIfResponse
from src.agent.utils import load_chat_model
from src.agent.prompts import RUN_ONLY_IF_PROMPT, FORMULA_PROMPT
from langchain_core.messages import AIMessage, HumanMessage, ToolMessage, SystemMessage
from typing import Dict, Any
import logging
from supabase import Client
from src.agent.graph import graph

# Import utility functions
from src.backend_server.services.llm_utils import (
    extract_domain,
    extract_tool_arg,
    update_cell_status,
)

# Import centralized configuration
from src.core.config import get_settings
from src.schemas.requests import ServiceRequest

# Get application settings
settings = get_settings()

# Access settings as attributes
RUN_ONLY_IF_MODEL = settings.RUN_ONLY_IF_MODEL
AI_COLUMN_MODEL = settings.AI_COLUMN_MODEL
AI_FORMULA_MODEL = settings.AI_FORMULA_MODEL

logger = logging.getLogger("backend_api.services.llm")


async def run_only_run_if(formula: str) -> Dict[str, Any]:
    """
    Evaluates a formula using an LLM and returns a serializable dictionary.

    Args:
        formula: The formula to evaluate

    Returns:
        Dict[str, Any]: A dictionary containing 'is_valid' boolean and 'status' string fields
    """
    llm = load_chat_model(RUN_ONLY_IF_MODEL).with_structured_output(RunOnlyIfResponse)
    prompt = RUN_ONLY_IF_PROMPT.format(formula=formula)
    response = llm.invoke(prompt)
    return response.json()


async def _handle_formula_condition(
    request: AIColumnRequest,
    cell: Dict[str, Any],
    supabase_client: Client,
) -> bool:
    """
    Handle formula condition evaluation.

    Args:
        request: The AI column request
        cell: The cell data
        supabase_client: Supabase client

    Returns:
        bool: True if processing should continue, False otherwise
    """
    if not request.formula:
        return True

    try:
        await update_cell_status(cell, "validating", "Validating condition...")
        result = await run_only_run_if(request.formula)

        if not result["is_valid"]:
            await update_cell_status(cell, "condition_not_met", "Condition not met!")

            # Update cell and reimburse tokens
            await update_cell(
                supabase_client,
                request.column_id,
                request.row_id,
                request.table_id,
                None,
                {"run": "condition_not_met", "status": "Condition not met!"},
            )

            # Reimburse tokens if credits are available
            if request.credits > 0:
                await reimburse_tokens(
                    organization_id=request.organization_id,
                    tokens=request.credits,
                    supabase=supabase_client,
                )
            return False

    except Exception as e:
        logger.error(f"Error evaluating formula condition: {str(e)}")
        error_msg = f"Formula evaluation error: {str(e)}"
        await update_cell_status(cell, "failed", error_msg)

        await update_cell(
            supabase_client,
            request.column_id,
            request.row_id,
            request.table_id,
            None,
            {"run": "failed", "status": error_msg},
        )

        if request.credits > 0:
            await reimburse_tokens(
                organization_id=request.organization_id,
                tokens=request.credits,
                supabase=supabase_client,
            )
        return False

    return True


async def _handle_token_reimbursement(
    request: AIColumnRequest, supabase_client: Client
) -> None:
    """
    Reimburse tokens if credits are available.

    Args:
        request: The AI column request
        supabase_client: Supabase client
    """
    if request.credits > 0:
        await reimburse_tokens(
            organization_id=request.organization_id,
            tokens=request.credits,
            supabase=supabase_client,
        )


async def run_ai_column(
    request: AIColumnRequest,
    supabase_client: Client,
) -> None:
    """
    Process an AI column request, evaluating any formula conditions and generating content.

    This function handles the entire lifecycle of an AI column request:
    1. Evaluates any conditional formula to determine if processing should continue
    2. If condition fails, updates cell status and reimburses tokens
    3. If condition passes, generates AI content and updates the cell
    4. Stores detailed results in the cell_details table

    Args:
        request: The AIColumnRequest containing all parameters for the AI operation
        supabase_client: An authenticated Supabase client for database operations

    Returns:
        None: This function operates asynchronously and doesn't return a value
    """
    try:
        # Get cell data
        cell = (
            supabase_client.table("cells")
            .select("*")
            .eq("table_id", request.table_id)
            .eq("row_id", request.row_id)
            .eq("column_id", request.column_id)
            .execute()
            .data[0]
        )

        # Initialize processing status
        await update_cell_status(cell, "processing", "Processing...")

        # Check formula condition
        if not await _handle_formula_condition(request, cell, supabase_client):
            return

        # Generate AI content
        try:
            await update_cell_status(cell, "generating", "Generating content...")

            llm = load_chat_model(AI_COLUMN_MODEL)
            messages = [
                SystemMessage(content=request.system_prompt),
                HumanMessage(content=request.user_prompt),
            ]
            response = llm.invoke(messages)

            # Update cell with result
            await update_cell_status(
                cell, "completed", value=response.content, completed=True
            )

            await update_cell(
                supabase_client,
                request.column_id,
                request.row_id,
                request.table_id,
                response.content,
                {"run": "completed", "status": None},
            )

            # Store detailed results
            await upsert_cell_details(
                supabase_client,
                request.table_id,
                request.column_id,
                request.row_id,
                [{"Response": response.content}],
            )

        except Exception as e:
            logger.error(f"Error generating or storing AI content: {str(e)}")
            error_msg = f"AI processing error: {str(e)}"
            await update_cell_status(cell, "failed", error_msg)

            await update_cell(
                supabase_client,
                request.column_id,
                request.row_id,
                request.table_id,
                None,
                {"run": "failed", "status": error_msg},
            )

            await _handle_token_reimbursement(request, supabase_client)

    except Exception as e:
        # Catch-all for any unexpected errors
        logger.error(f"Unexpected error in run_ai_column: {str(e)}")

    return


async def run_ai_formula(
    request: ServiceRequest,
    supabase_client: Client,
) -> None:
    """
    Process an AI formula request, evaluating any formula conditions and generating formula content.

    This function handles the entire lifecycle of an AI formula request:
    1. Evaluates any conditional formula to determine if processing should continue
    2. If condition fails, updates cell status and reimburses tokens
    3. If condition passes, generates AI formula content and updates the cell
    4. Stores detailed results in the cell_details table

    Args:
        request: The AIColumnRequest containing all parameters for the AI operation
        supabase_client: An authenticated Supabase client for database operations

    Returns:
        None: This function operates asynchronously and doesn't return a value
    """
    try:
        # Get cell data
        cell = (
            supabase_client.table("cells")
            .select("*")
            .eq("table_id", request.table_id)
            .eq("row_id", request.row_id)
            .eq("column_id", request.column_id)
            .execute()
            .data[0]
        )

        # Initialize processing status
        await update_cell_status(cell, "processing", "Processing...")

        # Check formula condition
        if not await _handle_formula_condition(request, cell, supabase_client):
            return

        # Generate AI formula content
        try:
            await update_cell_status(cell, "generating", "Generating content...")

            llm = load_chat_model(AI_FORMULA_MODEL)
            messages = [
                SystemMessage(content=FORMULA_PROMPT),
                HumanMessage(content=request.user_prompt),
            ]
            response = llm.invoke(messages)

            # Update cell with result
            await update_cell_status(
                cell, "completed", value=response.content, completed=True
            )

            await update_cell(
                supabase_client,
                request.column_id,
                request.row_id,
                request.table_id,
                response.content,
                {"run": "completed", "status": None},
            )

            # Store detailed results
            await upsert_cell_details(
                supabase_client,
                request.table_id,
                request.column_id,
                request.row_id,
                [{"Response": response.content}],
            )

        except Exception as e:
            logger.error(f"Error generating or storing AI formula: {str(e)}")
            error_msg = f"AI formula generation error: {str(e)}"
            await update_cell_status(cell, "failed", error_msg)

            await update_cell(
                supabase_client,
                request.column_id,
                request.row_id,
                request.table_id,
                None,
                {"run": "failed", "status": error_msg},
            )

            await _handle_token_reimbursement(request, supabase_client)

    except Exception as e:
        # Catch-all for any unexpected errors
        logger.error(f"Unexpected error in run_ai_formula: {str(e)}")

    return


async def run_research_agent(
    request: ServiceRequest,
    supabase_client: Client,
) -> None:
    """
    Process a research agent request, evaluating any formula conditions and running the research agent.

    This function handles the entire lifecycle of a research agent request:
    1. Evaluates any conditional formula to determine if processing should continue
    2. If condition fails, updates cell status and reimburses tokens
    3. If condition passes, runs the research agent and updates the cell with results
    4. Stores detailed results in the cell_details table

    Args:
        request: The ResearchAgentRequest containing all parameters for the research operation
        supabase_client: An authenticated Supabase client for database operations

    Returns:
        None: This function operates asynchronously and doesn't return a value
    """
    try:
        # Get cell data
        cell = (
            supabase_client.table("cells")
            .select("*")
            .eq("table_id", request.table_id)
            .eq("row_id", request.row_id)
            .eq("column_id", request.column_id)
            .execute()
            .data[0]
        )

        # Initialize processing status
        await update_cell_status(cell, "processing", "Processing...")

        # Check formula condition
        if not await _handle_formula_condition(request, cell, supabase_client):
            return

        # Run research agent
        try:
            await update_cell_status(cell, "generating", "Starting research...")

            inputs = {"messages": [HumanMessage(content=request.user_prompt)]}
            stream = graph.stream(inputs, stream_mode="values")
            final_output = None

            for s in stream:
                message = s["messages"][-1]

                # Handle tool messages (search, website reading, etc.)
                if isinstance(message, ToolMessage):
                    tool_name = message.name

                    # Find the corresponding tool call in previous messages
                    for prev_msg in s["messages"]:
                        if hasattr(prev_msg, "tool_calls"):
                            for tool_call in prev_msg.tool_calls:
                                if tool_call["name"] == tool_name:
                                    if tool_name == "search":
                                        query = extract_tool_arg(
                                            tool_call, ["__arg1", "query"]
                                        )
                                        await update_cell_status(
                                            cell,
                                            "processing",
                                            f"🔍 Searching for: '{query}'",
                                        )

                                    elif tool_name == "scrape_website":
                                        url = extract_tool_arg(
                                            tool_call, ["__arg1", "url"]
                                        )
                                        domain = extract_domain(url)
                                        await update_cell_status(
                                            cell,
                                            "processing",
                                            f"📄 Reading website: {domain}",
                                        )

                                    elif tool_name == "crawl_website":
                                        url = extract_tool_arg(
                                            tool_call, ["__arg1", "url"]
                                        )
                                        domain = extract_domain(url)
                                        await update_cell_status(
                                            cell,
                                            "processing",
                                            f"🔬 Browsing website: {domain}",
                                        )

                                    break

                # Handle AI messages (thinking or final answer)
                elif isinstance(message, AIMessage):
                    if not hasattr(message, "tool_calls") or not message.tool_calls:
                        final_output = message.content
                    else:
                        await update_cell_status(cell, "processing", "🤔 Thinking...")

            # Update with final output
            if final_output:
                await update_cell_status(
                    cell, "completed", value=final_output, completed=True
                )

                # Update cell with result
                await update_cell(
                    supabase_client,
                    request.column_id,
                    request.row_id,
                    request.table_id,
                    final_output,
                    {"run": "completed", "status": None},
                )

                # Store detailed results
                await upsert_cell_details(
                    supabase_client,
                    request.table_id,
                    request.column_id,
                    request.row_id,
                    [{"Response": final_output}],
                )

        except Exception as e:
            logger.error(f"Error generating or storing AI research: {str(e)}")
            error_msg = f"AI research generation error: {str(e)}"
            await update_cell_status(cell, "failed", error_msg)

            await update_cell(
                supabase_client,
                request.column_id,
                request.row_id,
                request.table_id,
                None,
                {"run": "failed", "status": error_msg},
            )

            await _handle_token_reimbursement(request, supabase_client)

    except Exception as e:
        # Catch-all for any unexpected errors
        logger.error(f"Unexpected error in run_research_agent: {str(e)}")

    return
