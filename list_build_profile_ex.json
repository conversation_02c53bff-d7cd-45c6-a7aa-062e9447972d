{"results": [{"id": "<string>", "connections": [{"userId": "<string>", "userEmail": "<string>", "userLinkedinName": "<string>", "createdAt": "2023-11-07T05:31:56Z", "sources": [{"origin": "<string>", "network": "<string>"}], "connectionStrength": 123, "manualStrength": 123, "strengthNormalized": 123}], "lists": [{"id": "<string>", "name": "<string>", "status": "<string>"}], "notes": [{"id": "<string>", "content": "<string>"}], "tags": [{"id": "<string>", "name": "<string>"}], "profileInfo": {"id": "<string>", "fullName": "<string>", "firstName": "<string>", "lastName": "<string>", "linkedinUrl": "<string>", "workEmail": "<string>", "personalEmails": ["<string>"], "industry": "<string>", "jobTitle": "<string>", "jobTitleRole": "<string>", "jobSeniority": ["<string>"], "jobCompanyId": "<string>", "jobCompanyName": "<string>", "jobCompanyLogoUrl": "<string>", "jobCompanyWebsite": "<string>", "jobCompanySize": {"min": 123, "max": 123}, "jobCompanyIndustry": "<string>", "jobCompanyLocationName": "<string>", "jobCompanyLinkedinUrl": "<string>", "experience": [{"isPrimary": true, "companyId": "<string>", "company": {"id": "<string>", "canonicalName": "<string>", "name": "<string>", "founded": "2023-11-07T05:31:56Z", "industry": "<string>", "website": "<string>", "size": {"min": 123, "max": 123}, "networks": [{"network": "<string>", "id": "<string>", "url": "<string>"}], "locations": [{"location": {"name": "<string>", "city": "<string>", "state": "<string>", "country": "<string>", "streetAddress": "<string>", "postalCode": "<string>"}, "headquarter": true}], "logo": "<string>"}, "title": "<string>", "startDate": "2023-12-25", "endDate": "2023-12-25"}], "latestRoleChangeAt": "2023-12-25", "latestCompanyChangeAt": "2023-12-25", "locationName": "<string>", "locationNames": ["<string>"], "interests": ["<string>"], "skills": ["<string>"], "education": [{"school": {"name": "<string>", "type": "<string>", "id": "<string>", "location": {"name": "<string>", "city": "<string>", "state": "<string>", "country": "<string>", "streetAddress": "<string>", "postalCode": "<string>"}, "website": "<string>"}, "startDate": "2023-12-25", "endDate": "2023-12-25", "degrees": ["<string>"], "majors": ["<string>"], "minors": ["<string>"]}], "profiles": [{"network": "<string>", "id": "<string>", "url": "<string>"}], "updatedAt": "2023-11-07T05:31:56Z", "currentJobUpdatedAt": "2023-11-07T05:31:56Z", "createdAt": "2023-11-07T05:31:56Z"}}], "notFound": ["3c90c3cc-0d44-4b50-8888-8dd25736052a"]}