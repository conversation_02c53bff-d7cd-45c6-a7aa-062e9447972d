import asyncio
import httpx
import json
import os
from dotenv import load_dotenv
import time
from typing import List, Dict, Any, Optional
from supabase import create_client, Client

# Load environment variables at the start
load_dotenv()

async def test_get_table_data_by_row_ids(
    table_id: str,
    row_ids: List[int]
):
    """
    Test the table data retrieval endpoint with caching.
    
    Args:
        table_id: The ID of the table to query
        row_ids: List of row IDs to retrieve
    """
    # API endpoint URL
    base_url = os.getenv("API_URL", "http://localhost:2024")
    url = f"{base_url}/table/{table_id}/rows"
    
    print(f"Testing endpoint: {url}")
    
    # Prepare request payload
    payload = {
        "row_ids": row_ids
    }
    
    print("\n📝 Request Payload:")
    print(json.dumps(payload, indent=2))
    
    # Create client with increased timeout
    timeout = httpx.Timeout(30.0)  # 30 seconds timeout
    async with httpx.AsyncClient(timeout=timeout) as client:
        print("\n🔍 Sending request...")
        start_time = time.time()
        
        response = await client.post(url, json=payload)
        
        end_time = time.time()
        latency_s = end_time - start_time  # Seconds
        latency_ms = latency_s * 1000      # Milliseconds
        
        print(f"\n⏱️ Latency: {latency_s:.3f} s ({latency_ms:.2f} ms)")
        
        if response.status_code == 200:
            result = response.json()
            
            # Calculate and print response size
            response_text = response.text
            response_size_bytes = len(response_text)
            response_size_kb = response_size_bytes / 1024
            response_size_mb = response_size_kb / 1024
            
            print(f"\n📊 Response Size:")
            print(f"  Bytes: {response_size_bytes:,}")
            print(f"  KB: {response_size_kb:.2f}")

            # Save response to file
            with open("tests/development_tests/response.json", "w") as f:
                json.dump(result, f, indent=2)
            print("\n💾 Response saved to response.json")
            if response_size_mb >= 1:
                print(f"  MB: {response_size_mb:.2f}")
            
            # Print row count
            row_count = len(result.get('data', []))
            print(f"\n📋 Total rows: {row_count:,}")
            
            # Print average bytes per row if we have rows
            if row_count > 0:
                bytes_per_row = response_size_bytes / row_count
                print(f"  Avg bytes per row: {bytes_per_row:.2f}")
            
            # Print cell count per row
            if row_count > 0:
                cells_per_row = len(result['data'][0]['cells'])
                print(f"  Cells per row: {cells_per_row}")
            
            # Print cache status if available
            if "from_cache" in result:
                cache_status = "HIT ✅" if result["from_cache"] else "MISS ❌"
                print(f"\n🔄 Cache: {cache_status}")
            
            # Uncomment to print full response
            # print("\n✅ Success Response:")
            # print(json.dumps(result, indent=2))
        else:
            print(f"\n❌ Error {response.status_code}:")
            print(response.text)

async def test_supabase_rpc(
    table_id: str,
    row_ids: List[int]
):
    """Test the same functionality using Supabase RPC."""
    print("\n🚀 Testing Supabase RPC")
    
    # Initialize Supabase client with better error handling
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    # Debug: Print environment variable status
    print("\n🔍 Environment Variables Check:")
    print(f"  SUPABASE_URL: {'✅ Set' if supabase_url else '❌ Missing'}")
    print(f"  SUPABASE_KEY: {'✅ Set' if supabase_key else '❌ Missing'}")
    
    if not supabase_url or not supabase_key:
        print("\n❌ Supabase Error: Missing environment variables")
        print("Current values:")
        print(f"  SUPABASE_URL={supabase_url}")
        print(f"  SUPABASE_KEY={supabase_key}")
        print("\nPlease check your .env file contains:")
        print("SUPABASE_URL='your-project.supabase.co'")
        print("SUPABASE_KEY='your-anon-key'")
        return
    
    try:
        print("\n🔌 Connecting to Supabase...")
        supabase: Client = create_client(supabase_url, supabase_key)
        
        # Prepare payload
        payload = {
            "p_row_ids": row_ids,
            "p_table_id": table_id
        }
        
        print("\n📝 Supabase Payload:")
        print(json.dumps(payload, indent=2))
        
        # Make the RPC call
        print("\n🔍 Sending Supabase request...")
        start_time = time.time()
        
        response = supabase.rpc(
            'get_table_data_by_row_ids',
            payload
        ).execute()
        
        end_time = time.time()
        latency_s = end_time - start_time
        latency_ms = latency_s * 1000
        
        print(f"\n⏱️ Supabase Latency: {latency_s:.3f}s ({latency_ms:.2f}ms)")
        
        if hasattr(response, 'data'):
            print_response_stats("Supabase", response, response.data)
        else:
            print("\n❌ Supabase Error:")
            print(response)
            
    except Exception as e:
        print(f"\n❌ Supabase Error: {str(e)}")
        print(f"Error type: {type(e)}")

def print_response_stats(name: str, response, result: Dict):
    """Helper function to print response statistics."""
    # Calculate response size
    response_text = json.dumps(result)
    response_size_bytes = len(response_text)
    response_size_kb = response_size_bytes / 1024
    response_size_mb = response_size_kb / 1024
    
    print(f"\n📊 {name} Response Size:")
    print(f"  Bytes: {response_size_bytes:,}")
    print(f"  KB: {response_size_kb:.2f}")
    if response_size_mb >= 1:
        print(f"  MB: {response_size_mb:.2f}")
    
    # Print row count
    row_count = len(result.get('data', []))
    print(f"\n📋 {name} Total rows: {row_count:,}")
    
    # Print average bytes per row if we have rows
    if row_count > 0:
        bytes_per_row = response_size_bytes / row_count
        print(f"  Avg bytes per row: {bytes_per_row:.2f}")
    
    # Print cell count per row
    if row_count > 0:
        cells_per_row = len(result['data'][0]['cells'])
        print(f"  Cells per row: {cells_per_row}")
    
    # Print cache status if available
    if "from_cache" in result:
        cache_status = "HIT ✅" if result["from_cache"] else "MISS ❌"
        print(f"\n🔄 Cache: {cache_status}")

async def run_comparison(table_id: str, row_ids: List[int]):
    """Run both tests for comparison."""
    print("\n=== Starting Performance Comparison ===\n")
    
    print("Running FastAPI Test...")
    await test_get_table_data_by_row_ids(table_id, row_ids)
    
    print("\nRunning Supabase Test...")
    await test_supabase_rpc(table_id, row_ids)
    
    print("\n=== Comparison Complete ===")

if __name__ == "__main__":
    # Example usage
    TABLE_ID = "tbl_3a5bdcaff1591af3"
    ROW_IDS = [1,2,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202]
    
    # Run both tests for comparison
    asyncio.run(run_comparison(TABLE_ID, ROW_IDS))
