


from src.outbond_ai_assistant_v2.supabase.notify_fe.notify_fe import notify_fe
from src.outbond_ai_assistant_v2.supabase.notify_fe.models import NotifyFERequest, NotifyFEType
from .models import TableBaseModel, TableResponse, UpdateTableRequest
from ..client import supabase
from typing import Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg
from ...configuration import Configuration


def update_table(
    config: Annotated[RunnableConfig, InjectedToolArg],
    request: UpdateTableRequest,
    is_notify_fe: bool = False
) -> TableResponse:
    """Create a normal "non-smart" column in the database."""
    try:
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        response = supabase.table('tables').update(request.model_dump(exclude_unset=True)).eq('id', table_id).execute()
        
        
        # Success case: RPC returns data with column
        if hasattr(response, 'data') and response.data and 'organization_id' in response.data:
            if is_notify_fe:
                notify_fe(config, NotifyFERequest(channel=table_id, type=NotifyFEType.TABLE_UPDATE, payload=response.data[0]))
            return TableResponse(
                table=TableBaseModel(**response.data[0]),
                success=True
            )
        
        # Unexpected response format
        return TableResponse(
            error_message=f"Unexpected response from update table '{table_id}'",
            success=False
        )
            
    except Exception as error:
        # Error case
        return TableResponse(
            error_message=f"Failed to update table '{table_id}': {str(error)}",
            success=False
        )

