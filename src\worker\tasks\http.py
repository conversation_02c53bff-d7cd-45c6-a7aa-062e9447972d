"""HTTP service Celery tasks following Lima patterns."""

import asyncio
import logging

from supabase import AsyncClient

import src.services.http.http as http_service
from src.db.utils import (
    CellDetails,
    CellId,
    CellStatus,
    RealtimeEvent,
    get_cell,
    get_supabase_client,
    reimburse_tokens,
    send_realtime_broadcast,
    update_cell,
    upsert_cell_details,
)
from src.schemas.requests import (
    ServiceRequest,
)
from src.services.http.http import HTTPRateLimitException
from src.services.http.models import HTTPMethod, HTTPRequest
from src.services.llm.generation import _handle_formula_condition

from ..celery_app import app

logger = logging.getLogger("src.worker.tasks.http")


@app.task(
    name="src.worker.tasks.http.run_http_task",
    ignore_result=True,
    max_retries=1,
    queue="enrichment",
    rate_limit="5/s",
    bind=True,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_kwargs={"max_retries": 3},
)
def run_http_task(self, input_dict):
    """
    Celery task that runs the async function using asyncio.

    Args:
        self: The task instance (provided by bind=True)
        input_dict: Dictionary representation of the ServiceRequest
    """
    # Convert dict back to ServiceRequest
    input = ServiceRequest(**input_dict)

    # Run the async function in a new event loop
    try:
        asyncio.run(_run_http_async(self, input))
    except Exception as e:
        logger.error(f"Error in run_http_task: {str(e)}")
        raise


async def _run_http_async(self, input: ServiceRequest):
    """
    The actual async implementation that gets run by the Celery task
    """
    supabase_client: AsyncClient = await get_supabase_client()

    try:
        cell = await get_cell(
            supabase_client,
            CellId(
                column_id=input.column_id,
                row_id=input.row_id,
                table_id=input.table_id,
            ),
        )
        # Check formula condition
        if not await _handle_formula_condition(input, cell, supabase_client):
            return

        cell.run_status = CellStatus(
            run="processing", message="🔄 Processing HTTP request..."
        )
        await send_realtime_broadcast(
            supabase_client,
            input.table_id,
            RealtimeEvent.CELL_UPDATE,
            cell.model_dump(),
        )
        await update_cell(supabase_client, cell)

        cell_details = CellDetails(
            table_id=input.table_id,
            column_id=input.column_id,
            row_id=input.row_id,
        )

        # Handle HTTP request based on input value structure
        cell.run_status = CellStatus(
            run="processing", message="🌐 Making HTTP request..."
        )
        await send_realtime_broadcast(
            supabase_client,
            input.table_id,
            RealtimeEvent.CELL_UPDATE,
            cell.model_dump(),
        )
        await update_cell(supabase_client, cell)

        # # Handle HTTP request from dict input
        # if not isinstance(input.value, dict):
        #     raise ValueError(
        #         f"Invalid request type: {type(input.value)}. Expected dict."
        #     )

        # Full HTTP request specification
        url = input.value.url
        method = input.value.method
        params = input.value.params
        body = input.value.body
        headers = input.value.headers or {}
        
        # Append authorization key-value pairs to headers if defined
        if input.value.authorization:
            headers.update(input.value.authorization)

        if not url:
            raise ValueError("URL is required in HTTP request")

        # Use appropriate method-specific function or generic request
        if method == "GET":
            response = await http_service.get_request(
                url, headers=headers, params=params
            )
        elif method == "POST":
            response = await http_service.post_request(url, body=body, headers=headers)
        elif method == "PUT":
            response = await http_service.put_request(url, body=body, headers=headers)
        elif method == "DELETE":
            response = await http_service.delete_request(url, headers=headers)
        else:
            # Use generic request for other methods
            request = HTTPRequest(
                url=url,
                method=HTTPMethod(method),
                params=params,
                body=body,
                headers=headers,
            )
            response = await http_service.make_http_request(request)

        # Store response in cell details
        cell_details.value = {
            "status_code": response.status_code,
            "headers": response.headers,
            "body": response.json,
        }

        # Handle status code and set appropriate cell status
        if 200 <= response.status_code < 300:
            cell.value = "✅ Success"
            cell.run_status = CellStatus(run="completed", message=None)
        elif 300 <= response.status_code < 400:
            cell.value = "🔄 Redirect"
            cell.run_status = CellStatus(run="completed", message=None)
        elif response.status_code == 429:
            # Rate limit - set up for retry
            retry_after = response.headers.get("retry-after")
            if retry_after:
                try:
                    retry_delay = int(retry_after)
                except ValueError:
                    retry_delay = 300
            else:
                retry_delay = 300

            # Store the retry info for the outer function to handle
            raise HTTPRateLimitException(
                "Rate limit exceeded", retry_after=str(retry_delay)
            )

        elif 400 <= response.status_code < 500:
            # Other client errors - treat as failed
            cell.value = None
            cell.run_status = CellStatus(
                run="failed", message="❌ Bad request or authentication issue"
            )
        elif 500 <= response.status_code < 600:
            # Server errors - treat as failed
            cell.value = None
            cell.run_status = CellStatus(run="failed", message="🚨 Remote server issue")
        else:
            # Unknown status codes - treat as failed
            cell.value = None
            cell.run_status = CellStatus(run="failed", message="❓ Unexpected response")
        await send_realtime_broadcast(
            supabase_client,
            input.table_id,
            RealtimeEvent.CELL_UPDATE,
            cell.model_dump(),
        )
        await upsert_cell_details(supabase_client, cell_details)
        await update_cell(supabase_client, cell)

    except HTTPRateLimitException as e:
        # Check if we've exceeded max retries
        if self.request.retries >= 3:
            cell.run_status = CellStatus(
                run="failed",
                message="🚫 Rate limit exceeded - maximum retries reached. Please try again later.",
            )
            cell.value = None
            cell.extras = None
            await send_realtime_broadcast(
                supabase_client,
                input.table_id,
                RealtimeEvent.CELL_UPDATE,
                cell.model_dump(),
            )
            await update_cell(supabase_client, cell)
            return
        
        cell.run_status = CellStatus(
            run="failed",
            message="⏳ Rate limit exceeded, retrying later...",
        )
        cell.value = None
        cell.extras = None
        await send_realtime_broadcast(
            supabase_client,
            input.table_id,
            RealtimeEvent.CELL_UPDATE,
            cell.model_dump(),
        )
        await update_cell(supabase_client, cell)

        # Use self.retry with the countdown from the exception
        retry_delay = int(e.retry_after) if e.retry_after else 60
        raise self.retry(countdown=retry_delay)

    except Exception as e:
        logger.error(f"Error processing HTTP request: {str(e)}")
        cell.run_status = CellStatus(
            run="failed",
            message="Failed to process HTTP request, please try again later.",
        )
        cell.value = None
        cell.extras = None
        await send_realtime_broadcast(
            supabase_client,
            input.table_id,
            RealtimeEvent.CELL_UPDATE,
            cell.model_dump(),
        )
        await update_cell(supabase_client, cell)
        await reimburse_tokens(supabase_client, input.organization_id, input.credits)
        raise
