"""LinkedIn profile column creation and editing functionality."""

from typing import Union
from src.outbond_ai_assistant_v2.supabase.validate_injection_sequance.validate_injection_sequance import validate_injection_sequence
from src.outbond_ai_assistant_v2.supabase.validate_injection_sequance.models import ValidateInjectionSequenceRequest, ValidateInjectionSequenceResponse, TargetDataType
from src.outbond_ai_assistant_v2.supabase.upsert_smart_column.upsert_smart_column import upsert_smart_column
from src.outbond_ai_assistant_v2.supabase.upsert_smart_column.model import UpsertSmartColumnRequest, SmartColumnSettings, UpsertSmartColumnResponse
from src.outbond_ai_assistant_v2.supabase.table.get_columns import get_columns_by_names
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool
from typing import Annotated
from .models import (
    CreateAndEditLinkedInProfileColumnRequest,
    LinkedInProfileColumnResponse,
)
from src.outbond_ai_assistant_v2.configuration import Configuration


@tool
def create_and_edit_linkedin_profile_column(
    config: Annotated[RunnableConfig, InjectedToolArg],
    request: CreateAndEditLinkedInProfileColumnRequest
) -> Union[LinkedInProfileColumnResponse, ValidateInjectionSequenceResponse, UpsertSmartColumnResponse]:
    """Create or edit a LinkedIn profile column with injection sequence validation.
    
    This function provides a complete workflow for LinkedIn profile column management:
    1. Validates the injection sequence against the table
    2. For COLUMN_UPDATE actions, fetches the existing column ID by name
    3. Creates or updates the LinkedIn profile column if validation passes
    4. Returns detailed success or error information
    
    Args:
        request (CreateAndEditLinkedInProfileColumnRequest): The validated request model containing
                                                           all necessary parameters for column creation/editing
        
    Returns:
        Union[LinkedInProfileColumnResponse, LinkedInProfileColumnError]: Response model containing either
        success data (LinkedInProfileColumnResponse) or error information (LinkedInProfileColumnError)
    """
    try:
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id

        # Step 1: Validate injection sequence
        validation_request = ValidateInjectionSequenceRequest(
            injection_sequence=request.linkedin_profile_url_injection_path,
            target_data_type=TargetDataType.LINKEDIN_PROFILE_URL
        )
        
        validation_result = validate_injection_sequence(config=config, request=validation_request)
        
        # If validation failed, return the validation response directly
        if not validation_result.success:
            return validation_result
        
        # Step 2: Get column ID if this is an update operation
        column_id = None
        if request.action == "COLUMN_UPDATE":
            column_ids_data, column_ids_error = get_columns_by_names(config=config, column_names=[request.column_name])
            
            if column_ids_error:
                return LinkedInProfileColumnResponse(
                    error_message=f"Failed to fetch column ID for update: {column_ids_error}",
                    success=False
                )
            
            if not column_ids_data or len(column_ids_data) == 0:
                return LinkedInProfileColumnResponse(
                    error_message=f"Column '{request.column_name}' not found for update",
                    success=False
                )
            
            column_id = column_ids_data[0]["column_id"]

        # Step 3: Create LinkedIn profile column settings
        settings = SmartColumnSettings(
            inputs=[{"linkedin_profile_url": request.linkedin_profile_url_injection_path}],
            parameters=[{}],
            providers=[{"providers": ["outbond"]}]
        )
        
        # Step 4: Create upsert request
        upsert_request = UpsertSmartColumnRequest(
            table_id=table_id,
            column_name=request.column_name,
            service_id=7,  # LinkedIn Person Profile service ID
            settings=settings,
            column_id=column_id  # This will be None for creates, and the actual ID for updates
        )
        
        # Step 5: Execute column creation/update
        upsert_result = upsert_smart_column(config=config, request=upsert_request)
        
        # If upsert failed, return the upsert response directly
        if not upsert_result.success:
            return upsert_result
        
        return LinkedInProfileColumnResponse(
            column=upsert_result.column,
            action=request.action,
            success=True
        )
        
    except Exception as e:
        error_msg = f"Unexpected error in LinkedIn profile column operation: {str(e)}"
        return LinkedInProfileColumnResponse(
            error_message=error_msg,
            success=False
        )