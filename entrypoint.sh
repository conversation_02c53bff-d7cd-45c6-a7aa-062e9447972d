#!/bin/bash
set -e

# We're skipping all connection checks and directly starting the service
echo "Starting requested service without connection checks..."

# Set timezone to UTC for consistent time across workers
export TZ=UTC

case "$1" in
  # Run Celery worker for default queue
  worker-default)
    echo "Starting Celery worker for default queue..."
    exec celery -A src.worker.celery_app worker \
      -n dev-worker-default@%h \
      -Q default \
      -l info \
      --pool=threads \
      --autoscale=50,20
    ;;

  # Run Celery worker for enrichment queue
  worker-enrichment)
    echo "Starting Celery worker for enrichment queue..."
    exec celery -A src.worker.celery_app worker \
      -n dev-worker-enrichment@%h \
      -Q enrichment \
      -l info \
      --autoscale=50,20 \
      --purge
    ;;

  # Run Celery worker for LLM queue
  worker-llm)
    echo "Starting Celery worker for llm queue..."
    exec celery -A src.worker.celery_app worker \
      -n dev-worker-llm@%h \
      -Q llm \
      -l info \
      --pool=threads \
      --autoscale=80,50
    ;;

  # Run Celery worker with multiple queues (optional)
  worker-all)
    echo "Starting Celery worker for all queues..."
    exec celery -A src.worker.celery_app worker \
      -n dev-worker-all@%h \
      -Q default,enrichment,llm \
      -l info \
      --pool=threads \
      --autoscale=300,50
    ;;

  # Run Celery beat for scheduled tasks
  beat)
    echo "Starting Celery beat scheduler..."
    exec celery -A src.worker.celery_app beat \
      --loglevel=info 
    ;;

  flower)
    echo "Starting Flower for Celery monitoring..."
    # Add a delay to ensure workers are fully initialized
    sleep 10
    exec celery -A src.worker.celery_app \
      --broker=${CELERY_BROKER_URL} \
      --result-backend=${CELERY_RESULT_BACKEND} \
      flower \
      --loglevel=info \
      --port=5555 \
      --address=0.0.0.0 \
      --basic-auth=Izc39mhLZdKRyG99:UrZOdnGN8bvfO5m2
    ;;

  # Default case - just execute the passed command
  *)
    exec "$@"
    ;;
esac
