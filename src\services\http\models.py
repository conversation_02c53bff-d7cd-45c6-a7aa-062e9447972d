"""HTTP service models."""

from enum import Enum
from typing import Any, Dict, Optional, Union

from pydantic import BaseModel, HttpUrl


class HTTPMethod(str, Enum):
    """HTTP method enumeration."""

    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"


class HTTPRequest(BaseModel):
    """HTTP request model with method, params, body, and headers."""

    url: str
    method: HTTPMethod = HTTPMethod.GET
    params: Optional[Dict[str, Any]] = None
    body: Optional[Dict[str, Any]] = None
    headers: Optional[Dict[str, str]] = None
    authorization: Optional[Dict[str, str]] = None


class HTTPResponse(BaseModel):
    """HTTP response model with status code, headers, and body."""

    status_code: int
    headers: Dict[str, str]
    body: Optional[str] = None
    json: Optional[Union[Dict[str, Any], list[Dict[str, Any]]]] = None
