"""Run table operations wrapping the `run_table` RPC."""

from typing import Optional, Dict, Any, Tu<PERSON>, List
from ..client import supabase
from .models import RunTableRequest, RunTableResponse
from typing import Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg
from ...configuration import Configuration


def run_table(
    config: Annotated[RunnableConfig, InjectedToolArg],
    request: RunTableRequest,
):
    """Queue table cells for execution using the `run_table` RPC.

    Mirrors the RPC signature via `RunTableRequest` and returns the RPC JSON
    payload as a dictionary. Transport-level errors are returned via the error
    string; RPC-level errors are returned in the payload with status="error".
    """
    try:
        configuration = Configuration.from_runnable_config(config)
        table_id = request.table_id or configuration.table_id

        rpc_params = {
            'p_table_id': table_id,
            'p_column_names': request.column_names,
            'p_row_ids': request.row_ids,
            'p_apply_table_view': request.apply_table_view,
            'p_rows_limit': request.rows_limit,
            'p_auto_run': request.auto_run,
            'p_add_to_existing_run': request.add_to_existing_run,
        }

        response = supabase.rpc('run_table', rpc_params).execute()

        return response

    except Exception as e:
        return None, str(e)


