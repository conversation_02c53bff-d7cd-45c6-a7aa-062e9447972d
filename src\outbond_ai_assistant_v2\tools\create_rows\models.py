from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, ConfigDict


class CreateRowsRequest(BaseModel):
    """Request model for creating multiple rows.
    
    This model defines the structure for creating multiple rows with comprehensive
    validation and documentation following Google style guidelines.
    """
    model_config = ConfigDict(use_enum_values=True)

    row_count: int = Field(
        ...,
        description="The number of rows to create",
        ge=1,
        le=100,
        examples=[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    )

    
    data: Optional[List[Dict[str, Any]]] = Field(
        ...,
        description="The data to add to the rows. key is column name and value is cell value",
        min_length=0,
        max_length=75,
        examples=[[{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}]]
    )
    


class CreateRowsSingleResponse(BaseModel):
    """Response model for rows creation operations.
    
    This model defines the structure of successful rows creation responses,
    providing clear typing and documentation.
    """
    model_config = ConfigDict(use_enum_values=True)
    
    id: Optional[int] = Field(
        default=None,
        description="The id of the created rows when creation is successful"
    )

    table_id: Optional[str] = Field(
        default=None,
        description="The table id of the created rows when creation is successful"
    )

    cells: Optional[Dict[str, Any]] = Field(
        default=None,
        description="The cells of the created rows when creation is successful key is column name and value is cell value",
        examples=[[{"name": "<PERSON>e", "email": "<EMAIL>"}, {"name": "Jane Doe", "email": "<EMAIL>"}]]
    )



class CreateRowsResponse(BaseModel):
    """Response model for rows creation operations.
    
    This model defines the structure of successful rows creation responses,
    providing clear typing and documentation.
    """
    model_config = ConfigDict(use_enum_values=True)


    rows: Optional[List[CreateRowsSingleResponse]] = Field(
        default=None,
        description="The rows of the created rows when creation is successful",
        examples=[{"id": 1, "table_id": "tbl_c85117eb4106d464", "cells": {"name": "John Doe", "email": "<EMAIL>"}}, {"id": 2, "table_id": "tbl_c85117eb4106d464", "cells": {"name": "Jane Doe", "email": "<EMAIL>"}}]
    )
    

    
    success: bool = Field(
        default=True,
        description="Indicates if the operation was successful"
    )

    error_message: Optional[str] = Field(
        default=None,
        description="Detailed error message explaining what went wrong"
    )

