"""Pydantic models for LinkedIn profile column creation and editing operations."""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, ConfigDict, field_validator


class CreateAndEditLinkedInProfileColumnRequest(BaseModel):
    """Request model for creating or editing LinkedIn profile columns.
    
    This model defines the structure for creating/editing LinkedIn profile columns with comprehensive
    validation and documentation following Google style guidelines.
    """
    model_config = ConfigDict(use_enum_values=True)

    action: str = Field(
        ...,
        description="The action to perform on the LinkedIn profile column",
        min_length=1,
        max_length=255,
        examples=["COLUMN_CREATE", "COLUMN_UPDATE"]
    )
    
    column_name: str = Field(
        ...,
        description="Name of the LinkedIn profile column",
        min_length=1,
        max_length=255,
        examples=["LinkedIn Profile", "Person Profile", "Profile Data"]
    )
    
    linkedin_profile_url_injection_path: str = Field(
        ...,
        description="The injection sequence for LinkedIn profile URL (must be wrapped in double curly braces {{}})",
        min_length=4,
        examples=[
            "{{LinkedIn URL}}",
            "{{Profile.cell_details.linkedin_url}}",
            "{{Contact Info.cell_details.profile_link}}"
        ]
    )
    

    @field_validator('linkedin_profile_url_injection_path')
    @classmethod
    def validate_injection_sequence_format(cls, v: str) -> str:
        """Validate that injection sequence is wrapped in double curly braces."""
        if not isinstance(v, str):
            raise ValueError('linkedin_profile_url_injection_path must be a string')
        
        # Check if it starts with {{ and ends with }}
        if not (v.startswith('{{') and v.endswith('}}')):
            raise ValueError('linkedin_profile_url_injection_path must be wrapped in double curly braces {{}}')
        
        # Check that there's content between the braces
        content = v[2:-2].strip()
        if not content:
            raise ValueError('linkedin_profile_url_injection_path cannot be empty between the curly braces')
        
        return v


class LinkedInProfileColumnResponse(BaseModel):
    """Response model for LinkedIn profile column operations.
    
    This model defines the structure of successful LinkedIn profile column responses,
    providing clear typing and documentation.
    """
    model_config = ConfigDict(use_enum_values=True)
    
    column: Optional[Dict[str, Any]] = Field(
        default=None,
        description="The complete column data when operation is successful"
    )
    
    action: Optional[str] = Field(
        default=None,
        description="The action to perform on the LinkedIn profile column",
        min_length=1,
        max_length=255,
        examples=["COLUMN_CREATE", "COLUMN_UPDATE"]
    )
    
    success: bool = Field(
        default=True,
        description="Indicates if the operation was successful"
    )

    error_message: Optional[str] = Field(
        default=None,
        description="Detailed error message explaining what went wrong"
    )


class CreateOrEditLinkedInCompanyProfileColumnRequest(BaseModel):
    """Request model for creating or editing Linkedin company profile columns.
    
    This model defines the structure for creating/editing LinkedIn company profile columns with comprehensive
    validation and documentation following Google style guidelines.
    """
    model_config = ConfigDict(use_enum_values=True)

    action: str = Field(
        ...,
        description="The action to perform on the Linkedin company profile column",
        max_length=15,
        examples=["COLUMN_CREATE", "COLUMN_UPDATE"]
    )
    
    column_name: str = Field(
        ...,
        description="Name of the Linkedin company profile column",
        min_length=1,
        max_length=255,
        examples=["LinkedIn Company", "Company Column", "Company Data"]
    )
    
    linkedin_company_url_injection_path: str = Field(
        ...,
        description="The injection sequence for LinkedIn company profile URL (must be wrapped in double curly braces {{}})",
        min_length=4,
        examples=[
            "{{Linkedin Company URL}}",
            "{{Linkedin Profile.cell_details.company_linkedin_profile_url}}",
            "{{Linkedin Profile.cell_details.experiences.0.company_linkedin_profile_url}}"
        ]
    )
    

    @field_validator('linkedin_company_url_injection_path')
    @classmethod
    def validate_injection_sequence_format(cls, v: str) -> str:
        """Validate that injection sequence is wrapped in double curly braces."""
        if not isinstance(v, str):
            raise ValueError('linkedin_company_url_injection_path must be a string')
        
        # Check if it starts with {{ and ends with }}
        if not (v.startswith('{{') and v.endswith('}}')):
            raise ValueError('linkedin_company_url_injection_path must be wrapped in double curly braces {{}}')
        
        # Check that there's content between the braces
        content = v[2:-2].strip()
        if not content:
            raise ValueError('linkedin_company_url_injection_path cannot be empty between the curly braces')
        
        return v

class LinkedInCompanyProfileColumnResponse(BaseModel):
    """Response model for LinkedIn company profile column operations.
    
    This model defines the structure of successful LinkedIn company profile column responses,
    providing clear typing and documentation.
    """
    model_config = ConfigDict(use_enum_values=True)
    
    column: Optional[Dict[str, Any]] = Field(
        default=None,
        description="The complete column data when operation is successful"
    )
    
    action: Optional[str] = Field(
        default=None,
        description="The action to perform on the LinkedIn company profile column",
        max_length=15,
        examples=["COLUMN_CREATE", "COLUMN_UPDATE"]
    )
    
    success: bool = Field(
        default=True,
        description="Indicates if the operation was successful"
    )

    error_message: Optional[str] = Field(
        default=None,
        description="Detailed error message explaining what went wrong"
    )