from .leadmagic import run_leadmagic_task
from .prospeo import run_prospeo_task
from .findymail import run_findymail_task
from .millionverifier import run_millionverifier_task

# Define a dictionary mapping provider names to their implementations
email_finder_map = {
    "leadmagic": run_leadmagic_task,
    "prospeo": run_prospeo_task,
    "findymail": run_findymail_task,
}


def get_email_finder(provider: str):
    """Get the appropriate email finder function based on the provider name

    Args:
        provider: The name of the provider to use

    Returns:
        The email finder function for the specified provider, or leadmagic as fallback
    """
    return email_finder_map.get(provider, run_leadmagic_task)


email_verifier_map = {
    "millionverifier": run_millionverifier_task,
}


def get_email_verifier(provider: str):
    """Get the appropriate email verifier function based on the provider name

    Args:
        provider: The name of the provider to use

    Returns:
        The email verifier function for the specified provider, or millionverifier as fallback
    """
    return email_verifier_map.get(provider, run_millionverifier_task)
