import asyncio
import httpx
import json
import os
import time
from typing import Optional, List, Dict, Any
from colorama import init, Fore, Style

# Initialize colorama for colored terminal output
init()

async def test_get_table_rows(
    table_id: str,
    filters: Optional[Dict] = None,
    search_text: Optional[str] = None,
    sort_array: Optional[List[Dict]] = None,
    limit: int = 100,
    offset: int = 0,
    iterations: int = 1,
    delay_between_calls: float = 1.0
):
    """
    Test the table rows retrieval endpoint with detailed cache performance metrics.
    
    Args:
        table_id: The ID of the table to query
        filters: Optional filter conditions
        search_text: Optional search term
        sort_array: Optional sort rules
        limit: Number of rows to return per page
        offset: Starting position for pagination
        iterations: Number of times to call the API (to test caching)
        delay_between_calls: Delay in seconds between API calls
    """
    # API endpoint URL
    base_url = os.getenv("API_URL", "http://localhost:2024")
    url = f"{base_url}/table/rows"
    
    print(f"\n{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}Testing endpoint: {url}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
    
    # Prepare request payload
    payload = {
        "target_table_id": table_id,
        "filter_json": filters,
        "search_text": search_text,
        "sort_array": sort_array,
        "p_limit": limit,
        "p_offset": offset
    }
    
    print(f"\n{Fore.YELLOW}📝 Request Payload:{Style.RESET_ALL}")
    print(json.dumps(payload, indent=2))
    
    # Create client with increased timeout
    timeout = httpx.Timeout(30.0)  # 30 seconds timeout
    
    # Track metrics across iterations
    total_latency = 0
    cache_hits = 0
    cache_misses = 0
    
    for i in range(iterations):
        iteration_label = f"Iteration {i+1}/{iterations}"
        print(f"\n{Fore.MAGENTA}{'='*40} {iteration_label} {'='*40}{Style.RESET_ALL}")
        
        async with httpx.AsyncClient(timeout=timeout) as client:
            print(f"\n{Fore.BLUE}🔍 Sending request...{Style.RESET_ALL}")
            start_time = time.time()
            
            response = await client.post(url, json=payload)
            
            end_time = time.time()
            latency_s = end_time - start_time  # Seconds
            latency_ms = latency_s * 1000      # Milliseconds
            total_latency += latency_ms
            
            print(f"\n{Fore.GREEN}⏱️ Latency: {latency_s:.3f} s ({latency_ms:.2f} ms){Style.RESET_ALL}")
            
            if response.status_code == 200:
                result = response.json()
                
                # Calculate and print response size
                response_text = response.text
                response_size_bytes = len(response_text)
                response_size_kb = response_size_bytes / 1024
                response_size_mb = response_size_kb / 1024
                
                print(f"\n{Fore.YELLOW}📊 Response Size:{Style.RESET_ALL}")
                print(f"  Bytes: {response_size_bytes:,}")
                print(f"  KB: {response_size_kb:.2f}")
                if response_size_mb >= 1:
                    print(f"  MB: {response_size_mb:.2f}")
                
                # Print cache status if available
                if "from_cache" in result:
                    if result["from_cache"]:
                        cache_status = f"{Fore.GREEN}HIT ✅{Style.RESET_ALL}"
                        cache_hits += 1
                    else:
                        cache_status = f"{Fore.RED}MISS ❌{Style.RESET_ALL}"
                        cache_misses += 1
                    print(f"\n🔄 Cache: {cache_status}")
                
                # Print row count
                row_count = len(result.get('data', []))
                total_count = result.get('total_count', 0)
                print(f"\n{Fore.CYAN}📋 Rows:{Style.RESET_ALL}")
                print(f"  Returned: {row_count:,}")
                print(f"  Total available: {total_count:,}")
                print(f"  Page: {(offset // limit) + 1} of {(total_count + limit - 1) // limit}")
                
                # Print average bytes per row if we have rows
                if row_count > 0:
                    bytes_per_row = response_size_bytes / row_count
                    print(f"  Avg bytes per row: {bytes_per_row:.2f}")
                
                # Print sample of the data (first row)
                if row_count > 0:
                    print(f"\n{Fore.YELLOW}📝 Sample Row (First):{Style.RESET_ALL}")
                    sample_row = result['data'][0]
                    # Print a truncated version of the row to avoid overwhelming the console
                    sample_row_str = json.dumps(sample_row, indent=2)
                    if len(sample_row_str) > 500:
                        sample_row_str = sample_row_str[:500] + "... (truncated)"
                    print(sample_row_str)
            else:
                print(f"\n{Fore.RED}❌ Error {response.status_code}:{Style.RESET_ALL}")
                print(response.text)
                # Don't continue with iterations if there's an error
                break
        
        # Wait between iterations (except for the last one)
        if i < iterations - 1:
            print(f"\n{Fore.BLUE}⏳ Waiting {delay_between_calls} seconds before next request...{Style.RESET_ALL}")
            await asyncio.sleep(delay_between_calls)
    
    # Print summary statistics
    if iterations > 1:
        print(f"\n{Fore.CYAN}{'='*30} SUMMARY {'='*30}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}📊 Performance Metrics:{Style.RESET_ALL}")
        print(f"  Total Requests: {iterations}")
        print(f"  Cache Hits: {cache_hits} ({cache_hits/iterations*100:.1f}%)")
        print(f"  Cache Misses: {cache_misses} ({cache_misses/iterations*100:.1f}%)")
        print(f"  Average Latency: {total_latency/iterations:.2f} ms")
        
        # Calculate cache benefit
        if cache_hits > 0 and cache_misses > 0:
            # We need to track individual latencies for hits and misses to do this properly
            print(f"\n{Fore.YELLOW}💡 Note:{Style.RESET_ALL} For more accurate cache performance metrics,")
            print(f"  track separate latencies for cache hits and misses.")

if __name__ == "__main__":
    # Example usage with all parameters
    TABLE_ID = "tbl_3a5bdcaff1591af3"  # Replace with your table ID
    
    # Example filter condition
    FILTERS = {
        "operator": "AND",
        "rules": [
            {
                "column_id": 1,
                "operator": "nempty", 
                "value": ""
            }
        ]
    }
    
    # Example sort rules
    SORTS = [
        {
            "column_id": 1,
            "direction": "asc"
        }
    ]
    
    # Run the async test function with multiple iterations to test caching
    asyncio.run(test_get_table_rows(
        table_id=TABLE_ID,
        filters=None,  # Set to FILTERS to test filtering
        search_text=None,
        sort_array=None,  # Set to SORTS to test sorting
        limit=50,
        offset=1,
        iterations=2,  # Run 3 times to see cache performance
        delay_between_calls=1.0  # 2 second delay between calls
    ))
