# **Outbond: FastAPI + Redis Caching Implementation**

## **🚀 Overview**

This document outlines the implementation of **Redis caching** for Outbond’s table data, using **FastAPI** as the API server and **Redis** as the primary caching layer. PostgreSQL remains the source of truth, with **event-based synchronization** ensuring data consistency.

## **🔥 Goal**

- **Minimize latency** by serving table data from Redis.
- **Write-through caching**: Updates go to Redis first, then PostgreSQL in the background.
- **Event-driven cache invalidation** using PostgreSQL triggers and LISTEN/NOTIFY.

---

# **🚀 API Implementation**

## **1️⃣ Read: `get_table_data_by_row_ids`**

### **Flow:**

1. **Check Redis first** for the requested row IDs.
2. **If data is missing**, fetch from PostgreSQL, return to the user, and store it in Redis.
3. **Keep Redis fresh** by listening for updates from PostgreSQL.

### **Example RPC Response (Row Data Format)**

```json
{
  "data": [
    {
      "id": 1,
      "cells": [
        {
          "value": "https://www.linkedin.com/in/christian-peverelli/",
          "column_id": 1
        },
        {
          "value": "+18183124434",
          "column_id": 5,
          "run_status": { "run": "completed" }
        },
        { "value": "Christian Peverelli", "column_id": 2 },
        { "value": "Outbond", "column_id": 3 },
        { "value": "<EMAIL>", "column_id": 4 }
      ]
    }
  ],
  "table_id": "tbl_c85117eb4106d464",
  "total_count": 5
}
```

### **🔹 Redis Storage Format**

Each **row is stored as a Redis Hash**, where **column_id** is the field and **cell data** is the value.

```bash
HSET row:1 1 '{"value": "https://www.linkedin.com/in/christian-peverelli/"}'
HSET row:1 5 '{"value": "+18183124434", "run_status": {"run": "completed"}}'
HSET row:1 2 '{"value": "Christian Peverelli"}'
```

### **🔹 Redis Query for Fast Reads**

```bash
HGETALL row:1    # Retrieve all columns for row 1
HMGET row:1 2 3 4  # Retrieve specific columns (e.g., name, company, email)
```

---

## **2️⃣ Write: `paste_cells` (Write-Through Caching)**

### **Flow:**

1. **Write to Redis first** (fast response to the user).
2. **Flag row as `pending`** (`row:{row_id}:pending = true`).
3. **Sync with PostgreSQL in the background**.
4. **Once confirmed, remove the `pending` flag`**.

### **🔹 Example Payload**

```json
{
  "new_rows": [],
  "new_columns": [],
  "updated_cells": [
    {
      "value": "New data",
      "row_id": 3,
      "table_id": "tbl_c85117eb4106d464",
      "column_id": 1
    }
  ]
}
```

### **🔹 Redis Write Before PostgreSQL Sync**

```bash
SET row:3:pending "true"
HSET row:3 1 '{"value": "New data"}'
```

---

# **🚀 Event-Based Cache Invalidation**

Instead of using TTLs, we use **PostgreSQL LISTEN/NOTIFY** to refresh Redis **only when a row is updated.**

## **1️⃣ PostgreSQL Trigger to Notify FastAPI**

```sql
CREATE OR REPLACE FUNCTION notify_cache_invalidation() RETURNS TRIGGER AS $$
DECLARE
    payload JSON;
BEGIN
    payload := json_build_object('table_id', NEW.table_id, 'row_id', NEW.row_id);
    PERFORM pg_notify('cache_invalidation', payload::text);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER invalidate_cache_on_update
AFTER INSERT OR UPDATE OR DELETE ON cells
FOR EACH ROW EXECUTE FUNCTION notify_cache_invalidation();
```

## **2️⃣ FastAPI Listener to Sync Redis**

```python
import asyncio
import aioredis
import asyncpg
import json

async def listen_for_changes():
    conn = await asyncpg.connect("****************************************/postgres")
    redis = await aioredis.from_url("redis://localhost", decode_responses=True)

    async with conn.transaction():
        await conn.execute("LISTEN cache_invalidation;")

        while True:
            msg = await conn.fetchrow("SELECT pg_notify, * FROM pg_notification_queue()")
            if msg:
                event_data = json.loads(msg["pg_notify"])
                row_id = event_data["row_id"]
                redis_key = f"row:{row_id}"

                # Refresh Redis
                updated_row = await conn.fetchrow("SELECT * FROM get_table_data_by_row_ids($1::int[]);", [row_id])
                await redis.hset(redis_key, mapping=updated_row)
```

✅ **Keeps cache fresh automatically when PostgreSQL updates rows**.

---

# **🔥 Summary: Optimized Redis Caching for Outbond**

| **Action**                          | **Best Redis Command**                         |
| ----------------------------------- | ---------------------------------------------- |
| **Store a row with multiple cells** | `HSET row:{row_id} {column_id} {JSON_value}`   |
| **Retrieve all columns for a row**  | `HGETALL row:{row_id}`                         |
| **Retrieve specific columns**       | `HMGET row:{row_id} {column_id1} {column_id2}` |
| **Get all rows for a table**        | `SMEMBERS table:{table_id}:rows`               |
| **Mark row as "pending update"**    | `SET row:{row_id}:pending "true"`              |
| **Remove "pending update" flag**    | `DEL row:{row_id}:pending`                     |
| **Invalidate a cached row**         | `DEL row:{row_id}`                             |
| **Listen for PostgreSQL updates**   | `LISTEN cache_invalidation`                    |

✅ **Event-driven updates ensure cache is always fresh.**
✅ **FastAPI + Redis provides real-time, low-latency queries.**
✅ **Write-through caching minimizes DB load while ensuring persistence.**

---

### **🔥 Next Steps**

1️⃣ **Test PostgreSQL `LISTEN/NOTIFY` locally**
2️⃣ **Optimize Redis queries for batch processing**
3️⃣ **Monitor cache hits/misses for performance tuning**

---

How to add custom lifespan events¶
When deploying agents on the LangGraph platform, you often need to initialize resources like database connections when your server starts up, and ensure they're properly closed when it shuts down. Lifespan events let you hook into your server's startup and shutdown sequence to handle these critical setup and teardown tasks.

This works the same way as adding custom routes - you just need to provide your own Starlette app (including FastAPI, FastHTML and other compatible apps).

Below is an example using FastAPI.

Python only
We currently only support custom lifespan events in Python deployments with langgraph-api>=0.0.26.

Create app¶
Starting from an existing LangGraph Platform application, add the following lifespan code to your webapp.py file. If you are starting from scratch, you can create a new app from a template using the CLI.

langgraph new --template=new-langgraph-project-python my_new_project
Once you have a LangGraph project, add the following app code:

# ./src/agent/webapp.py

from contextlib import asynccontextmanager
from fastapi import FastAPI
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

@asynccontextmanager
async def lifespan(app: FastAPI): # for example...
engine = create*async_engine("postgresql+asyncpg://user:pass@localhost/db") # Create reusable session factory
async_session = sessionmaker(engine, class*=AsyncSession) # Store in app state
app.state.db_session = async_session
yield # Clean up connections
await engine.dispose()

app = FastAPI(lifespan=lifespan)

# ... can add custom routes if needed.
