import asyncio
from typing import List, Dict, Any
from src.core.config import get_settings
import httpx
import logging
from src.db.utils import get_type_id, get_type_name, format_date
from src.services.proxycurl.company.schemas import (
    CompanyProfileOutput,
    CompanyProfileResponse,
    CompanyProfileInput,
    LookupCompanyProfileInput,
    CompanyExtra,
    CompanyLocation,
)

settings = get_settings()
logger = logging.getLogger(__name__)


# Custom exceptions
class RateLimitException(Exception):
    def __init__(self, retry_after=None, message="Rate limit exceeded"):
        self.retry_after = retry_after
        self.message = message
        super().__init__(self.message)


def transform_company_profile(src: CompanyProfileOutput) -> List[Dict[str, Any]]:
    """Transform company profile data into a structured format"""
    result = [
        {
            "name": "LinkedIn company profile",
            "type_id": get_type_id("linkedin_company"),
            "type": get_type_name("linkedin_company"),
            "is_brand": True,
            "value": src.name,
        },
        {
            "name": "General info",
            "type_id": get_type_id("object"),
            "type": get_type_name("object"),
            "is_brand": False,
            "injection_sequence": "1",
            "value": [
                {
                    "name": "Name",
                    "type_id": get_type_id("company_name"),
                    "type": get_type_name("company_name"),
                    "is_brand": False,
                    "value": src.name,
                    "injection_sequence": "1.0",
                },
                {
                    "name": "LinkedIn URL",
                    "type_id": get_type_id("linkedin_company_url"),
                    "type": get_type_name("linkedin_company_url"),
                    "is_brand": False,
                    "value": (
                        f"https://www.linkedin.com/company/{src.universal_name_id}"
                        if src.universal_name_id
                        else ""
                    ),
                    "injection_sequence": "1.1",
                },
                {
                    "name": "Website",
                    "type_id": get_type_id("company_domain"),
                    "type": get_type_name("company_domain"),
                    "is_brand": False,
                    "value": src.website,
                    "injection_sequence": "1.2",
                },
                {
                    "name": "Tagline",
                    "type_id": get_type_id("text"),
                    "type": get_type_name("text"),
                    "is_brand": False,
                    "value": src.tagline,
                    "injection_sequence": "1.3",
                },
                {
                    "name": "Description",
                    "type_id": get_type_id("text"),
                    "type": get_type_name("text"),
                    "is_brand": False,
                    "value": src.description,
                    "injection_sequence": "1.4",
                },
                {
                    "name": "Type",
                    "type_id": get_type_id("text"),
                    "type": get_type_name("text"),
                    "is_brand": False,
                    "value": src.company_type,
                    "injection_sequence": "1.5",
                },
                {
                    "name": "Phone",
                    "type_id": get_type_id("phone"),
                    "type": get_type_name("phone"),
                    "is_brand": False,
                    "value": (
                        src.extra.phone_number
                        if src.extra and src.extra.phone_number
                        else ""
                    ),
                    "injection_sequence": "1.6",
                },
                {
                    "name": "Staff count",
                    "type_id": get_type_id("number"),
                    "type": get_type_name("number"),
                    "is_brand": False,
                    "value": src.company_size_on_linkedin,
                    "injection_sequence": "1.7",
                },
                {
                    "name": "Follower count",
                    "type_id": get_type_id("number"),
                    "type": get_type_name("number"),
                    "is_brand": False,
                    "value": src.follower_count,
                    "injection_sequence": "1.8",
                },
                {
                    "name": "ID",
                    "type_id": get_type_id("text"),
                    "type": get_type_name("text"),
                    "is_brand": False,
                    "value": src.linkedin_internal_id,
                    "injection_sequence": "1.9",
                },
                {
                    "name": "Founded year",
                    "type_id": get_type_id("number"),
                    "type": get_type_name("number"),
                    "is_brand": False,
                    "value": src.founded_year,
                    "injection_sequence": "1.10",
                },
                {
                    "name": "Crunchbase URL",
                    "type_id": get_type_id("text"),
                    "type": get_type_name("text"),
                    "is_brand": False,
                    "value": (
                        src.extra.crunchbase_profile_url
                        if src.extra and src.extra.crunchbase_profile_url
                        else ""
                    ),
                    "injection_sequence": "1.11",
                },
                {
                    "name": "Logo",
                    "type_id": get_type_id("text"),
                    "type": get_type_name("text"),
                    "is_brand": False,
                    "value": src.profile_pic_url,
                    "injection_sequence": "1.12",
                },
            ],
        },
        {
            "name": "Industry",
            "type_id": get_type_id("text"),
            "type": get_type_name("text"),
            "is_brand": False,
            "value": src.industry,
            "injection_sequence": "2",
        },
    ]

    # Add specialities if available
    if src.specialities:
        specialities_items = []
        for index, speciality in enumerate(src.specialities):
            specialities_items.append(
                {
                    "name": f"{index}",
                    "is_brand": False,
                    "value": speciality,
                    "type_id": get_type_id("text"),
                    "type": get_type_name("text"),
                    "injection_sequence": f"3.{index}",
                }
            )

        result.append(
            {
                "name": "Specialities",
                "type_id": get_type_id("array"),
                "type": get_type_name("array"),
                "is_brand": False,
                "injection_sequence": "3",
                "value": specialities_items,
            }
        )

    # Add locations if available
    if src.locations:
        locations_items = []
        for index, location in enumerate(src.locations):
            locations_items.append(
                {
                    "name": "Headquarter" if location.is_hq else "Branch",
                    "type_id": get_type_id("location"),
                    "type": get_type_name("location"),
                    "injection_sequence": f"4.{index}",
                    "is_brand": False,
                    "value": [
                        {
                            "name": "Type",
                            "type_id": get_type_id("text"),
                            "type": get_type_name("text"),
                            "is_brand": False,
                            "value": "Headquarter" if location.is_hq else "Branch",
                            "injection_sequence": f"4.{index}.0",
                        },
                        {
                            "name": "City",
                            "type_id": get_type_id("text"),
                            "type": get_type_name("text"),
                            "is_brand": False,
                            "value": location.city,
                            "injection_sequence": f"4.{index}.1",
                        },
                        {
                            "name": "Country",
                            "type_id": get_type_id("text"),
                            "type": get_type_name("text"),
                            "is_brand": False,
                            "value": location.country,
                            "injection_sequence": f"4.{index}.2",
                        },
                        {
                            "name": "Postal Code",
                            "type_id": get_type_id("text"),
                            "type": get_type_name("text"),
                            "is_brand": False,
                            "value": location.postal_code,
                            "injection_sequence": f"4.{index}.3",
                        },
                        {
                            "name": "Street Address",
                            "type_id": get_type_id("text"),
                            "type": get_type_name("text"),
                            "is_brand": False,
                            "value": location.line_1,
                            "injection_sequence": f"4.{index}.4",
                        },
                    ],
                }
            )

        result.append(
            {
                "name": "Locations",
                "type_id": get_type_id("array"),
                "type": get_type_name("array"),
                "injection_sequence": "4",
                "is_brand": False,
                "value": locations_items,
            }
        )

    # Add funding data if available
    if src.funding_data:
        funding_items = []
        for index, funding in enumerate(src.funding_data):
            investor_items = []
            if funding.investor_list:
                for i_index, investor in enumerate(funding.investor_list):
                    investor_items.append(
                        {
                            "name": investor.name,
                            "type_id": get_type_id("linkedin_profile_url"),
                            "type": get_type_name("linkedin_profile_url"),
                            "injection_sequence": f"5.{index}.4.{i_index}",
                            "is_brand": False,
                            "value": investor.linkedin_profile_url,
                        }
                    )

            funding_items.append(
                {
                    "name": "Funding Round",
                    "type_id": get_type_id("text"),
                    "type": get_type_name("text"),
                    "is_brand": False,
                    "injection_sequence": f"5.{index}",
                    "value": [
                        {
                            "name": "Funding Type",
                            "type_id": get_type_id("text"),
                            "type": get_type_name("text"),
                            "is_brand": False,
                            "value": funding.funding_type,
                            "injection_sequence": f"5.{index}.0",
                        },
                        {
                            "name": "Announced On",
                            "type_id": get_type_id("date"),
                            "type": get_type_name("date"),
                            "is_brand": False,
                            "injection_sequence": f"5.{index}.1",
                            "value": format_date(funding.announced_date),
                        },
                        {
                            "name": "Money Raised",
                            "injection_sequence": f"5.{index}.2",
                            "type_id": get_type_id("text"),
                            "type": get_type_name("text"),
                            "is_brand": False,
                            "value": funding.money_raised,
                        },
                        {
                            "name": "Number of Investors",
                            "type_id": get_type_id("number"),
                            "type": get_type_name("number"),
                            "is_brand": False,
                            "value": funding.number_of_investor,
                            "injection_sequence": f"5.{index}.3",
                        },
                        {
                            "name": "Investors",
                            "type_id": get_type_id("array"),
                            "type": get_type_name("array"),
                            "is_brand": False,
                            "injection_sequence": f"5.{index}.4",
                            "value": investor_items,
                        },
                    ],
                }
            )

        result.append(
            {
                "name": "Funding Data",
                "type_id": get_type_id("array"),
                "type": get_type_name("array"),
                "is_brand": False,
                "injection_sequence": "5",
                "value": funding_items,
            }
        )

    return result


async def get_company_profile(input: CompanyProfileInput) -> CompanyProfileResponse:
    """Fetch LinkedIn company profile data from the ProxyCurl API

    Args:
        input: CompanyProfileInput with linkedin_company_url

    Returns:
        CompanyProfileResponse with profile data and credit cost
    """
    try:
        if settings.IS_STRESS_TESTING:
            # wait for 30 seconds
            await asyncio.sleep(30)
            return CompanyProfileResponse(
                profile=CompanyProfileOutput(
                    profile_pic_url=settings.TESTING_LINKEDIN_PROFILE_PICTURE_URL,
                    name="Outbond",
                    universal_name_id="outbond",
                    website="https://outbond.io",
                    tagline="Outbond",
                    description="Outbond",
                    company_type="PRIVATELY_HELD",
                    company_size_on_linkedin=100,
                    follower_count=1000,
                    linkedin_internal_id="1234567890",
                    founded_year=2020,
                    industry="Technology",
                    specialities=[],
                    locations=[],
                    funding_data=[],
                    extra=CompanyExtra(
                        phone_number="1234567890",
                        contact_email="<EMAIL>"
                    )
                ),
                credit_cost=0,
            )
        async with httpx.AsyncClient() as client:
            response = await client.get(
                "https://nubela.co/proxycurl/api/linkedin/company",
                params={
                    "url": input.linkedin_company_url,
                    "use_cache": "if-recent",
                    "fallback_to_cache": "on-error",
                },
                headers={
                    "Authorization": f"Bearer {settings.PROXYCURL_API_KEY.get_secret_value()}"
                },
                timeout=60.0,  # 60 second timeout
            )
            response.raise_for_status()

            # Extract credit cost from headers
            credit_cost = 0
            if "X-Proxycurl-Credit-Cost" in response.headers:
                try:
                    credit_cost = int(response.headers["X-Proxycurl-Credit-Cost"])
                except (ValueError, TypeError):
                    # If header exists but can't be converted to int
                    logger.warning(
                        f"Could not parse credit cost from header: {response.headers.get('X-Proxycurl-Credit-Cost')}"
                    )

            # Parse the response JSON
            profile_data = response.json()

            # Create the CompanyProfileOutput object
            profile = CompanyProfileOutput(**profile_data)

            # Return the response with profile and credit cost
            return CompanyProfileResponse(
                profile=profile,
                credit_cost=credit_cost,
            )

    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            logger.warning("Company profile not found")
            raise ValueError("Company profile not found")
        elif e.response.status_code == 429:
            logger.warning("Rate limit exceeded")
            # Extract retry-after header if available
            retry_after = e.response.headers.get("Retry-After")
            raise RateLimitException(
                retry_after=retry_after, message="Rate limit exceeded. Try again later."
            )
        else:
            logger.error(f"HTTP error: {e.response.status_code} - {e.response.text}")
            raise e
    except Exception as e:
        logger.error(f"Error fetching company profile: {str(e)}")
        raise e


async def lookup_company_profile(
    input: LookupCompanyProfileInput,
) -> CompanyProfileResponse:
    """Fetch LinkedIn company profile data from the ProxyCurl API using company domain

    Args:
        input: LookupCompanyProfileInput with company_domain

    Returns:
        CompanyProfileResponse with profile data and credit cost
    """
    try:
        if settings.IS_STRESS_TESTING:
            # wait for 30 seconds
            await asyncio.sleep(30)
            return CompanyProfileResponse(
                profile=CompanyProfileOutput(
                    profile_pic_url=settings.TESTING_LINKEDIN_PROFILE_PICTURE_URL,
                    name="Outbond",
                    universal_name_id="outbond",
                    website="https://outbond.io",
                    tagline="Outbond",
                    description="Outbond",
                    company_type="PRIVATELY_HELD",
                    company_size_on_linkedin=100,
                    follower_count=1000,
                    linkedin_internal_id="1234567890",
                    founded_year=2020,
                    industry="Technology",
                    specialities=[],
                    locations=[],
                    funding_data=[],
                    extra=CompanyExtra(
                        phone_number="1234567890",
                        contact_email="<EMAIL>"
                    )
                ),
                credit_cost=0,
            )
        async with httpx.AsyncClient() as client:
            response = await client.get(
                "https://nubela.co/proxycurl/api/linkedin/company/resolve",
                params={
                    "company_domain": input.company_domain,
                    "enrich_profile": "enrich",
                },
                headers={
                    "Authorization": f"Bearer {settings.PROXYCURL_API_KEY.get_secret_value()}"
                },
                timeout=60.0,  # 60 second timeout
            )
            response.raise_for_status()

            # Extract credit cost from headers
            credit_cost = 0
            if "X-Proxycurl-Credit-Cost" in response.headers:
                try:
                    credit_cost = int(response.headers["X-Proxycurl-Credit-Cost"])
                except (ValueError, TypeError):
                    # If header exists but can't be converted to int
                    logger.warning(
                        f"Could not parse credit cost from header: {response.headers.get('X-Proxycurl-Credit-Cost')}"
                    )

            # Parse the response JSON
            result = response.json()
            logger.info(f"Response JSON: {result}")
            # The response contains the profile directly

            profile_data = result["profile"]

            # Create the CompanyProfileOutput object
            profile = CompanyProfileOutput(**profile_data)

            # Return the response with profile and credit cost
            return CompanyProfileResponse(
                profile=profile,
                credit_cost=credit_cost,
            )

    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            logger.warning("Company profile not found")
            raise ValueError("Company profile not found")
        elif e.response.status_code == 429:
            logger.warning("Rate limit exceeded")
            # Extract retry-after header if available
            retry_after = e.response.headers.get("Retry-After")
            raise RateLimitException(
                retry_after=retry_after, message="Rate limit exceeded. Try again later."
            )
        else:
            logger.error(f"HTTP error: {e.response.status_code} - {e.response.text}")
            raise e
    except Exception as e:
        logger.error(f"Error fetching company profile: {str(e)}")
        raise e
