"""Database upsert operations for smart column management."""

from ..client import supabase
from .models import CreateRowsRequest, CreateRowsResponse
from typing import Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg
from ...configuration import Configuration


def create_rows(
    config: Annotated[RunnableConfig, InjectedToolArg],
    request: CreateRowsRequest
) -> CreateRowsResponse:
    """Create a row in the database."""
    try:
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id

        response = supabase.rpc('create_row_v2', {
            "p_table_id": table_id,
            "p_data": request.data,
        }).execute()
        
        # Success case: RPC returns data with column
        if hasattr(response, 'data') and response.data and 'id' in response.data:
            return CreateRowsResponse(
                rows=response.data,
                success=True
            )
        
        # Unexpected response format
        return CreateRowsResponse(
            error_message=f"Unexpected response from create row for row '{request.data}'",
            success=False
        )
            
    except Exception as error:
        # Error case
        return CreateRowsResponse(
            error_message=f"Failed to create row '{request.data}': {str(error)}",
            success=False
        )

