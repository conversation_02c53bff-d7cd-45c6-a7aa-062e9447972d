"""Database upsert operations for smart column management."""

from ..client import supabase
from .models import NotifyFERequest, NotifyFEResponse
from typing import Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg
from ...configuration import Configuration


def notify_fe(
    config: Annotated[RunnableConfig, InjectedToolArg],
    request: NotifyFERequest
) -> NotifyFEResponse:
    """Notify Frontend about changes in the table."""
    try:


        response = supabase.rpc('notify_fe_generic', {
            "channel": request.channel,
            "type": request.type,
            "data": request.payload,
        }).execute()
        
        # Success case: RPC returns data with column
        if hasattr(response, 'data') and response.data:
            return NotifyFEResponse(
                success=response.data['success'],
            )
        
        # Unexpected response format
        return NotifyFEResponse(
            error_message=f"Unexpected response from notify FE for channel '{request.channel}'",
            success=False
        )
            
    except Exception as error:
        # Error case
        return NotifyFEResponse(
            error_message=f"Failed to notify FE for channel '{request.channel}': {str(error)}",
            success=False
        )


