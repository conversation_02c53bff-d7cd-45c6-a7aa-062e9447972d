"""Database upsert operations for smart column management."""

from ..client import supabase
from .model import CreateNormalColumnRequest, CreateNormalColumnResponse
from typing import Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg
from ...configuration import Configuration


def create_normal_column(
    config: Annotated[RunnableConfig, InjectedToolArg],
    request: CreateNormalColumnRequest
) -> CreateNormalColumnResponse:
    """Create a normal "non-smart" column in the database."""
    try:
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id

        response = supabase.rpc('create_column_v3', {
            "p_table_id": table_id,
            "p_name": request.column_name,
            "p_type_id": request.type_id,
        }).execute()
        
        # Success case: RPC returns data with column
        if hasattr(response, 'data') and response.data and 'column' in response.data:
            return CreateNormalColumnResponse(
                column=response.data['column'],
                success=True
            )
        
        # Unexpected response format
        return CreateNormalColumnResponse(
            error_message=f"Unexpected response from create column for column '{request.column_name}'",
            success=False
        )
            
    except Exception as error:
        # Error case
        return CreateNormalColumnResponse(
            error_message=f"Failed to create column '{request.column_name}': {str(error)}",
            success=False
        )


# def update_normal_column(
#     config: Annotated[RunnableConfig, InjectedToolArg],
#     request: UpdateNormalColumnRequest
# ) -> UpdateNormalColumnResponse:
#     """Update a normal "non-smart" column in the database."""
#     try:
#         configuration = Configuration.from_runnable_config(config)
#         table_id = configuration.table_id

#         response = supabase.rpc('update_column_v3', {
#             "p_id": request.column_id,
#             "p_table_id": table_id,
#             "p_name": request.column_name,
#             "p_type_id": request.type_id,
#             "p_index": request.index
#         }).execute()
        
#         # Success case: RPC returns data with column
#         if hasattr(response, 'data') and response.data and 'column' in response.data:
#             return UpdateNormalColumnResponse(
#                 column=response.data['column'],
#                 success=True
#             )
        
#         # Unexpected response format
#         return UpdateNormalColumnResponse(
#             error_message=f"Unexpected response from update column for column '{request.column_name}'",
#             success=False
#         )

#     except Exception as error:
#         # Error case
#         return UpdateNormalColumnResponse(
#             error_message=f"Failed to update column '{request.column_name}': {str(error)}",
#             success=False
#         )