"""LinkedIn person profile column creation tool."""

import json
import logging
from typing import Annotated

from bond_ai.configuration import Configuration
from langchain_core.messages import ToolMessage
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg, InjectedToolCallId, tool
from langgraph.config import get_stream_writer
from langgraph.types import Command

from ..agent_db import upsert_smart_column
from ..supabase.validate_injection_sequance.models import (
    TargetDataType,
    ValidateInjectionSequenceRequest,
)
from ..supabase.validate_injection_sequance.validate_injection_sequance import (
    validate_injection_sequence,
)
from ..utilities.run_and_store_results_summery import run_and_store_results_summary
from ..supabase.run_table import RunTableRequest


@tool
def upsert_linkedin_person_profile_column_from_url(
    tool_call_id: Annotated[str, InjectedToolCallId],
    config: Annotated[RunnableConfig, InjectedToolArg],
    column_name: str,
    linkedin_profile_url: str,
    column_id: int | None = None,
) -> Command:
    """Create or update a LinkedIn person profile column using a profile URL.

    IMPORTANT: ALWAYS provide the column_id if you want to update/edit an existing column. Do NOT duplicate the column by NOT providing the column_id.

    Parameters:
        config: Configuration injected by the system
        column_name: A short descriptive name of the column. Must be unique per table.
        linkedin_profile_url: A SINGLE injection path of the LinkedIn profile URL data point.
        column_id: Optional ID of the existing column to update/edit. If not provided, a new column will be created.

    Returns:
        Command: Updates graph state with a ToolMessage and, on success, appends a memory reference to memory_refs.
    """
    try:
        from ..memory.vector_store import memory_store
        from ..registry.registry import SupervisorSubAgents
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Working on {column_name} column"})

       
        linkedin_profile_url = "_".join(linkedin_profile_url.strip().lower().split()) 
        linkedin_profile_url = f"$${linkedin_profile_url}$$"
        
        logging.info(f"\n[Debug] upsert_linkedin_PERSON_profile_column_from_url:\n  **Column name: {column_name}\n  **linkedin_profile_url: {linkedin_profile_url}\n  **column_id: {column_id}\n")
        
      
        
        
        # Set up metadata
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        service_id = 7


                # Step 1: Validate injection sequence
        validation_request = ValidateInjectionSequenceRequest(
            injection_sequence=linkedin_profile_url,
            target_data_type=TargetDataType.LINKEDIN_PROFILE_URL
        )
        logging.info(f"\n[Debug]  Validation request:\n **   {validation_request.model_dump()}     **\n")

        validation_result = validate_injection_sequence(config=config, request=validation_request)
        logging.info(f"\n[Debug]  Validation result:\n **    {validation_result.model_dump()}     **\n")
   
        # If validation failed, return Command with error
        if not validation_result.success:
            msg = validation_result.error_message or "Validation failed for the provided injection sequence."
            return Command(update={
                "last_error_message": msg,
                "messages": [
                    ToolMessage(
                        content=msg,
                        tool_call_id=tool_call_id,
                    )
                ],
            })

        inputs = [
            {"linkedin_profile_url": linkedin_profile_url} 
        ]
        
        # Set up providers as specified in the payload
        providers = [
            {"providers": ["outbond"]}
        ]
        
        # Set up parameters (empty dict as in the payload)
        parameters = [{}]
        
        # Call the create_smart_column function
        response, error = upsert_smart_column(
            table_id=table_id,
            column_name=column_name,
            service_id=service_id,
            inputs=inputs,
            parameters=parameters,
            providers=providers,
            column_id=column_id
        )
        
   

        if error:
            msg = f"Failed to upsert LinkedIn person profile column: {error}"
            return Command(update={
                "last_error_message": msg,
                "messages": [
                    ToolMessage(
                        content=msg,
                        tool_call_id=tool_call_id,
                    )
                ]
            })
            
        # Extract column details from response
        column = response.get('column', {})
        column_id = column.get('id')
        column_type = column.get('type') 
        name = column.get('name')
        
        action = "updated" if column_id is not None else "created"
        
        # Create JSON response
        response_json = {
            "action": action,
            "column_name": name,
            "column_id": column_id,
            "message": f"The LinkedIn profile column was successfully {action}."
        }

        memory_content = f"Successfully {action} LinkedIn person profile column '{column_name}' using URL: {linkedin_profile_url}"
        memory_ref = memory_store.write(SupervisorSubAgents.linkedin_enrichment_agent.value.name, memory_content)
        logging.info(f"\n[Debug] linkedin_enrichment_agent  Memory ref: {memory_ref}\n")

        # After successful create/update, run first 5 rows for this column and wait for results
        last_error_message: str | None = None
        try:
            run_request = RunTableRequest(
                table_id=table_id,
                column_names=[name] if name else None,
                apply_table_view=True,
            )
            run_data, run_error = run_and_store_results_summary(
                config=config,
                request=run_request,
                poll_interval_seconds=1.0,
                max_attempts=10,
            )
            if run_error:
                last_error_message = f"Run initiation/polling error: {run_error}"
                run_section = {
                    "action": "ran_first_5_rows",
                    "status": "error",
                    "message": last_error_message,
                }
            else:
                run_section = {
                    "action": "ran_first_5_rows",
                    "status": "success",
                    "column_name": name,
                    "message": "Completed polling for first 5 rows.",
                    "data": run_data,
                }
        except Exception as run_exc:
            last_error_message = f"Exception during run & summary: {str(run_exc)}"
            run_section = {
                "action": "ran_first_5_rows",
                "status": "exception",
                "message": last_error_message,
            }

        # Single tool result message combining upsert and run summaries
        combined_payload = {
            "upsert": response_json,
            "run": run_section,
        }

        return Command(update={
            "last_error_message": last_error_message,
            "memory_refs": [memory_ref],
            "messages": [
                ToolMessage(
                    content=json.dumps(combined_payload),
                    tool_call_id=tool_call_id,
                )
            ],
        })


    except Exception as e:
        logging.error(f"\n[Debug]  Exception: {e}\n")
        msg = f"Exception while upserting LinkedIn person profile column: {str(e)}"
        return Command(update={
            "last_error_message": msg,
            "messages": [
                ToolMessage(
                    content=msg,
                    tool_call_id=tool_call_id,
                )
            ]
        })

