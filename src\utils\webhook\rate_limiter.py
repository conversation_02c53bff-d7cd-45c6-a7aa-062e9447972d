from typing import Dict, Any, Optional
from pydantic import BaseModel
from fastapi import HTTPException, Request


class WebhookPayload(BaseModel):
    """Generic webhook payload model"""

    data: Dict[str, Any]
    timestamp: Optional[str] = None
    source: Optional[str] = None


async def ip_identifier(request: Request):
    """Identifier for IP-based rate limiting"""
    forwarded = request.headers.get("X-Forwarded-For")
    if forwarded:
        return f"ip:{forwarded.split(',')[0].strip()}"
    else:
        client_ip = request.client.host if request.client else "unknown"
        return f"ip:{client_ip}"


async def webhook_id_identifier(request: Request):
    """Identifier for webhook ID-based rate limiting"""
    webhook_id = request.path_params.get("webhook_id", "unknown")
    return f"webhook:{webhook_id}"


async def custom_callback(request: Request, response, pexpire: int):
    """Custom callback for rate limit exceeded"""
    expire_seconds = pexpire // 1000
    webhook_id = request.path_params.get("webhook_id", "unknown")

    raise HTTPException(
        status_code=429,
        detail={
            "error": "Rate limit exceeded",
            "webhook_id": webhook_id,
            "retry_after": expire_seconds,
            "message": f"Too many requests for webhook {webhook_id}",
        },
        headers={"Retry-After": str(expire_seconds)},
    )
