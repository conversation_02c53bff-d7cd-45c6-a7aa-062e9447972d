"""Define tools for the ReAct agent."""

from typing import Optional, Any, cast, List
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from langchain_community.tools.tavily_search import TavilySearchResults
from typing import Annotated
from langchain_core.tools import InjectedToolArg, Tool
from agent.configuration import Configuration
import os
from dotenv import load_dotenv
import asyncio

load_dotenv()

FIRECRAWL_API_KEY = os.getenv("FIRECRAWL_API_KEY")

# Import FireCrawl Python SDK instead of LangChain wrapper
try:
    from firecrawl import FirecrawlApp
    FIRECRAWL_AVAILABLE = True
except ImportError:
    FIRECRAWL_AVAILABLE = False


@tool
def search(
    query: str, *, config: Annotated[RunnableConfig, InjectedToolArg]
) -> Optional[list[dict[str, Any]]]:
    """Search for general web results.

    This function performs a search using the Tavily search engine, which is designed
    to provide comprehensive, accurate, and trusted results. It's particularly useful
    for answering questions about current events.
    """
    configuration = Configuration.from_runnable_config(config)
    wrapped = TavilySearchResults(max_results=configuration.max_search_results)
    
    # Use invoke instead of ainvoke for synchronous operation
    result = wrapped.invoke({"query": query})
    return cast(list[dict[str, Any]], result)


@tool
def scrape_website(url: str) -> str:
    """Scrape a website and return its content in clean, LLM-ready markdown format.
    
    This tool uses FireCrawl to extract content from a single webpage. It's useful when you need
    detailed information from a specific page. The content is returned as clean markdown,
    making it ideal for analysis.
    
    Args:
        url: The URL of the website to scrape (e.g., "https://example.com")
        
    Returns:
        The content of the website in markdown format
    """
    if not FIRECRAWL_API_KEY:
        return "Error: FIRECRAWL_API_KEY environment variable is not set."
    
    if not FIRECRAWL_AVAILABLE:
        return "Error: firecrawl-py package is not installed. Please install it with: pip install firecrawl-py"
    
    try:
        app = FirecrawlApp(api_key=FIRECRAWL_API_KEY)
        
        # Use the exact syntax from FireCrawl docs
        scrape_result = app.scrape_url(url, formats=['markdown'])
        
        # Check if we have the expected structure
        if not scrape_result:
            return "No content was found on the provided URL."
        
        # Try to access the markdown content
        try:
            # The response should be a dict with 'data' containing 'markdown'
            if isinstance(scrape_result, dict):
                markdown_content = scrape_result.get('data', {}).get('markdown', '')
            else:
                # If it's an object, try attribute access
                markdown_content = getattr(scrape_result, 'markdown', '') or getattr(scrape_result.data, 'markdown', '') if hasattr(scrape_result, 'data') else ''
            
            if not markdown_content:
                return f"No markdown content found. Response: {scrape_result}"
            
            return f"Content from {url}:\n\n{markdown_content}"
            
        except Exception as parse_error:
            return f"Error parsing response: {parse_error}. Raw response: {scrape_result}"
    
    except Exception as e:
        return f"Error scraping website: {str(e)}"



# Create tool objects
search_tool = Tool.from_function(
    func=search,
    name="search",
    description=search.__doc__
)

# Add all tools to the list
tools = [search_tool, scrape_website]

# Create a lookup dictionary for tools by name
tools_by_name = {tool.name: tool for tool in tools}

