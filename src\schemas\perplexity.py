from enum import Enum


class PerplexityModel(str, Enum):
    SONAR = "perplexity/sonar"
    SONAR_PRO = "perplexity/sonar-pro"
    SONAR_DEEP_RESEARCH = "perplexity/sonar-deep-research"
    SONAR_REASONING = "perplexity/sonar-reasoning"
    SONAR_REASONING_PRO = "perplexity/sonar-reasoning-pro"


class PerplexitySearchMode(str, Enum):
    ACADEMIC = "academic"
    WEB = "web"


class PerplexityReasoningEffort(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class PerplexitySearchContextSize(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"