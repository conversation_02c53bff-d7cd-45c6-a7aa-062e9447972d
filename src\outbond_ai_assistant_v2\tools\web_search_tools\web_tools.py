import os
from langchain_tavily import TavilySearch
from langchain_core.tools import tool
from firecrawl import FirecrawlApp



# Initialize the TavilySearch instance
_web_search = TavilySearch(max_results=10)


@tool
def web_search_tool(query: str) -> str:
    """
    Perform a web search using Tavily Search.
    
    Args:
        query: The search query string
        
    Returns:
        Search results as a formatted string
    """
    try:
        results = _web_search.invoke(query)
        
        if not results or "results" not in results:
            return "No search results found."
        
        # Format the results
        formatted_results = []
        for i, result in enumerate(results["results"], 1):
            formatted_result = f"{i}. {result.get('title', 'No title')}\n"
            formatted_result += f"   URL: {result.get('url', 'No URL')}\n"
            formatted_result += f"   Content: {result.get('content', 'No content')}\n"
            formatted_results.append(formatted_result)
        
        return "\n".join(formatted_results)
    
    except Exception as e:
        return f"Error performing web search: {str(e)}"


@tool
def scrape_website(url: str) -> str:
    """
    Scrape a website and return its content in clean, LLM-ready markdown format.
    
    This tool uses FireCrawl to extract content from a single webpage. It's useful when you need
    detailed information from a specific page. The content is returned as clean markdown,
    making it ideal for analysis.
    
    Args:
        url: The URL of the website to scrape (e.g., "https://example.com")
        
    Returns:
        The content of the website in markdown format
    """
    firecrawl_api_key = os.getenv("FIRECRAWL_API_KEY")
    if not firecrawl_api_key:
        return "Error: FIRECRAWL_API_KEY environment variable is not set."

    try:
        app = FirecrawlApp(api_key=firecrawl_api_key)
        scrape_result = app.scrape_url(url, formats=['markdown'])
        
        if not scrape_result:
            return "No content was found on the provided URL."
        
        # Extract markdown content from the response
        if isinstance(scrape_result, dict):
            markdown_content = scrape_result.get('data', {}).get('markdown', '')
        else:
            # Handle object response
            data = getattr(scrape_result, 'data', None)
            markdown_content = getattr(data, 'markdown', '') if data else ''
        
        if not markdown_content:
            return f"No markdown content found in the response."
        
        return f"Content from {url}:\n\n{markdown_content}"
    
    except Exception as e:
        return f"Error scraping website: {str(e)}"


# List of available tools for the search agent
SEARCH_TOOLS = [web_search_tool, scrape_website]
