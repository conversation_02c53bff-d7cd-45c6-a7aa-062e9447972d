# Environment
ENV=dev
DEBUG=true

# To separate your traces from other application
LANGSMITH_PROJECT=outbond-dev
LANGSMITH_API_KEY=lsv2_0000
# The following depend on your selected configuration

## LLM choice:
ANTHROPIC_API_KEY=sk-0000
FIREWORKS_API_KEY=fw_0000
OPENAI_API_KEY=sk-0000
PPLX_API_KEY=pplx-0000

# Redis Configuration
REDIS_URL=redis://:0000@000.000.000.000:6379/0
CELERY_BROKER_URL=redis://:0000@000.000.000.000:6379/0
CELERY_RESULT_BACKEND=redis://:0000@000.000.000.000:6379/0

# PostgreSQL Configuration
POSTGRES_URL=***********************************************/postgres

SUPABASE_URL=https://dev-api.outbond.io
SUPABASE_KEY=0000

# Your Supabase anon/public key
SUPABASE_SERVICE_ROLE_KEY=0000


# AWS Configuration
AWS_ACCESS_KEY_ID=0000
AWS_SECRET_ACCESS_KEY=0000


# Model Configuration
RUN_ONLY_IF_MODEL=openai/gpt-4o-mini
AI_COLUMN_MODEL=openai/gpt-4o-mini
AI_FORMULA_MODEL=openai/gpt-4o-mini
AI_RESEARCH_MODEL=openai/gpt-4o-mini

# Bedrock Model:
#BEDROCK_MODEL=Bedrock/us.amazon.nova-micro-v1:0

# Tools API Keys
TAVILY_API_KEY=0000
FIRECRAWL_API_KEY=0000

# Service provider API keys
PROXYCURL_API_KEY=0000
LEADMAGIC_API_KEY=0000
PROSPEO_API_KEY=0000
FINDYMAIL_API_KEY=0000
MILLIONVERIFIER_API_KEY=0000


IS_STRESS_TESTING=false
TESTING_LINKEDIN_PROFILE_PICTURE_URL=xx

LIMA_API_KEY=xx