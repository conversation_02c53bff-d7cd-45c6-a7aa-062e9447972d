# app/worker/celery_app.py
from celery import Celery
from src.core.config import get_settings

# Get application settings
settings = get_settings()

# Create the Celery instance
app = Celery(
    "worker",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=[
        "src.worker.tasks.lima",
        "src.worker.tasks.proxycurl",
        "src.worker.tasks.llm",
        "src.worker.tasks.openai",
        "src.worker.tasks.perplexity",
        "src.worker.tasks.chaining",
        "src.worker.tasks.leadmagic",
        "src.worker.tasks.prospeo",
        "src.worker.tasks.findymail",
        "src.worker.tasks.millionverifier",
        "src.worker.tasks.http",
        "src.worker.tasks.db",
        "src.worker.tasks.csv",
    ],
    broker_pool_limit=100,
    broker_heartbeat=30,
)

# Optional: Load celery configuration from an object
app.config_from_object("src.worker.worker_config")

# Optional: Configure periodic tasks
app.conf.beat_schedule = {}

if __name__ == "__main__":
    app.start()
