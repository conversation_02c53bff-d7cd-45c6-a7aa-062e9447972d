import json
import time
import logging
from fastapi import HTTPEx<PERSON>, BackgroundTasks
from typing import Dict, Any, List

# Configure logging
logger = logging.getLogger("backend_api.server")

async def get_table_data_by_row_ids_handler(
    table_id: str,
    row_ids: List[int],
    redis: Any,
    pg_pool: Any,
    background_tasks: BackgroundTasks
):
    try:
        start_time = time.time()
        
        # 1. Check cache for rows using GET instead of HGETALL
        # This is much faster as we store entire row as single JSON blob
        t0 = time.time()
        pipe = redis.pipeline(transaction=False)  # transaction=False for speed
        for row_id in row_ids:
            pipe.get(f"row:{row_id}")  # Single key per row instead of hash
        
        cached_blobs = await pipe.execute()
        cache_check_time = (time.time() - t0) * 1000
        print(f"  Cache check time: {cache_check_time:.0f}ms")
        
        # 2. Process cache results and identify missing rows
        response_data = []
        missing_row_ids = []
        
        t0 = time.time()
        for row_id, blob in zip(row_ids, cached_blobs):
            if blob:
                # Only one JSON parse per row instead of per cell
                row_data = json.loads(blob)
                response_data.append(row_data)
            else:
                missing_row_ids.append(row_id)
        
        json_parse_time = (time.time() - t0) * 1000
        print(f"  JSON parse time: {json_parse_time:.0f}ms")
        print(f"  Cache hits: {len(row_ids) - len(missing_row_ids)}")
        print(f"  Cache misses: {len(missing_row_ids)}")
        
        # 3. If all rows found in cache, return immediately
        if len(missing_row_ids) == 0:
            print("\n✅ All rows found in cache")
            end_time = time.time()
            total_time = (end_time - start_time) * 1000
            print(f"  Total time (cache hit): {total_time:.0f}ms")
            
            return {
                "data": response_data,
                "table_id": table_id,
                "total_count": len(row_ids)
            }
        
        # 4. Fetch ONLY missing rows from PostgreSQL
        print(f"\n📡 Fetching {len(missing_row_ids)} missing rows from PostgreSQL")
        t0 = time.time()
        async with pg_pool.acquire() as conn:
            rows = await conn.fetch(
                """
                SELECT * FROM get_table_data_by_row_ids(
                    p_row_ids := $1::int[],
                    p_table_id := $2::text
                );
                """,
                missing_row_ids,  # Only fetch missing rows!
                table_id
            )
            
            if not rows:
                # No rows found, just return what we have from cache
                end_time = time.time()
                total_time = (end_time - start_time) * 1000
                print(f"  No missing rows found in DB")
                print(f"  Total time (partial cache): {total_time:.0f}ms")
                
                return {
                    "data": response_data,
                    "table_id": table_id,
                    "total_count": len(response_data)
                }
            
            pg_data = json.loads(rows[0]['get_table_data_by_row_ids'])
            db_fetch_time = (time.time() - t0) * 1000
            print(f"  DB fetch time: {db_fetch_time:.0f}ms")
            
            # Add missing rows to response data
            new_rows = pg_data.get('data', [])
            print(f"  Retrieved {len(new_rows)} rows from DB")
            response_data.extend(new_rows)
        
        # 5. Background task to cache the missing rows
        async def cache_missing_rows():
            try:
                t0 = time.time()
                pipe = redis.pipeline(transaction=False)
                
                for row in new_rows:
                    row_id = row['id']
                    # Store entire row as a single JSON string
                    row_json = json.dumps(row)
                    pipe.set(f"row:{row_id}", row_json)
                    pipe.expire(f"row:{row_id}", 900)  # 15 minutes TTL
                
                await pipe.execute()
                cache_time = (time.time() - t0) * 1000
                print(f"\n💾 Background cache complete:")
                print(f"  Cache storage time: {cache_time:.0f}ms")
                print(f"  Rows cached: {len(new_rows)}")
                
            except Exception as e:
                print(f"\n❌ Background caching failed: {str(e)}")
        
        # Add caching to background tasks
        background_tasks.add_task(cache_missing_rows)
        
        # 6. Sort response data to match requested order
        t0 = time.time()
        row_id_to_index = {row_id: idx for idx, row_id in enumerate(row_ids)}
        response_data.sort(key=lambda x: row_id_to_index.get(x['id'], 999999))
        sort_time = (time.time() - t0) * 1000
        print(f"  Sort time: {sort_time:.0f}ms")
        
        # Return the complete data
        end_time = time.time()
        total_time = (end_time - start_time) * 1000
        print(f"\n✅ Request completed:")
        print(f"  Total time: {total_time:.0f}ms")
        print(f"  Rows returned: {len(response_data)}")
        
        return {
            "data": response_data,
            "table_id": table_id,
            "total_count": len(response_data)
        }

    except Exception as e:
        print(f"\n❌ Request failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 