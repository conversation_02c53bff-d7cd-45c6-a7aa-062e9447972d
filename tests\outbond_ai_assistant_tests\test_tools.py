import sys
import os
from typing import Dict, Any, Optional, Tuple
import json

# Print current directory for debugging
print(f"Current directory: {os.getcwd()}")
print(f"Script directory: {os.path.dirname(os.path.abspath(__file__))}")

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../src')))

from outbond_ai_assistant.tools import get_structured_table_data
from outbond_ai_assistant.agent_db import get_table_data

def test_get_structured_table_data(table_id, max_rows: int = 1, 
                              filters: Optional[Dict[str, Any]] = None,
                              search: Optional[str] = None, 
                              sorts: Optional[Dict[str, Any]] = None,
                              include_cell_details: bool = True):
    """
    Test the get_structured_table_data tool.
    This function calls the tool with the specified parameters to debug its output.
    
    Args:
        table_id (str): The ID of the table to get data from
        max_rows (int, optional): Maximum number of rows to fetch. Defaults to 1.
        filters (Dict, optional): Filters to apply to the query. Defaults to None.
        search (str, optional): Search text to filter results. Defaults to None.
        sorts (Dict, optional): Sort criteria to apply. Defaults to None.
        include_cell_details (bool, optional): Whether to include cell details. Defaults to True.
    """
    print("\n=== TEST: get_structured_table_data ===")
    print(f"Parameters: table_id={table_id}, max_rows={max_rows}")
    if filters:
        print(f"filters={filters}")
    if search:
        print(f"search={search}")
    if sorts:
        print(f"sorts={sorts}")
    print(f"include_cell_details={include_cell_details}")
    

    # Call the tool with the specified parameters using the invoke method
    print("\nCalling tool via invoke:")
    result = get_structured_table_data.invoke({
        "table_id": table_id, 
        "max_rows": max_rows,
        "filters": filters,
        "search": search,
        "sorts": sorts,
        "include_cell_details": include_cell_details
    })
    
    # Unpack the result tuple
    table_data, error = result
    
    # Check if there was an error
    if error:
        print(f"Error: {error}")
        return None, error
    
    # If successful, print the structured data
    print(f"Data type: {type(table_data)}")
    
    if table_data:
        # Pretty print the table data
        print(json.dumps(table_data, indent=2))
    else:
        print("No data returned")
    
    print("=== END TEST ===\n")
    
    return table_data, error

# Run the test if executed directly
if __name__ == "__main__":
    # Try with multiple table IDs to see if any work
    test_ids = [
        "tbl_466afbcbf0792ac0"
    ]
    
    for tid in test_ids:
        print(f"\n\nTrying with table ID: {tid}")
        test_get_structured_table_data(table_id=tid, max_rows=1)
