from src.db.utils import (
    Cell,
    CellDetails,
    download_and_upload_image,
    get_cell,
    get_supabase_client,
    update_cell,
    upsert_cell_details,
    send_realtime_broadcast,
    reimburse_tokens,
    CellId,
    RealtimeEvent,
    CellStatus,
)
from src.schemas.requests import ServiceRequest
import src.services.proxycurl.people.person_profile as person_proxycurl
import src.services.proxycurl.company.company_profile as company_proxycurl
from src.services.proxycurl.company.company_profile import RateLimitException
from ..celery_app import app
import logging
from supabase import AsyncClient
import asyncio
import os
from src.services.llm.generation import _handle_formula_condition

logger = logging.getLogger("src.worker.tasks.proxycurl")


@app.task(
    name="src.worker.tasks.proxycurl.run_proxycurl_task",
    ignore_result=True,
    max_retries=1,
    queue="enrichment",
    rate_limit="280/m",
    bind=True,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_kwargs={"max_retries": 3},
)
def run_proxycurl_task(self, input_dict):
    """
    Celery task that runs the async function using asyncio.

    Args:
        self: The task instance (provided by bind=True)
        input_dict: Dictionary representation of the ServiceRequest
    """
    # Convert dict back to ServiceRequest
    input = ServiceRequest(**input_dict)

    # Run the async function in a new event loop
    try:

        asyncio.run(_run_proxycurl_async(input))
    except Exception as e:
        logger.error(f"Error in run_proxycurl_task: {str(e)}")
        raise


async def _run_proxycurl_async(input: ServiceRequest):
    """
    c    The actual async implementation that gets run by the Celery task
    """
    supabase_client: AsyncClient = await get_supabase_client()

    try:
        cell = await get_cell(
            supabase_client,
            CellId(
                column_id=input.column_id,
                row_id=input.row_id,
                table_id=input.table_id,
            ),
        )
        # Check formula condition
        if not await _handle_formula_condition(input, cell, supabase_client):
            return
        cell.run_status = CellStatus(run="processing", message="Processing...")
        await send_realtime_broadcast(
            supabase_client,
            input.table_id,
            RealtimeEvent.CELL_UPDATE,
            cell.model_dump(),
        )
        await update_cell(supabase_client, cell)

        cell_details = CellDetails(
            table_id=input.table_id,
            column_id=input.column_id,
            row_id=input.row_id,
        )

        match input.service_id:
            case 7 | 16:
                # Run the async function and get both profile data and credit cost
                cell.run_status = CellStatus(
                    run="processing", message="Reading LinkedIn profile..."
                )
                await send_realtime_broadcast(
                    supabase_client,
                    input.table_id,
                    RealtimeEvent.CELL_UPDATE,
                    cell.model_dump(),
                )
                await update_cell(supabase_client, cell)
                if input.service_id == 7:
                    response = await person_proxycurl.get_linkedin_person_profile(
                        input.value
                    )
                elif input.service_id == 16:
                    response = await person_proxycurl.lookup_linkedin_person_profile(
                        input.value
                    )

                # Transform the data into the structured format
                cell_details.value = response.profile.model_dump()
                cell.run_status = CellStatus(
                    run="processing", message="Getting profile image..."
                )
                await send_realtime_broadcast(
                    supabase_client,
                    input.table_id,
                    RealtimeEvent.CELL_UPDATE,
                    cell.model_dump(),
                )
                await update_cell(supabase_client, cell)

                # Download and upload the image
                if response.profile.profile_pic_url:
                    image_path = await download_and_upload_image(
                        supabase=supabase_client,
                        image_url=response.profile.profile_pic_url,
                        bucket=input.organization_id,
                        storage_path=input.table_id,
                    )
                    # Update the cell with the image path
                    cell.extras = {"avatar_url": image_path}

                # Set the cell value to the profile name
                cell.value = response.profile.full_name

            case 8 | 9 | 17:
                # Company profile processing
                cell.run_status = CellStatus(
                    run="processing", message="Reading company LinkedIn profile..."
                )
                await send_realtime_broadcast(
                    supabase_client,
                    input.table_id,
                    RealtimeEvent.CELL_UPDATE,
                    cell.model_dump(),
                )
                await update_cell(supabase_client, cell)
                if input.service_id == 8 or input.service_id == 9:
                    response = await company_proxycurl.get_company_profile(input.value)
                elif input.service_id == 17:
                    response = await company_proxycurl.lookup_company_profile(
                        input.value
                    )

                # Transform the data into the structured format
                cell_details.value = response.profile.model_dump()  
                cell.run_status = CellStatus(
                    run="processing", message="Getting company logo..."
                )
                await send_realtime_broadcast(
                    supabase_client,
                    input.table_id,
                    RealtimeEvent.CELL_UPDATE,
                    cell.model_dump(),
                )
                await update_cell(supabase_client, cell)

                # Download and upload the company logo if available
                if response.profile.profile_pic_url:
                    image_path = await download_and_upload_image(
                        supabase=supabase_client,
                        image_url=response.profile.profile_pic_url,
                        bucket=input.organization_id,
                        storage_path=input.table_id,
                    )
                    # Update the cell with the image path
                    cell.extras = {"avatar_url": image_path}

                # Set the cell value to the company name
                cell.value = response.profile.name

            case _:
                logger.error(f"Unknown service_id: {input.service_id}")
                raise ValueError(f"Unknown service_id: {input.service_id}")
        cell.run_status = CellStatus(run="completed", message="Completed")
        await send_realtime_broadcast(
            supabase_client,
            input.table_id,
            RealtimeEvent.CELL_UPDATE,
            cell.model_dump(),
        )
        await upsert_cell_details(supabase_client, cell_details)
        await update_cell(supabase_client, cell)
    except RateLimitException as e:
        cell.run_status = CellStatus(
            run="failed",
            message="Provider is busy, please try again later.",
        )
        cell.value = None
        cell.extras = None
        await send_realtime_broadcast(
            supabase_client,
            input.table_id,
            RealtimeEvent.CELL_UPDATE,
            cell.model_dump(),
        )
        await update_cell(supabase_client, cell)
        await reimburse_tokens(supabase_client, input.organization_id, input.credits)
        raise

    except Exception as e:
        logger.error(f"Error processing LinkedIn profile: {str(e)}")
        cell.run_status = CellStatus(
            run="failed",
            message="Failed to process LinkedIn profile, please try again later.",
        )
        cell.value = None
        cell.extras = None
        await send_realtime_broadcast(
            supabase_client,
            input.table_id,
            RealtimeEvent.CELL_UPDATE,
            cell.model_dump(),
        )
        await update_cell(supabase_client, cell)
        await reimburse_tokens(supabase_client, input.organization_id, input.credits)
