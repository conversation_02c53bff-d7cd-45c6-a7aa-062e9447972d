from typing import List, Dict, Any, Optional
from fastapi.security import api_key
from pydantic import BaseModel, Field
from src.schemas.linkedin import LinkedinPersonProfileUrl
from .schemas import PersonProfileOutput, PersonProfileResponse, Experience, DateObject
from src.db.utils import get_type_id, get_type_name, format_date
import httpx
from src.core.config import get_settings
import asyncio
import logging
import os

settings = get_settings()

logger = logging.getLogger(__name__)


# Custom exceptions
class RateLimitException(Exception):
    """Exception raised when API rate limit is exceeded"""

    def __init__(self, message: str, retry_after: Optional[str] = None):
        self.message = message
        self.retry_after = retry_after
        super().__init__(self.message)


# Input Model
class PersonProfileInput(BaseModel):
    linkedin_profile_url: LinkedinPersonProfileUrl


class LookupPersonProfileInput(BaseModel):
    company_domain: str
    full_name: str


def transform_linkedin_profile(src: PersonProfileOutput) -> list:
    """Transform LinkedIn profile data into a structured format"""

    result = [
        {
            "name": "LinkedIn Profile",
            "type_id": get_type_id("linkedin_profile"),
            "type": get_type_name("linkedin_profile"),
            "is_brand": True,
            "value": f"{src.first_name} {src.last_name}",
        },
        {
            "name": "Personal Info",
            "type_id": get_type_id("object"),
            "type": get_type_name("object"),
            "is_brand": False,
            "injection_sequence": "1",
            "value": [
                {
                    "name": "Full Name",
                    "type_id": get_type_id("full_name"),
                    "type": get_type_name("full_name"),
                    "is_brand": False,
                    "value": f"{src.first_name} {src.last_name}",
                    "injection_sequence": "1.0",
                },
                {
                    "name": "First Name",
                    "type_id": get_type_id("first_name"),
                    "type": get_type_name("first_name"),
                    "is_brand": False,
                    "value": src.first_name,
                    "injection_sequence": "1.1",
                },
                {
                    "name": "Last Name",
                    "type_id": get_type_id("last_name"),
                    "type": get_type_name("last_name"),
                    "is_brand": False,
                    "value": src.last_name,
                    "injection_sequence": "1.2",
                },
                {
                    "name": "Headline",
                    "type_id": get_type_id("job_title"),
                    "type": get_type_name("job_title"),
                    "is_brand": False,
                    "value": src.headline,
                    "injection_sequence": "1.3",
                },
                {
                    "name": "Summary",
                    "type_id": get_type_id("text"),
                    "type": get_type_name("text"),
                    "is_brand": False,
                    "value": src.summary,
                    "injection_sequence": "1.4",
                },
                {
                    "name": "Country",
                    "type_id": get_type_id("text"),
                    "type": get_type_name("text"),
                    "is_brand": False,
                    "value": src.country_full_name,
                    "injection_sequence": "1.5",
                },
                {
                    "name": "City",
                    "type_id": get_type_id("text"),
                    "type": get_type_name("text"),
                    "is_brand": False,
                    "value": src.city,
                    "injection_sequence": "1.6",
                },
                {
                    "name": "Username",
                    "type_id": get_type_id("text"),
                    "type": get_type_name("text"),
                    "is_brand": False,
                    "value": src.public_identifier,
                    "injection_sequence": "1.7",
                },
            ],
        },
    ]

    # Add Experience section
    experience_items = []
    if src.experiences:
        for i, exp in enumerate(src.experiences):
            experience_items.append(
                {
                    "name": exp.title,
                    "type_id": get_type_id("object"),
                    "type": get_type_name("object"),
                    "is_brand": False,
                    "injection_sequence": f"2.{i}",
                    "value": [
                        {
                            "name": "Job Title",
                            "type_id": get_type_id("job_title"),
                            "type": get_type_name("job_title"),
                            "is_brand": False,
                            "value": exp.title,
                            "injection_sequence": f"2.{i}.0",
                        },
                        {
                            "name": "Company Name",
                            "type_id": get_type_id("company_name"),
                            "type": get_type_name("company_name"),
                            "is_brand": False,
                            "value": exp.company,
                            "injection_sequence": f"2.{i}.1",
                        },
                        {
                            "name": "Company URL",
                            "type_id": get_type_id("linkedin_company_url"),
                            "type": get_type_name("linkedin_company_url"),
                            "is_brand": False,
                            "value": exp.company_linkedin_profile_url,
                            "injection_sequence": f"2.{i}.2",
                        },
                        {
                            "name": "Location",
                            "type_id": get_type_id("text"),
                            "type": get_type_name("text"),
                            "is_brand": False,
                            "value": exp.location,
                            "injection_sequence": f"2.{i}.3",
                        },
                        {
                            "name": "Start Date",
                            "type_id": get_type_id("date"),
                            "type": get_type_name("date"),
                            "is_brand": False,
                            "value": format_date(exp.starts_at if exp.starts_at else None),
                            "injection_sequence": f"2.{i}.4",
                        },
                        {
                            "name": "End Date",
                            "type_id": get_type_id("date"),
                            "type": get_type_name("date"),
                            "is_brand": False,
                            "value": format_date(exp.ends_at if exp.ends_at else None)
                            or "current",
                            "injection_sequence": f"2.{i}.5",
                        },
                    ],
                }
            )

    result.append(
        {
            "name": "Experience",
            "type_id": get_type_id("array"),
            "type": get_type_name("array"),
            "is_brand": False,
            "injection_sequence": "2",
            "value": experience_items,
        }
    )

    # Add Education section
    education_items = []
    if src.education:
        for i, edu in enumerate(src.education):
            education_items.append(
                {
                    "name": edu.school,
                    "type_id": get_type_id("object"),
                    "type": get_type_name("object"),
                    "is_brand": False,
                    "injection_sequence": f"3.{i}",
                    "value": [
                        {
                            "name": "School",
                            "type_id": get_type_id("company_name"),
                            "type": get_type_name("company_name"),
                            "is_brand": False,
                            "value": edu.school,
                            "injection_sequence": f"3.{i}.0",
                        },
                        {
                            "name": "Field of Study",
                            "type_id": get_type_id("text"),
                            "type": get_type_name("text"),
                            "is_brand": False,
                            "value": edu.field_of_study,
                            "injection_sequence": f"3.{i}.1",
                        },
                        {
                            "name": "Degree",
                            "type_id": get_type_id("text"),
                            "type": get_type_name("text"),
                            "is_brand": False,
                            "value": edu.degree_name,
                            "injection_sequence": f"3.{i}.2",
                        },
                        {
                            "name": "Description",
                            "type_id": get_type_id("text"),
                            "type": get_type_name("text"),
                            "is_brand": False,
                            "value": edu.description,
                            "injection_sequence": f"3.{i}.3",
                        },
                        {
                            "name": "School URL",
                            "type_id": get_type_id("linkedin_company_url"),
                            "type": get_type_name("linkedin_company_url"),
                            "is_brand": False,
                            "value": edu.school_linkedin_profile_url,
                            "injection_sequence": f"3.{i}.4",
                        },
                        {
                            "name": "Start Date",
                            "type_id": get_type_id("date"),
                            "type": get_type_name("date"),
                            "is_brand": False,
                            "value": format_date(edu.starts_at if edu.starts_at else None),
                            "injection_sequence": f"3.{i}.5",
                        },
                        {
                            "name": "End Date",
                            "type_id": get_type_id("date"),
                            "type": get_type_name("date"),
                            "is_brand": False,
                            "value": format_date(edu.ends_at if edu.ends_at else None)
                            or "current",
                            "injection_sequence": f"3.{i}.6",
                        },
                    ],
                }
            )

    result.append(
        {
            "name": "Education",
            "type_id": get_type_id("array"),
            "type": get_type_name("array"),
            "is_brand": False,
            "injection_sequence": "3",
            "value": education_items,
        }
    )

    # Add Certifications section if available
    if src.certifications:
        certification_items = []
        for i, cert in enumerate(src.certifications):
            certification_items.append(
                {
                    "name": cert.name,
                    "type_id": get_type_id("object"),
                    "type": get_type_name("object"),
                    "is_brand": False,
                    "injection_sequence": f"4.{i}",
                    "value": [
                        {
                            "name": "Name",
                            "type_id": get_type_id("text"),
                            "type": get_type_name("text"),
                            "is_brand": False,
                            "value": cert.name,
                            "injection_sequence": f"4.{i}.0",
                        },
                        {
                            "name": "Authority",
                            "type_id": get_type_id("text"),
                            "type": get_type_name("text"),
                            "is_brand": False,
                            "value": cert.authority,
                            "injection_sequence": f"4.{i}.1",
                        },
                        {
                            "name": "Start Date",
                            "type_id": get_type_id("date"),
                            "type": get_type_name("date"),
                            "is_brand": False,
                            "value": format_date(
                                cert.starts_at if cert.starts_at else None
                            ),
                            "injection_sequence": f"4.{i}.2",
                        },
                        {
                            "name": "End Date",
                            "type_id": get_type_id("date"),
                            "type": get_type_name("date"),
                            "is_brand": False,
                            "value": format_date(cert.ends_at if cert.ends_at else None)
                            or "current",
                            "injection_sequence": f"4.{i}.3",
                        },
                    ],
                }
            )

        result.append(
            {
                "name": "Certifications",
                "type_id": get_type_id("array"),
                "type": get_type_name("array"),
                "is_brand": False,
                "injection_sequence": "4",
                "value": certification_items,
            }
        )

    return result


async def get_linkedin_person_profile(
    input: PersonProfileInput,
) -> PersonProfileResponse:
    """Fetch LinkedIn person profile data from the ProxyCurl API

    Returns:
        tuple: (PersonProfileOutput, credit_cost)
    """
    try:
        if settings.IS_STRESS_TESTING:
            # wait for 30 seconds
            await asyncio.sleep(30)
            return PersonProfileResponse(
                profile=PersonProfileOutput(
                    profile_pic_url=settings.TESTING_LINKEDIN_PROFILE_PICTURE_URL,
                    first_name="Abudi",
                    last_name="Mohamed",
                    full_name="Abudi Mohamed",
                    headline="CTO",
                    summary="CTO of Outbond",
                    country_full_name="Germany",
                    city="Berlin",
                    public_identifier="abudimohamed",
                    experiences=[
                        Experience(
                            title="CTO",
                            company="Outbond",
                            company_linkedin_profile_url="https://www.linkedin.com/company/outbond",
                            location="Berlin",
                            starts_at=DateObject(year=2020, month=1, day=1),
                            ends_at=DateObject(year=2024, month=1, day=1)
                        )
                    ],
                    education=[],
                    certifications=[]
                ),
                credit_cost=0,
            )
        async with httpx.AsyncClient() as client:

            response = await client.get(
                "https://nubela.co/proxycurl/api/v2/linkedin",
                params={
                    "linkedin_profile_url": input.linkedin_profile_url,
                    "use_cache": "if-recent",
                    "fallback_to_cache": "on-error",
                },
                headers={
                    "Authorization": f"Bearer {settings.PROXYCURL_API_KEY.get_secret_value()}"
                },
                timeout=60.0,  # 60 second timeout
            )
            response.raise_for_status()

            # Extract credit cost from headers
            credit_cost = 0
            if "X-Proxycurl-Credit-Cost" in response.headers:
                try:
                    credit_cost = int(response.headers["X-Proxycurl-Credit-Cost"])
                except (ValueError, TypeError):
                    # If header exists but can't be converted to int
                    print(
                        f"Warning: Could not parse credit cost from header: {response.headers.get('X-Proxycurl-Credit-Cost')}"
                    )

            return PersonProfileResponse(
                profile=PersonProfileOutput(**response.json()), credit_cost=credit_cost
            )
    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            print("Not Found")
            raise ValueError("Profile not found")
        elif e.response.status_code == 429:
            print("Rate Limit")
            # Extract retry-after header if available
            retry_after = e.response.headers.get("Retry-After")
            raise RateLimitException(
                retry_after=retry_after, message="Rate limit exceeded. Try again later."
            )
        else:
            print(f"HTTP Error: {e.response.status_code}")
            raise e
    except Exception as e:
        print(f"Exception: {str(e)}")
        raise e


async def lookup_linkedin_person_profile(
    input: LookupPersonProfileInput,
) -> PersonProfileResponse:
    """Fetch LinkedIn person profile data from the ProxyCurl API

    Returns:
        tuple: (PersonProfileOutput, credit_cost)
    """
    try:
        if settings.IS_STRESS_TESTING:
            # wait for 30 seconds
            await asyncio.sleep(30)
            return PersonProfileResponse(
                profile=PersonProfileOutput(
                    profile_pic_url=settings.TESTING_LINKEDIN_PROFILE_PICTURE_URL,
                    first_name="Abudi",
                    last_name="Mohamed",
                    full_name="Abudi Mohamed",
                    headline="CTO",
                    summary="CTO of Outbond",
                    country_full_name="Germany",
                    city="Berlin",
                    public_identifier="abudimohamed",
                    experiences=[
                        Experience(
                            title="CTO",
                            company="Outbond",
                            company_linkedin_profile_url="https://www.linkedin.com/company/outbond",
                            location="Berlin",
                            starts_at=DateObject(year=2020, month=1, day=1),
                            ends_at=DateObject(year=2024, month=1, day=1)
                        )
                    ],
                    education=[],
                    certifications=[]
                ),
                credit_cost=0,
            )
        async with httpx.AsyncClient() as client:

            response = await client.get(
                "https://nubela.co/proxycurl/api/linkedin/profile/resolve",
                params={
                    "company_domain": input.company_domain,
                    "first_name": input.full_name.split(" ")[0],
                    "last_name": input.full_name.split(" ")[1],
                    "enrich_profile": "enrich",
                },
                headers={
                    "Authorization": f"Bearer {settings.PROXYCURL_API_KEY.get_secret_value()}"
                },
                timeout=60.0,  # 60 second timeout
            )
            response.raise_for_status()

            # Extract credit cost from headers
            credit_cost = 0
            if "X-Proxycurl-Credit-Cost" in response.headers:
                try:
                    credit_cost = int(response.headers["X-Proxycurl-Credit-Cost"])
                except (ValueError, TypeError):
                    # If header exists but can't be converted to int
                    logger.warning(
                        f"Warning: Could not parse credit cost from header: {response.headers.get('X-Proxycurl-Credit-Cost')}"
                    )
            output = response.json()
            return PersonProfileResponse(
                profile=PersonProfileOutput(**output["profile"]),
                credit_cost=credit_cost,
            )
    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            print("Not Found")
            raise ValueError("Profile not found")
        elif e.response.status_code == 429:
            print("Rate Limit")
            # Extract retry-after header if available
            retry_after = e.response.headers.get("Retry-After")
            raise RateLimitException(
                retry_after=retry_after, message="Rate limit exceeded. Try again later."
            )
        else:
            print(f"HTTP Error: {e.response.status_code}")
            raise e
    except Exception as e:
        print(f"Exception: {str(e)}")
        raise e
