"""Pydantic models for database operations."""

from typing import Dict, List, Optional, Union, Any
from pydantic import BaseModel, Field, ConfigDict
from enum import Enum


class Operator(str, Enum):
    """Filter operators for table data queries."""
    EQ = "eq"
    """Equal to"""
    
    NEQ = "neq"
    """Not equal to"""
    
    LT = "lt"
    """Less than"""
    
    LTE = "lte"
    """Less than or equal to"""
    
    GT = "gt"
    """Greater than"""
    
    GTE = "gte"
    """Greater than or equal to"""
    
    CONTAINS = "contains"
    """Contains substring"""
    
    NCONTAINS = "ncontains"
    """Does not contain substring"""
    
    EMPTY = "empty"
    """Is empty"""
    
    NEMPTY = "nempty"
    """Is not empty"""
    
    ERROR = "error"
    """Has error from a runnable column"""
    
    NERROR = "nerror"
    """Does not have error from a runnable column"""
    
    RESULT = "result"
    """Has result from a runnable column"""
    
    NRESULT = "nresult"
    """Does not have result from a runnable column"""
    
    RUN = "run"
    """Is running"""
    
    NRUN = "nrun"
    """Has not run"""
    
    AWAITING_INPUT = "awaiting_input"
    """Awaiting input"""
    
    QUEUED = "queued"
    """Is queued"""
    
    FAILED = "failed"
    """Has failed"""

    COMPLETED = "completed"
    """Has completed"""


class GroupOperator(str, Enum):
    """Operators for combining filter rules."""
    AND = "AND"
    """All conditions must be true"""
    
    OR = "OR"
    """At least one condition must be true"""


class SortDirection(str, Enum):
    """Sort direction options."""
    ASC = "asc"
    """Ascending order"""
    
    DESC = "desc"
    """Descending order"""


class Filter(BaseModel):
    """Filter model for table data queries."""
    model_config = ConfigDict(use_enum_values=True)
    
    column_name: str = Field(
        ..., 
        description="Name of the column to filter."
    )
    operator: Operator = Field(
        ..., 
        description="Filter operator such as 'eq', 'neq', 'lt', 'lte', etc."
    )
    value: Optional[str] = Field(
        None, 
        description="Value to compare against. Not required for some operators like 'empty'."
    )


class FilterGroup(BaseModel):
    """Group of filters that can be combined with AND/OR operators.
    
    Supports nested filter groups for complex query conditions.
    """
    model_config = ConfigDict(use_enum_values=True)
    
    operator: GroupOperator = Field(
        ..., 
        description="Logical operator ('AND' or 'OR') to combine the rules in this group."
    )
    rules: List[Union['Filter', 'FilterGroup']] = Field(
        ..., 
        min_length=1,
        description="List of filters or nested filter groups to apply. Must contain at least one rule. For nested groups, each can have its own operator."
    )


class Sort(BaseModel):
    """Sort model for table data queries."""
    model_config = ConfigDict(use_enum_values=True)
    
    column_name: str = Field(
        ..., 
        description="Name of the column to sort by."
    )
    direction: SortDirection = Field(
        ..., 
        description="Sort direction, either 'asc' (ascending) or 'desc' (descending)."
    )


class TableDataRequest(BaseModel):
    """Request model for table data retrieval.
    
    This model defines the structure for requesting table data with comprehensive
    validation and documentation following Google style guidelines.
    """
    model_config = ConfigDict(use_enum_values=True)
    
    table_id: str = Field(
        ...,
        description="The unique identifier of the table to retrieve data from",
        examples=["tbl_c85117eb4106d464"]
    )
    
    max_rows: int = Field(
        default=5,
        description="Maximum number of rows to return from the table",
        ge=1,
        le=100,
        examples=[5, 10, 25]
    )
    
    filters: Optional[FilterGroup] = Field(
        default=None,
        description="Optional filtering conditions to apply to the query using FilterGroup model"
    )
    
    search: Optional[str] = Field(
        default=None,
        description="Optional search text to filter results across all columns",
        max_length=500,
        examples=["john doe", "google.com", "engineer"]
    )
    
    sorts: Optional[List[Sort]] = Field(
        default=None,
        description="Optional sort criteria to apply using Sort model",
        max_length=10
    )
    
    row: bool = Field(
        default=False,
        description="Boolean flag to control data format. When False (default), returns summarized column schema. When True, returns full row data."
    )
    
    column_names: Optional[List[str]] = Field(
        default=None,
        description="Optional list of specific column names to retrieve. If None, retrieves all columns.",
        examples=[["name", "email", "company"], ["linkedin_url"]]
    )
    
    apply_table_filters: bool = Field(
        default=True,
        description="Whether to apply table-level filters defined in the table configuration."
    )
    
    apply_table_sorts: bool = Field(
        default=True,
        description="Whether to apply table-level sorts defined in the table configuration."
    )
    
    include_summary: bool = Field(
        default=True,
        description="Whether to include LLM-generated summary of the table data. When True, calls LLM to summarize the data. When False, skips LLM summary and returns raw data."
    )


class TableDataResponse(BaseModel):
    """Response model for table data retrieval.
    
    This model defines the structure of successful table data responses,
    providing clear typing and documentation.
    """
    model_config = ConfigDict(use_enum_values=True)
    
    columns: List[Dict[str, Any]] = Field(
        description="The retrieved table data as a list of columns"
    )
    
    total_rows: int = Field(
        description="Total number of rows returned in this response"
    )
    
    success: bool = Field(
        default=True,
        description="Indicates if the operation was successful"
    )
    
    error_message: Optional[str] = Field(
        default=None,
        description="Error message if the operation failed"
    )

