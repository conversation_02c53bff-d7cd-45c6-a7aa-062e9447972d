import json
import time
import logging
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, BackgroundTasks
from typing import Dict, Any

# Configure logging
logger = logging.getLogger("backend_api.server")

async def get_table_row_ids_handler(
    request: Dict[str, Any],
    redis: Any,
    pg_pool: Any,
    background_tasks: BackgroundTasks
):
    try:
        start_time = time.time()

        # Define TTL in seconds (15 minutes)
        CACHE_TTL = 15 * 60
        
        # Create exact filter structure
        filters_dict = None
        if request.get("p_filters"):
            filters_dict = {
                "operator": request["p_filters"]["operator"],
                "rules": [
                    {
                        "column_id": rule["column_id"],
                        "operator": rule["operator"],
                        "value": rule["value"]
                    }
                    for rule in request["p_filters"]["rules"]
                ]
            }
        
        # Create cache key
        cache_key = f"table_rows:{request['p_table_id']}"
        if filters_dict:
            cache_key += f":filters:{hash(json.dumps(filters_dict, sort_keys=True))}"
        if request.get("p_search"):
            cache_key += f":search:{request['p_search']}"
        
        
        # Try to get from cache first
        cached_result = await redis.get(cache_key)
        if cached_result:
            result = json.loads(cached_result)
            
            logger.info(
                f"Cache HIT for {cache_key}",
                extra={
                    "cache_hit": True,
                    "table_id": request["p_table_id"]
                }
            )
            
            # Reset TTL on cache hit to implement sliding window expiration
            await redis.expire(cache_key, CACHE_TTL)
            
            return {
                "row_ids": result['row_ids'],
                "total_count": result['total_count'],
                "from_cache": True
            }
        
        logger.info(
            f"Cache MISS for {cache_key}",
            extra={
                "cache_hit": False,
                "table_id": request["p_table_id"]
            }
        )
        
        # Convert to exact JSON string for PostgreSQL
        pg_filters = json.dumps(filters_dict, separators=(',', ':')) if filters_dict else None
        
        async with pg_pool.acquire() as conn:
            rows = await conn.fetch(
                """
                SELECT * FROM get_table_row_ids(
                    p_table_id => $1::text,
                    p_filters => $2::jsonb,
                    p_search => $3::text,
                    p_sorts => $4::jsonb
                );
                """,
                request["p_table_id"],
                pg_filters,
                request.get("p_search"),
                None
            )
            
            if not rows:
                return {
                    "row_ids": [],
                    "total_count": 0,
                    "from_cache": False
                }
            
            # Process the result
            result = rows[0]['get_table_row_ids']
            if isinstance(result, str):
                result = json.loads(result)
            
            # Format the result
            formatted_result = {
                "row_ids": result if isinstance(result, list) else result['row_ids'],
                "total_count": len(result if isinstance(result, list) else result['row_ids']),
                "from_cache": False
            }
            
            # Add Redis storage to background tasks
            async def store_in_redis():
                try:
                    await redis.set(
                        cache_key,
                        json.dumps({
                            "row_ids": formatted_result["row_ids"],
                            "total_count": formatted_result["total_count"]
                        }),
                        ex=CACHE_TTL  # 15 minutes TTL
                    )
                except Exception as cache_error:
                    logger.error(f"Cache storage failed: {str(cache_error)}")
            
            background_tasks.add_task(store_in_redis)
            
            # Log request completion
            end_time = time.time()
            latency_ms = (end_time - start_time) * 1000
            logger.info(
                f"POST /table/row-ids {200} {latency_ms:.0f}ms",
                extra={
                    "method": "POST",
                    "path": "/table/row-ids",
                    "status": 200,
                    "latency_ms": latency_ms
                }
            )
            
            return formatted_result

    except Exception as e:
        logger.error(f"Error in get_table_row_ids: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))