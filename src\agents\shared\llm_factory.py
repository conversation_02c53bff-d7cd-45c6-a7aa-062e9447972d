"""Utility & helper functions."""
import asyncio
from enum import Enum
import os
import json
import asyncio
from langchain_core.language_models import BaseChatModel
from langchain_core.messages import BaseMessage, AIMessage, SystemMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from typing import Any, Dict, List, Union

from langchain_openai import ChatOpenA<PERSON>
from langchain_aws import ChatBedrock
from pydantic import BaseModel



#######################################################
#                      LLM
#######################################################
class GPT5Models(str, Enum):
    GPT_5 = "gpt-5"
    GPT_5_MINI = "gpt-5-mini"
    GPT_5_NANO = "gpt-5-nano"

class BedrockModels(str, Enum):
    CLAUDE_3_7_SONNET = "us.anthropic.claude-3-7-sonnet-20250219-v1:0"
    CLAUDE_4_SONNET = "anthropic.claude-sonnet-4-20250514-v1:0"
# Claude Opus 4	anthropic.claude-opus-4-20250514-v1:0
# Claude Sonnet 4	anthropic.claude-sonnet-4-20250514-v1:0
# Claude 3.7 Sonnet	us.anthropic.claude-3-7-sonnet-20250219-v1:0
class ReasoninEffort(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    NONE = "none"

class OpenAIGpt5LlmModel(BaseModel):
    model: GPT5Models = GPT5Models.GPT_5_MINI
    reasoning_effort: ReasoninEffort = ReasoninEffort.MEDIUM

class BedrockLlmModel(BaseModel):
    model: BedrockModels = BedrockModels.CLAUDE_3_7_SONNET
    max_tokens: int = 131072
    temperature: float = 0.1
    thinking_enabled: bool = False
    thinking_budget_tokens: int = 4000

def llm_factory(model: Union[OpenAIGpt5LlmModel, BedrockLlmModel, None] = None) -> BaseChatModel:
    """Factory function to get an LLM model based on the configuration.
    Supports both OpenAI GPT-5 and AWS Bedrock models.
    Defaults to GPT5Models.GPT_5_MINI with ReasoninEffort.MEDIUM if not specified.
    """
    if model is None:
        model = OpenAIGpt5LlmModel()

    if isinstance(model, OpenAIGpt5LlmModel):
        kwargs = {"model": model.model.value}
        if model.reasoning_effort != ReasoninEffort.NONE:
            kwargs["reasoning"] = {"effort": model.reasoning_effort.value}
        return ChatOpenAI(**kwargs)

    elif isinstance(model, BedrockLlmModel):
        kwargs = {
            "model": model.model.value,
            "model_kwargs": {
                "max_tokens": model.max_tokens,
                "temperature": model.temperature
            }
        }

        if model.thinking_enabled:
            kwargs["additional_model_request_fields"] = {
                "thinking": {"type": "enabled", "budget_tokens": model.thinking_budget_tokens}
            }

        return ChatBedrock(**kwargs)

    else:
        raise ValueError(f"Unsupported model type: {type(model)}")

async def llm_factory_async(model: Union[OpenAIGpt5LlmModel, BedrockLlmModel, None] = None) -> BaseChatModel:
    """Create the LLM instance without blocking the event loop.

    Why: ChatBedrock/ChatOpenAI constructors may perform blocking I/O (e.g.,
    AWS credential/config file reads or metadata service calls via botocore).
    In ASGI contexts this must not run on the main event loop, so we offload
    the synchronous factory to a thread.
    """
    return await asyncio.to_thread(llm_factory, model)