from .leadmagic import run_leadmagic_task
from .prospeo import run_prospeo_task

# Define a dictionary mapping provider names to their implementations
phone_finder_map = {
    "leadmagic": run_leadmagic_task,
    "prospeo": run_prospeo_task,
}


def get_phone_finder(provider: str):
    """Get the appropriate phone finder function based on the provider name

    Args:
        provider: The name of the provider to use

    Returns:
        The phone finder function for the specified provider, or leadmagic as fallback
    """
    return phone_finder_map.get(provider, run_leadmagic_task)
