"""High-performance table data retrieval tool for AI agents.

This module provides enterprise-grade table data access with advanced filtering,
sorting, and schema analysis capabilities. It integrates with Supabase backend
and supports both full data retrieval and schema-only analysis modes.

Key features:
    - Comprehensive input validation with clear error messages
    - Advanced filtering with nested AND/OR logic support
    - Multi-column sorting capabilities
    - Schema extraction with AI-powered summarization
    - Robust error handling and logging for production environments
    - Type-safe operations with full annotations

Typical usage:
    # Get schema overview (default)
    response = read_table_data_tool("tbl_12345", max_rows=10, row=False)
    
    # Get full row data
    response = read_table_data_tool("tbl_12345", max_rows=5, row=True)
    
    # With advanced filtering
    filters = FilterGroup(...)
    response = read_table_data_tool("tbl_12345", filters=filters)
"""

import logging
import traceback
from typing import Any, Dict, List, Optional
from typing import Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg
from ...configuration import Configuration
from langchain_core.tools import tool
from pydantic import ValidationError

from ...supabase.read_table.models import (
    FilterGroup,
    Sort,
    TableDataRequest,
    TableDataResponse,
)
from ...supabase.read_table.read_table import get_table_data

from .summerize import extract_column_schema_with_summary, extract_column_schema


# Constants for validation and configuration
_MIN_ROWS = 1
_MAX_ROWS = 10
_DEFAULT_ROWS = 5
_TABLE_ID_PREFIX = "tbl_"
_MIN_TABLE_ID_LENGTH = 8

# Configure module logger
logger = logging.getLogger(__name__)


class TableDataToolError(Exception):
    """Base exception for table data tool operations."""
    pass


class InvalidTableIdError(TableDataToolError):
    """Raised when table ID format is invalid."""
    pass


class DataRetrievalError(TableDataToolError):
    """Raised when data retrieval from backend fails."""
    pass


def _validate_table_id(table_id: str) -> None:
    """Validate table ID format and structure.
    
    Args:
        table_id: The table identifier to validate.
        
    Raises:
        InvalidTableIdError: If table ID format is invalid.
    """
    if not isinstance(table_id, str):
        raise InvalidTableIdError(f"Table ID must be a string, got {type(table_id)}")
    
    if not table_id:
        raise InvalidTableIdError("Table ID cannot be empty")
    
    if not table_id.startswith(_TABLE_ID_PREFIX):
        raise InvalidTableIdError(
            f"Table ID must start with '{_TABLE_ID_PREFIX}', got: {table_id}"
        )
    
    if len(table_id) < _MIN_TABLE_ID_LENGTH:
        raise InvalidTableIdError(
            f"Table ID too short, minimum length: {_MIN_TABLE_ID_LENGTH}, got: {len(table_id)}"
        )


def _validate_max_rows(max_rows: int) -> None:
    """Validate max_rows parameter is within acceptable range.
    
    Args:
        max_rows: The maximum number of rows to validate.
        
    Raises:
        ValueError: If max_rows is outside valid range.
    """
    if not isinstance(max_rows, int):
        raise ValueError(f"max_rows must be an integer, got {type(max_rows)}")
    
    if max_rows < _MIN_ROWS:
        raise ValueError(f"max_rows must be at least {_MIN_ROWS}, got: {max_rows}")
    
    if max_rows > _MAX_ROWS:
        raise ValueError(f"max_rows cannot exceed {_MAX_ROWS}, got: {max_rows}")


def _create_error_response(error_message: str, log_message: Optional[str] = None) -> TableDataResponse:
    """Create standardized error response.
    
    Args:
        error_message: User-facing error message.
        log_message: Optional detailed message for logging.
        
    Returns:
        TableDataResponse with error information.
    """
    if log_message:
        logger.error(log_message)
    else:
        logger.error(error_message)
    
    return TableDataResponse(
        columns=[],
        total_rows=0,
        success=False,
        error_message=error_message
    )


def _process_table_data(
    table_data: Dict[str, Any],
    request: TableDataRequest,
    config: RunnableConfig,
) -> TableDataResponse:
    """Process raw table data into structured response.
    
    Args:
        table_data: Raw data from backend.
        request: Original request parameters.
        
    Returns:
        Processed TableDataResponse.
        
    Raises:
        DataRetrievalError: If data processing fails.
    """
    try:
        columns_data = table_data.get("columns", [])
        total_rows = table_data.get("rows_count", 0)
        
        # Apply schema extraction for schema-only requests
        if not request.row and columns_data:
            try:
                if request.include_summary:
                    columns_data = extract_column_schema_with_summary(columns_data, config=config)
                    logger.debug("Applied schema extraction with summarization to %d columns", len(columns_data))
                else:
                    columns_data = extract_column_schema(columns_data, config=config)
                    logger.debug("Applied schema extraction without summarization to %d columns", len(columns_data))
            except Exception as e:
                logger.warning(
                    "Schema extraction failed, returning raw data: %s", str(e)
                )
                # Continue with raw data if schema extraction fails
        
        response = TableDataResponse(
            columns=columns_data,
            total_rows=total_rows,
            success=True,
            error_message=None
        )
        
        logger.info(
            "Successfully processed table data: %d columns, %d total rows",
            len(columns_data), total_rows
        )
        
        return response
        
    except Exception as e:
        raise DataRetrievalError(f"Failed to process table data: {str(e)}") from e


@tool
def read_table_data_tool(
    config: Annotated[RunnableConfig, InjectedToolArg],
    request: TableDataRequest,
) -> TableDataResponse:
    """Read data from a specified table with comprehensive filtering and sorting options.
    
    This tool provides enterprise-grade table data access with advanced filtering,
    searching, sorting, and schema analysis capabilities. It supports both full
    data retrieval and schema-only modes for optimal performance.
    
    Args:
        config: Runnable configuration containing table_id and other settings.
        request: TableDataRequest model containing all query parameters:
            - table_id: Will be set from config, unique identifier in format 'tbl_xxxxx'
            - max_rows: Maximum number of rows to return (1-100, default: 5)
            - filters: Optional FilterGroup for advanced filtering with AND/OR logic
            - search: Optional global search text for substring matching
            - sorts: Optional list of Sort criteria for multi-column sorting
            - row: Data format flag (False=schema, True=full row data)
            - column_names: Optional list of specific columns to retrieve
            - apply_table_filters: Whether to apply table-level filters (default: True)
            - apply_table_sorts: Whether to apply table-level sorts (default: True)
            - include_summary: Whether to include LLM-generated summary in schema mode (default: True)
    
    Returns:
        TableDataResponse: Structured response containing:
            - columns: List of column data or schema information
            - total_rows: Total number of rows matching criteria
            - success: Boolean indicating operation success
            - error_message: Detailed error information if operation failed
    
    Raises:
        ValidationError: When input parameters fail validation checks.
        
    Examples:
        # Basic schema retrieval
        >>> request = TableDataRequest(table_id="", max_rows=5)
        >>> response = read_table_data_tool(config, request)
        >>> print(f"Found {response.total_rows} rows, {len(response.columns)} columns")
        
        # Full data with filtering
        >>> filters = FilterGroup(operator=GroupOperator.AND, rules=[...])
        >>> request = TableDataRequest(table_id="", max_rows=10, filters=filters, row=True)
        >>> response = read_table_data_tool(config, request)
        
        # Search with sorting
        >>> sorts = [Sort(column_name="name", direction=SortDirection.ASC)]
        >>> request = TableDataRequest(table_id="", search="john", sorts=sorts)
        >>> response = read_table_data_tool(config, request)
        
        # Schema without LLM summary (faster response)
        >>> request = TableDataRequest(table_id="", max_rows=5, include_summary=False)
        >>> response = read_table_data_tool(config, request)
    """
    # Comprehensive input validation
    try:
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
       
        _validate_table_id(table_id)
        _validate_max_rows(request.max_rows)
        
        # Set table_id from config on the request
        request.table_id = table_id
        
    except ValidationError as e:
        table_id_str = getattr(request, 'table_id', 'unknown') if 'request' in locals() else 'unknown'
        return _create_error_response(
            f"Parameter validation failed: {str(e)}",
            f"Pydantic validation error for table {table_id_str}: {str(e)}"
        )
    except (InvalidTableIdError, ValueError) as e:
        table_id_str = getattr(request, 'table_id', 'unknown') if 'request' in locals() else 'unknown'
        return _create_error_response(
            f"Invalid input parameters: {str(e)}",
            f"Input validation failed for table {table_id_str}: {str(e)}"
        )
    except Exception as e:
        table_id_str = getattr(request, 'table_id', 'unknown') if 'request' in locals() else 'unknown'
        return _create_error_response(
            "Invalid request parameters",
            f"Unexpected validation error for table {table_id_str}: {str(e)}"
        )
    
    # Log request details for monitoring
    logger.info(
        "Processing table data request - table_id=%s, max_rows=%d, row_mode=%s, "
        "has_filters=%s, has_search=%s, has_sorts=%s, include_summary=%s",
        request.table_id, request.max_rows, request.row,
        bool(request.filters), bool(request.search), bool(request.sorts), request.include_summary
    )
    
    # Execute backend data retrieval
    try:
        table_data, error = get_table_data(
            config=config,
            request=request
        )
        
        # Handle backend errors
        if error:
            return _create_error_response(
                f"Failed to retrieve table data: {error}",
                f"Backend error for table {request.table_id}: {error}"
            )
        
        # Handle empty response
        if table_data is None:
            return _create_error_response(
                "No data available for the specified table and criteria",
                f"Empty response from backend for table {request.table_id}"
            )
        
        # Process successful response
        response = _process_table_data(table_data, request, config)
        
        logger.info(
            "Successfully completed table data request for %s: %d rows retrieved",
            request.table_id, response.total_rows
        )
        
        return response
        
    except DataRetrievalError as e:
        return _create_error_response(str(e))
    except Exception as e:
        # Catch-all for unexpected errors
        error_trace = traceback.format_exc()
        logger.error(
            "Unexpected error during table data retrieval for %s: %s\nTrace: %s",
            request.table_id, str(e), error_trace
        )
        
        return _create_error_response(
            "An unexpected error occurred while retrieving table data",
            f"Critical error for table {request.table_id}: {str(e)}"
        )


# Public API exports
__all__ = [
    "read_table_data_tool",
    "TableDataRequest",
    "TableDataResponse",
    "TableDataToolError",
    "InvalidTableIdError", 
    "DataRetrievalError",
]
