"""
Task-aware agent utilities for integrating with the enhanced supervisor system.

This module provides utilities for agents to work seamlessly with the task-based
execution system, including task context awareness and completion reporting.
"""

from typing import Dict, Any, Optional
from langchain_core.messages import AIMessage, HumanMessage
import logging


def extract_task_context(state: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Extract current task context from state for task-aware agents.
    
    Args:
        state: Current agent state
        
    Returns:
        Dict containing task context or None if no active task
    """
    active_task_id = state.get("active_task_id")
    if not active_task_id:
        return None
    
    plan_tasks = state.get("plan_tasks", [])
    if not plan_tasks:
        return None
    
    # Find the active task
    for task in plan_tasks:
        task_dict = task.model_dump() if hasattr(task, 'model_dump') else task
        if task_dict.get('id') == active_task_id:
            return {
                'task_id': task_dict.get('id'),
                'action': task_dict.get('action'),
                'tool': task_dict.get('tool'),
                'why': task_dict.get('why'),
                'agent': task_dict.get('agent'),
                'order': task_dict.get('order'),
                'status': task_dict.get('status')
            }
    
    return None


def create_task_aware_prompt(base_prompt: str, task_context: Optional[Dict[str, Any]] = None) -> str:
    """
    Enhance agent prompt with task context awareness.
    
    Args:
        base_prompt: Original agent system prompt
        task_context: Current task context from extract_task_context()
        
    Returns:
        Enhanced prompt with task context
    """
    if not task_context:
        return base_prompt
    
    task_section = f"""

**CURRENT TASK ASSIGNMENT:**

🎯 **Task ID:** {task_context['task_id']}
📋 **Action:** {task_context['action']}
🔧 **Required Tool:** {task_context['tool']}
💡 **Objective:** {task_context['why']}
📊 **Order:** {task_context['order']}

**TASK-SPECIFIC INSTRUCTIONS:**
- Focus specifically on the assigned action: "{task_context['action']}"
- Use the required tool: "{task_context['tool']}"
- Keep the objective in mind: "{task_context['why']}"
- Provide clear, actionable results that advance this specific task
- When task is complete, clearly indicate completion in your response

"""
    
    return base_prompt + task_section


def create_task_completion_message(task_context: Dict[str, Any], results: str) -> str:
    """
    Create a standardized task completion message.
    
    Args:
        task_context: Task context from extract_task_context()
        results: Results of task execution
        
    Returns:
        Formatted completion message
    """
    return f"""✅ **TASK COMPLETED**

**Task ID:** {task_context['task_id']}
**Action:** {task_context['action']}
**Status:** Completed Successfully

**Results:**
{results}

**Next Steps:** Returning to supervisor for next task assignment.
"""


def create_task_failure_message(task_context: Dict[str, Any], error: str) -> str:
    """
    Create a standardized task failure message.
    
    Args:
        task_context: Task context from extract_task_context()
        error: Error description
        
    Returns:
        Formatted failure message
    """
    return f"""❌ **TASK FAILED**

**Task ID:** {task_context['task_id']}
**Action:** {task_context['action']}
**Status:** Failed

**Error:**
{error}

**Next Steps:** Returning to supervisor for error handling.
"""


def create_task_clarification_message(task_context: Dict[str, Any], clarification_request: str) -> str:
    """
    Create a standardized task clarification message.
    
    Args:
        task_context: Task context from extract_task_context()
        clarification_request: The clarification being requested
        
    Returns:
        Formatted clarification message
    """
    return f"""❓ **TASK NEEDS CLARIFICATION**

**Task ID:** {task_context['task_id']}
**Action:** {task_context['action']}
**Status:** Awaiting User Input

**Clarification Needed:**
{clarification_request}

**Next Steps:** Waiting for user response to proceed with task execution.
"""


def detect_agent_status_from_content(content: str) -> str:
    """
    Detect agent status from message content.
    
    Args:
        content: Agent's response content
        
    Returns:
        Status: 'completed', 'needs_clarification', or 'failed'
    """
    content_lower = content.lower()
    
    # Check for clarification indicators
    clarification_indicators = [
        'need', 'require', 'clarification', 'information', 
        'could you', 'please confirm', 'which', 'how many',
        'what', 'specify', 'details'
    ]
    
    if any(indicator in content_lower for indicator in clarification_indicators):
        return 'needs_clarification'
    
    # Check for completion indicators
    completion_indicators = ['completed', 'finished', 'done', 'success']
    if any(indicator in content_lower for indicator in completion_indicators):
        return 'completed'
    
    # Check for failure indicators
    failure_indicators = ['error', 'failed', 'cannot', 'unable']
    if any(indicator in content_lower for indicator in failure_indicators):
        return 'failed'
    
    return 'completed'  # Default assumption


def wrap_agent_with_task_awareness(agent_function):
    """
    Decorator to wrap an existing agent function with task awareness.
    
    Args:
        agent_function: Original agent function to wrap
        
    Returns:
        Task-aware version of the agent function
    """
    def task_aware_agent(state, config):
        """Task-aware wrapper for agent functions."""
        try:
            # Extract task context
            task_context = extract_task_context(state)
            
            if task_context:
                logging.info(f"Agent executing task: {task_context['task_id']} - {task_context['action']}")
                
                # Add task context to state for agent awareness
                enhanced_state = state.copy()
                enhanced_state['current_task_context'] = task_context
                
                # Execute the original agent function
                result = agent_function(enhanced_state, config)
                
                # Process agent response based on content
                if isinstance(result, dict) and 'messages' in result:
                    last_message = result['messages'][-1] if result['messages'] else None
                    if last_message and hasattr(last_message, 'content'):
                        
                        # Detect agent status from response
                        agent_status = detect_agent_status_from_content(last_message.content)
                        
                        if agent_status == 'needs_clarification':
                            # Agent needs clarification
                            clarification_msg = create_task_clarification_message(
                                task_context, 
                                last_message.content
                            )
                            result['messages'][-1] = AIMessage(content=clarification_msg)
                            result['sender_agent_name'] = task_context['agent']
                            result['sender_message'] = last_message.content
                            
                        elif agent_status == 'completed':
                            # Task completed successfully
                            completion_msg = create_task_completion_message(
                                task_context, 
                                last_message.content
                            )
                            result['messages'][-1] = AIMessage(content=completion_msg)
                            
                        elif agent_status == 'failed':
                            # Task failed
                            failure_msg = create_task_failure_message(
                                task_context, 
                                last_message.content
                            )
                            result['messages'][-1] = AIMessage(content=failure_msg)
                            result['last_error_message'] = last_message.content
                
                return result
            else:
                # No active task, execute normally
                return agent_function(state, config)
                
        except Exception as e:
            logging.error(f"Error in task-aware agent wrapper: {e}")
            
            # Create error response
            if task_context:
                error_msg = create_task_failure_message(task_context, str(e))
            else:
                error_msg = f"Agent execution failed: {str(e)}"
            
            return {
                "messages": [AIMessage(content=error_msg)],
                "last_error_message": str(e)
            }
    
    return task_aware_agent


def get_task_progress_summary(state: Dict[str, Any]) -> str:
    """
    Generate a progress summary for all tasks in the execution plan.
    
    Args:
        state: Current agent state
        
    Returns:
        Formatted progress summary
    """
    plan_tasks = state.get("plan_tasks", [])
    if not plan_tasks:
        return "No execution plan found."
    
    # Convert tasks to dicts if needed
    tasks_list = []
    for task in plan_tasks:
        if hasattr(task, 'model_dump'):
            tasks_list.append(task.model_dump())
        elif isinstance(task, dict):
            tasks_list.append(task)
        else:
            tasks_list.append({
                'id': getattr(task, 'id', ''),
                'action': getattr(task, 'action', ''),
                'status': getattr(task, 'status', 'pending'),
                'agent': getattr(task, 'agent', ''),
                'order': getattr(task, 'order', 0)
            })
    
    # Sort by order
    tasks_list.sort(key=lambda x: x.get('order', 0))
    
    # Generate summary
    summary_lines = ["📋 **Execution Plan Progress**\n"]
    
    status_emojis = {
        'completed': '✅',
        'in_progress': '🔄',
        'pending': '⏳',
        'failed': '❌'
    }
    
    for i, task in enumerate(tasks_list, 1):
        status = task.get('status', 'pending')
        emoji = status_emojis.get(status, '❓')
        action = task.get('action', 'Unknown action')
        agent = task.get('agent', 'Unknown agent')
        
        summary_lines.append(f"{i}. {emoji} **{action}** (Agent: {agent})")
    
    return "\n".join(summary_lines)






# Example usage for creating task-aware agents
def create_task_aware_research_agent(base_research_agent):
    """
    Example of creating a task-aware research agent.
    
    Args:
        base_research_agent: Original research agent function
        
    Returns:
        Task-aware research agent
    """
    @wrap_agent_with_task_awareness
    def research_agent(state, config):
        # Extract task context for enhanced prompting
        task_context = state.get('current_task_context')
        
        if task_context:
            # Enhance the research focus based on task
            action = task_context.get('action', '')
            why = task_context.get('why', '')
            
            # Add task-specific context to messages
            task_instruction = f"""
**RESEARCH FOCUS:** {action}
**OBJECTIVE:** {why}

Please conduct focused research based on the above task assignment.
"""
            
            # Add instruction to state messages
            enhanced_state = state.copy()
            messages = list(enhanced_state.get('messages', []))
            messages.append(HumanMessage(content=task_instruction))
            enhanced_state['messages'] = messages
            
            return base_research_agent(enhanced_state, config)
        else:
            return base_research_agent(state, config)
    
    return research_agent
