import httpx
from supabase import create_async_client, create_client, AsyncClient, Client
from typing import Dict, Any, Optional
import logging
from enum import Enum
from src.core.config import get_settings
from datetime import datetime

settings = get_settings()
logger = logging.getLogger("src.api.db.optimised_utils")


class RealtimeEvent(str, Enum):
    CELL_UPDATE = "CELL_UPDATE"
    COLUMN_UPDATE = "COLUMN_UPDATE"
    COLUMN_INSERT = "COLUMN_INSERT"
    FILTER_UPDATE = "FILTER_UPDATE"
    ROW_INSERT = "ROW_INSERT"


async def get_supabase_client() -> AsyncClient:
    """
    Initialize and return a Supabase client instance.
    Uses environment variables for configuration.
    """
    try:
        return await create_async_client(
            settings.SUPABASE_URL, settings.SUPABASE_SERVICE_ROLE_KEY.get_secret_value()
        )
    except Exception as e:
        logger.error(f"Failed to initialize Supabase client: {str(e)}")
        raise


def get_supabase() -> Client:
    """
    Initialize and return a Supabase client instance.
    Uses environment variables for configuration.
    """
    try:
        return create_client(
            settings.SUPABASE_URL, settings.SUPABASE_SERVICE_ROLE_KEY.get_secret_value()
        )
    except Exception as e:
        logger.error(f"Failed to initialize Supabase client: {str(e)}")
        raise


async def get_cell(supabase: AsyncClient, input: Dict[str, Any]) -> Dict[str, Any]:
    try:

        cell = (
            await supabase.table("cells")
            .select("*")
            .eq("column_id", input["column_id"])
            .eq("row_id", input["row_id"])
            .eq("table_id", input["table_id"])
            .execute()
        )
        if cell.data and len(cell.data) > 0:
            return cell.data[0]
        else:
            raise Exception("Cell not found")
    except Exception as e:
        logger.error(f"Failed to retrieve cell: {str(e)}")
        raise


async def reimburse_tokens(supabase: AsyncClient, input: Dict[str, Any]) -> None:
    try:
        await supabase.rpc(
            "modify_tokens",
            {
                "p_operation": "increase",
                "p_organization_id": input["organization_id"],
                "p_tokens": input["credits"],
            },
        ).execute()
    except Exception as e:
        logger.error(f"Failed to reimburse tokens: {str(e)}")
        raise


async def update_cell(supabase: AsyncClient, input: Dict[str, Any]) -> Dict[str, Any]:
    try:
        cell = (
            await supabase.table("cells")
            .update(input)
            .eq("column_id", input["column_id"])
            .eq("row_id", input["row_id"])
            .eq("table_id", input["table_id"])
            .execute()
        )
        return cell.data[0]
    except Exception as e:
        logger.error(f"Failed to update cell: {str(e)}")
        raise


async def upsert_cell_details(
    supabase: AsyncClient, input: Dict[str, Any], value: Optional[Dict[str, Any]]
) -> Dict[str, Any]:
    try:
        cell_details = (
            await supabase.table("cell_details")
            .upsert(
                {
                    "table_id": input["table_id"],
                    "column_id": input["column_id"],
                    "row_id": input["row_id"],
                    "value": value,
                },
                on_conflict="table_id,column_id,row_id",
            )
            .execute()
        )
        return cell_details.data[0]
    except Exception as e:
        logger.error(f"Failed to upsert cell details: {str(e)}")
        raise


async def append_cell_details(
    supabase: AsyncClient, input: Dict[str, Any], value: Optional[Dict[str, Any]]
) -> Dict[str, Any]:
    try:
        _cell_details = (
            await supabase.table("cell_details")
            .select("*")
            .eq("column_id", input["column_id"])
            .eq("row_id", input["row_id"])
            .eq("table_id", input["table_id"])
            .execute()
        )
        _cell_details = _cell_details.data[0] if _cell_details.data else None

        cell_details = (
            await supabase.table("cell_details")
            .upsert(
                {
                    "table_id": input["table_id"],
                    "column_id": input["column_id"],
                    "row_id": input["row_id"],
                    "value": (
                        _cell_details["value"] + [value] if _cell_details else [value]
                    ),
                },
                on_conflict="table_id,column_id,row_id",
            )
            .execute()
        )
        return cell_details.data[0]
    except Exception as e:
        logger.error(f"Failed to append cell details: {str(e)}")
        raise


async def send_realtime_broadcast(
    supabase: AsyncClient, channel: str, event: RealtimeEvent, payload: Dict[str, Any]
) -> None:
    try:
        response = await supabase.rpc('notify_fe_generic', {
            "channel": channel,
            "event": event,
            "data": payload,
        }).execute()    
    except Exception as e:
        logger.error(f"Failed to send realtime broadcast: {str(e)}")


async def update_cell_and_notify(
    supabase: AsyncClient,
    cell: Dict[str, Any],
) -> None:
    await update_cell(supabase, cell)
    await send_realtime_broadcast(supabase, cell["table_id"], RealtimeEvent.CELL_UPDATE, cell)


async def update_organization_subscription(
    supabase: AsyncClient,
    organization_id: str,
    customer_id: str,
    credits: int,
    subscription_id: str,
    subscription_status: str,
    plan: str,
    current_period_start: datetime,
    current_period_end: datetime
) -> Dict[str, Any]:
    """
    Updates organization subscription details in Supabase.
    Also adds new credits to existing tokens.
    
    Args:
        supabase: AsyncClient - Supabase client instance
        organization_id: str - Organization ID
        customer_id: str - Stripe customer ID
        credits: int - Number of credits/tokens to add
        subscription_id: str - Stripe subscription ID
        subscription_status: str - Subscription status
        plan: str - Subscription plan name
        current_period_start: datetime - Subscription period start
        current_period_end: datetime - Subscription period end
    
    Returns:
        Dict containing the updated organization data
    """
    try:
        # First get current tokens
        org_data = await supabase.table("organizations").select("tokens").eq("id", organization_id).execute()
        current_tokens = org_data.data[0]["tokens"] if org_data.data else 0
        
        # Calculate new total tokens
        new_total_tokens = current_tokens + credits
        
        # Convert datetime objects to Unix timestamps
        period_start_timestamp = int(current_period_start.timestamp())
        period_end_timestamp = int(current_period_end.timestamp())
        
        # Update organization with all subscription data
        result = await supabase.table("organizations").update({
            "stripe_customer_id": customer_id,
            "original_tokens": credits,
            "tokens": new_total_tokens,
            "stripe_subscription_id": subscription_id,
            "subscription_status": subscription_status,
            "subscription_plan": plan,
            "current_period_start": period_start_timestamp,
            "current_period_end": period_end_timestamp
        }).eq("id", organization_id).execute()

        if not result.data:
            raise Exception(f"Failed to update organization {organization_id}")

        return result.data[0]
    except Exception as e:
        logger.error(f"Failed to update organization subscription: {str(e)}")
        raise
