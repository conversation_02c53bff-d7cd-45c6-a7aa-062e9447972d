"""
The search node is responsible for searching the internet for information.
"""
import json
from datetime import datetime
import os
from dotenv import load_dotenv
from langchain_core.messages import HumanMessage
from langchain_core.runnables import RunnableConfig
from langchain_community.tools import TavilySearchResults
from bond_ai.perplexity.state import AgentState
from bond_ai.perplexity.model import get_model
from langchain_core.prompts import ChatPromptTemplate
from langchain_perplexity import ChatPerplexity

load_dotenv()

PPLX_API_KEY = os.getenv("PPLX_API_KEY")

async def search_node(state: AgentState, config: RunnableConfig):
    """
    The search node is responsible for searching the internet for information.
    """

    # Load environment variables to get Tavily API key

    chat = ChatPerplexity(temperature=0, pplx_api_key=PPLX_API_KEY, model="sonar")
   

    current_step = next((step for step in state["steps"] if step["status"] == "pending"), None)

    if current_step is None:
        raise ValueError("No step to search for")

    if current_step["type"] != "search":
        raise ValueError("Current step is not a search step")

    instructions = f"""
Execute a targeted web search for sales intelligence research.

SEARCH STEP: {current_step["description"]}
CONTEXT: Step {current_step["id"]} of {len(state["steps"])} total research steps
DATE: {datetime.now().strftime("%Y-%m-%d")}

Generate a precise search query to find the requested information. Focus on recent, credible sources that provide actionable sales intelligence.
"""
  
    system_prompt = "You are a helpful assistant."

    prompt = ChatPromptTemplate([
        ("system", system_prompt),
        ("user", "{input}")
    ])

    chain = prompt | chat
    response = await chain.ainvoke({"input": instructions})

    search_result = response.content



    # Handle the tool response content
    # try:
    #     if hasattr(tool_msg, 'content') and tool_msg.content:
    #         # Try to parse as JSON if it's a string
    #         if isinstance(tool_msg.content, str):
    #             search_result = json.loads(tool_msg.content)
    #         else:
    #             # If it's already a dict/object, use it directly
    #             search_result = tool_msg.content
    #     else:
    #         # If no content, create empty result
    #         search_result = {"results": [], "answer": "No results found"}

    # except json.JSONDecodeError as e:
    #     # If JSON parsing fails, wrap the raw content
    #     search_result = {
    #         "results": [],
    #         "answer": str(tool_msg.content) if tool_msg.content else "No results found",
    #         "error": f"JSON parsing failed: {str(e)}"
    #     }

    current_step["search_result"] = search_result
    current_step["updates"] = [*current_step["updates"], "Extracting information..."]

    return state
