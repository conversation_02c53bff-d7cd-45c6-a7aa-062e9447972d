from src.db.optimised_utils import (
    get_cell,
    get_supabase_client,
    update_cell_and_notify,
    upsert_cell_details,
)
import src.services.prospeo.mobile.finder as prospeo
import src.services.prospeo.email.finder as prospeo_email_finder
from ..celery_app import app
import logging
from supabase import AsyncClient
import asyncio
from typing import Dict, Any, Optional

logger = logging.getLogger("src.worker.tasks.prospeo")


async def run_prospeo_phone_finder(
    previous: Optional[Dict[str, Any]], input: Dict[str, Any]
):
    # Check if previous exists and has success=True OR if it has only_if=False
    if (previous and previous.get("success")) or (
        previous and previous.get("only_if") is False
    ):
        return previous

    supabase_client: AsyncClient = await get_supabase_client()
    cell = await get_cell(
        supabase_client,
        input,
    )
    cell["run_status"] = {
        "run": "processing",
        "message": "Finding email using Prospeo...",
    }
    await update_cell_and_notify(
        supabase_client,
        cell,
    )
    response = await prospeo.get_phone_number(input["value"])
    if response["success"]:
        # Transform the data into the structured format
        cell_details = response["data"]
        cell["value"] = response["data"]["mobile_number"]
        cell["run_status"] = {"run": "completed", "message": "Completed"}
        await update_cell_and_notify(supabase_client, cell)
        await upsert_cell_details(supabase_client, input, cell_details)
        response["result_found"] = True
    return response


async def run_prospeo_email_finder(
    previous: Optional[Dict[str, Any]], input: Dict[str, Any]
):
    # Check if previous exists and has success=True OR if it has only_if=False
    if (previous and previous.get("success")) or (
        previous and previous.get("only_if") is False
    ):
        return previous

    supabase_client: AsyncClient = await get_supabase_client()
    cell = await get_cell(
        supabase_client,
        input,
    )
    cell["run_status"] = {
        "run": "processing",
        "message": "Finding email using Prospeo...",
    }
    await update_cell_and_notify(
        supabase_client,
        cell,
    )
    response = await prospeo_email_finder.get_email(input["value"])
    if response["success"]:
        # Transform the data into the structured format
        cell_details = response["data"]
        cell["value"] = response["data"]["email"]
        cell["run_status"] = {"run": "completed", "message": "Completed"}
        await update_cell_and_notify(supabase_client, cell)
        await upsert_cell_details(supabase_client, input, cell_details)
        response["result_found"] = True
    else:
        if previous.get("result_found", False) is True:
            response["result_found"] = True
    return response


@app.task(
    name="src.worker.tasks.prospeo.run_prospeo_task",
    max_retries=1,
    ignore_result=False,
    queue="enrichment",
    rate_limit="150/m",
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_kwargs={"max_retries": 1},
)
def run_prospeo_task(previous: Optional[Dict[str, Any]], input: Dict[str, Any]):
    """
    Celery task that runs the async function using asyncio.

    Args:
        self: The task instance (provided by bind=True)
        input_dict: Dictionary representation of the ServiceRequest
    """
    # Convert dict back to ServiceRequest
    if input["service_id"] == 10:
        return asyncio.run(run_prospeo_email_finder(previous, input))
    elif input["service_id"] == 11:
        return asyncio.run(run_prospeo_phone_finder(previous, input))
