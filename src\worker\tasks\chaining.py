import time
from ..celery_app import app
import logging
from src.schemas.requests import ServiceRequest
import asyncio

logger = logging.getLogger("src.worker.tasks.chaining")


@app.task(name="src.worker.tasks.chaining.run_chaining_task1")
def run_chaining_task1(output=None, input=None):
    logger.info(f"task1 {output} {input}")
    return input + output if output else input + 1


@app.task(name="src.worker.tasks.chaining.run_chaining_task2")
def run_chaining_task2(output=None, input=None):
    logger.info(f"task2 {output} {input}")
    return input + output if output else input + 1


@app.task(name="src.worker.tasks.chaining.run_chaining_task3")
def run_chaining_task3(output=None, input=None):
    logger.info(f"task3 {output} {input}")
    return input + output if output else input + 1


@app.task(name="src.worker.tasks.chaining.run_chaining_task4")
def run_chaining_task4(output=None, input=None):
    return asyncio.run(_run_chaining_task4(output, input))


async def _run_chaining_task4(output=None, input=None):
    time.sleep(5)
    logger.info(f"task4 {output} {input}")
    return input + output if output else input + 1
