from ..celery_app import app
import logging
from supabase import AsyncClient
from typing import Dict, Any, Optional

from src.db.optimised_utils import get_cell, update_cell_and_notify, get_supabase_client
import asyncio
from src.core.config import get_settings
from src.schemas.llm import RunOnlyIfResponse
from src.agent.utils import load_chat_model
from src.agent.prompts import RUN_ONLY_IF_PROMPT

settings = get_settings()
logger = logging.getLogger("src.worker.tasks.llm")
RUN_ONLY_IF_MODEL = settings.RUN_ONLY_IF_MODEL


@app.task(
    name="src.worker.tasks.llm.run_only_if_task",
    ignore_result=False,
    queue="llm",
    rate_limit="10000/m",
    bind=True,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_kwargs={"max_retries": 1},
)
def run_only_if_task(self, previous: Optional[Dict[str, Any]], input: Dict[str, Any]):
    async def _run_only_if_task(
        previous: Optional[Dict[str, Any]], input: Dict[str, Any]
    ):
        if input["formula"] is None:
            return {"only_if": True}

        supabase_client: AsyncClient = await get_supabase_client()
        cell = await get_cell(
            supabase_client,
            input,
        )
        cell["run_status"] = {"run": "validating", "message": "Validating condition..."}
        await update_cell_and_notify(
            supabase_client,
            cell,
        )
        llm = load_chat_model(RUN_ONLY_IF_MODEL).with_structured_output(
            RunOnlyIfResponse
        )
        prompt = RUN_ONLY_IF_PROMPT.format(formula=input["formula"])
        response = llm.invoke(prompt)
        response = response.model_dump()
        logger.info(f"Run only if response: {response}")
        if response["is_valid"] is True:
            return {"only_if": True}
        else:
            cell["run_status"] = {
                "run": "condition_not_met",
                "message": "Condition not met!",
            }
            await update_cell_and_notify(
                supabase_client,
                cell,
            )
            return {"only_if": False}

    return asyncio.run(_run_only_if_task(previous, input))
