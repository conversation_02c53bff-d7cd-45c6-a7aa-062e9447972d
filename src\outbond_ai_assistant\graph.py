"""Define a ReAct agent using LangGraph.

This agent follows the ReAct pattern (Reasoning and Acting) to solve tasks
by thinking step by step and using tools when needed.
"""

import json
from typing import Dict, Any

from langchain_core.messages import SystemMessage, ToolMessage, HumanMessage, AIMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph, END
from langgraph.types import interrupt
from datetime import datetime

from src.outbond_ai_assistant.utils import load_chat_model, clean_thinking_blocks_for_bedrock
from src.outbond_ai_assistant.prompts import OUTBOND_AI_ASSISTANT_PROMPT
from src.outbond_ai_assistant.configuration import Configuration
from src.outbond_ai_assistant.state import AgentState
from src.outbond_ai_assistant.tools import tools, tools_by_name, get_table_filters, read_table_data
from src.outbond_ai_assistant.agent_db import update_column_description, get_table_columns_info
from src.outbond_ai_assistant_v2.tools.read_table_tools.read_table import read_table_data_tool
from src.outbond_ai_assistant_v2.supabase.read_table.models import TableDataRequest
from src.outbond_ai_assistant_v2.supabase.table.update_columns import update_column_by_id
from src.outbond_ai_assistant_v2.supabase.table.get_columns import get_columns_by_table_id


def summarize_selected_rows(row_ids, config: RunnableConfig) -> str:
    """
    Placeholder function to fetch and summarize selected rows.
    TODO: Implement actual row fetching and summarization logic.
    
    Args:
        row_ids: The selected row IDs (can be int, list, or str)
        config: The runnable configuration
        
    Returns:
        A summary of the selected rows
    """
    # Convert row_ids to a consistent format
    if isinstance(row_ids, int):
        row_ids = [row_ids]
    elif isinstance(row_ids, str):
        # Handle comma-separated string of IDs
        try:
            row_ids = [int(id.strip()) for id in row_ids.split(',') if id.strip()]
        except ValueError:
            row_ids = []
    
    if not row_ids:
        return "No valid row IDs provided"
    
    # TODO: Implement actual row fetching logic here
    # This should:
    # 1. Fetch the actual row data using the row IDs
    # 2. Extract key information from each row
    # 3. Summarize the data in a concise format
    
    # Placeholder response for now
    row_count = len(row_ids)
    if row_count == 1:
        return f"Selected 1 row (ID: {row_ids[0]}). [Row data summary will be implemented here]"
    else:
        return f"Selected {row_count} rows (IDs: {', '.join(map(str, row_ids))}). [Row data summary will be implemented here]"


def table_indexing_node(state: AgentState, config: RunnableConfig):
    """Node that indexes table data and creates a summary for the agent."""
    configuration = Configuration.from_runnable_config(config)
    
    def update_column_description(column_id: str, description: str) -> bool:
        """Helper to update column description in database."""
        success, _ = update_column_by_id(config=config, column_id=int(column_id), fields={"agent_description": description})
        return success is not None
    
    try:
        # Get columns data
        columns_data, error = get_columns_by_table_id(configuration.table_id)
        if error:
            raise Exception(error)
        
        # Handle case when there are no columns
        if not columns_data:
            return {"table_summary": "There is no columns in the table yet"}
        
        # Find columns needing descriptions (only those without summaries)
        columns_needing_descriptions = [
            col for col in columns_data 
            if not col.get('agent_description') or not str(col.get('agent_description', '')).strip()
        ]
        
        # Process only columns that need descriptions
        if columns_needing_descriptions:
            column_names = [col['name'] for col in columns_data]
            
            # Get structured analysis with summaries
            request = TableDataRequest(
                table_id="", max_rows=10, column_names=column_names, 
                row=False, apply_table_filters=True, apply_table_sorts=True, include_summary=True
            )
            response = read_table_data_tool.invoke({"request": request}, config)
            
            if not response.success or not response.columns:
                raise Exception(f"Failed to analyze table data: {response.error_message or 'No column data returned'}")
            
            # Create name-to-id mapping
            name_to_id = {col['name']: col['id'] for col in columns_data}
            
            # Process each column from response
            for idx, column_summary in enumerate(response.columns):
                summary_dict = column_summary.model_dump() if hasattr(column_summary, 'model_dump') else column_summary
                
                # Find column name (exclude standard keys)
                standard_keys = {'is_runnable', 'column_run_status', 'column_data_summary'}
                column_name = next((key for key in summary_dict.keys() if key not in standard_keys), 
                                 column_names[idx] if idx < len(column_names) else None)
                
                column_id = name_to_id.get(column_name) if column_name else None
                
                if not column_id:
                    raise Exception(f"Could not match column '{column_name}' to database ID")
                
                # Extract only the summary from the column data
                summary_text = summary_dict.get('column_data_summary', 'Summary not available')
                
                if not update_column_description(column_id, summary_text):
                    raise Exception(f"Failed to update description for column '{column_name}' (ID: {column_id})")
                
                # Update local data with just the summary
                for col in columns_data:
                    if col['id'] == column_id:
                        col['agent_description'] = summary_text
                        break
        
        # Always fetch fresh schema data for the final table summary
        column_names = [col['name'] for col in columns_data]
        request = TableDataRequest(
            table_id="", max_rows=10, column_names=column_names, 
            row=False, apply_table_filters=True, apply_table_sorts=True, include_summary=False
        )
        fresh_response = read_table_data_tool.invoke({"request": request}, config)
        
        if not fresh_response.success or not fresh_response.columns:
            raise Exception(f"Failed to get fresh table data: {fresh_response.error_message or 'No column data returned'}")
        
        # Create comprehensive table summary combining fresh schema + stored summaries
        table_summary_parts = []
        name_to_description = {col['name']: col.get('agent_description', '') for col in columns_data}
        name_to_id = {col['name']: col['id'] for col in columns_data}
        
        for idx, column_schema in enumerate(fresh_response.columns):
            schema_dict = column_schema.model_dump() if hasattr(column_schema, 'model_dump') else column_schema
            
            # Find column name (exclude standard keys)
            standard_keys = {'is_runnable', 'column_run_status', 'column_data_summary'}
            column_name = next((key for key in schema_dict.keys() if key not in standard_keys), 
                             column_names[idx] if idx < len(column_names) else None)
            
            if column_name:
                # Combine fresh schema with stored summary and column ID
                summary_data = {
                    'column_name': column_name,
                    'column_id': name_to_id.get(column_name),
                    'data_summary': name_to_description.get(column_name, 'No summary available'),
                    **schema_dict
                }
                
                table_summary_parts.append(json.dumps(summary_data, indent=2, default=str))
        
        # Ensure all columns have descriptions
        for col in columns_data:
            if not col.get('agent_description'):
                raise Exception(f"Column '{col['name']}' (ID: {col['id']}) has no description after processing")
        
        return {"table_summary": "\n\n".join(table_summary_parts)}
        
    except Exception as e:
        raise Exception(f"Table indexing failed: {str(e)}")

def call_model(
    state: AgentState,
    config: RunnableConfig,
):
    """Node that calls the LLM to generate the next response."""
    # Get configuration
    configuration = Configuration.from_runnable_config(config)
    
    # Check if we're in chat mode - if so, don't bind tools
    is_chat_mode = state.get("mode") == "chat"
    
    if is_chat_mode:
        model = load_chat_model(configuration.model)
    else:
        model = load_chat_model(configuration.model).bind_tools(tools)
    
    current_filters = get_table_filters(configuration.table_id)

    # Clean thinking blocks from messages to remove internal metadata
    cleaned_messages = clean_thinking_blocks_for_bedrock(list(state["messages"]))

    # Create the system prompt with table summary
    system_prompt = OUTBOND_AI_ASSISTANT_PROMPT.format(
        table_id=configuration.table_id, 
        today_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"), 
        current_filters=current_filters,
        table_summary=state["table_summary"]
    )
    
    # Add chat mode information to the prompt if in chat mode
    if is_chat_mode:
        system_prompt += "\n\n**CHAT MODE ACTIVE**: You are currently in chat mode. Answer questions and provide guidance, but do not use any tools or perform any actions. Only provide conversational responses and advice. If the USER wants you to use tools, ask them to turn off chat mode."
    
    # Add context for selected columns if they exist
    selected_column_ids = state.get("selected_column_ids")
    if selected_column_ids:
        # Handle both string and list formats
        if isinstance(selected_column_ids, str):
            column_ids_str = selected_column_ids
        else:
            column_ids_str = str(selected_column_ids)
        
        system_prompt += f"\n\n**SELECTED COLUMNS**: The user has selected specific columns (IDs: {column_ids_str}). Focus your attention and responses on these selected columns when relevant."
    
    # Add context for selected rows if they exist
    selected_row_ids = state.get("selected_row_ids")
    if selected_row_ids:
        try:
            row_summary = summarize_selected_rows(selected_row_ids, config)
            system_prompt += f"\n\n**SELECTED ROWS**: The user has selected specific rows. {row_summary}"
        except Exception as e:
            # Handle any errors in row summarization gracefully
            system_prompt += f"\n\n**SELECTED ROWS**: The user has selected specific rows (IDs: {selected_row_ids}), but unable to fetch summary: {str(e)}"

    # Create the messages with cleaned conversation history
    messages = [
        SystemMessage(system_prompt),
        *cleaned_messages
    ]

    try:
        # Call the model with the current state
        response = model.invoke(messages, config)
        
        # Return the response to be added to messages
        return {"messages": [response]}
    except Exception as e:
        # Handle model errors gracefully with same model configuration
        # Clean the last few messages before fallback call
        cleaned_fallback_messages = clean_thinking_blocks_for_bedrock(list(state["messages"][-3:]))
        fallback_messages = [
            SystemMessage(f"There was an error with the previous request. Please provide a helpful response. Error: {str(e)}"),
            *cleaned_fallback_messages
        ]
        fallback_response = model.invoke(fallback_messages, config)
        return {"messages": [fallback_response]}


def tool_node(state: AgentState):
    """Node that executes tools based on the model's tool calls."""
    outputs = []
    
    # Get the last message which should contain tool calls
    last_message = state["messages"][-1]
    
    # Process each tool call - only AIMessage has tool_calls
    if not isinstance(last_message, AIMessage) or not last_message.tool_calls:
        return {"messages": []}
    
    for tool_call in last_message.tool_calls:
        # Check if this is a run_column tool call that needs confirmation
        if tool_call["name"] == "run_column":
            # Extract parameters for confirmation message
            args = tool_call["args"]
            column_name = args.get("column_name", f"Column {args.get('column_id', 'Unknown')}")
            count = args.get("count", 1)
            rows_text = f"{count} row" if count == 1 else f"{count} rows"
            
            confirmation_message = {
                "column_name": column_name,
                "column_id": args.get("column_id"),
                "rows_count": count,
                "message": f"Do you want to run the column '{column_name}' for {rows_text}?"
            }
            
            # Request user confirmation
            user_response = interrupt(confirmation_message)
            
            # Check if user confirmed or cancelled
            if not (user_response and str(user_response).lower() in ['yes', 'y', 'confirm', 'proceed', 'ok', 'run']):
                # User cancelled - create error message
                outputs.append(
                    ToolMessage(
                        content=f"Column execution cancelled by user. Column '{column_name}' was not run.",
                        name=tool_call["name"],
                        tool_call_id=tool_call["id"],
                    )
                )
                continue  # Skip to next tool call without executing
        
        # Execute the tool (either non-run_column tools or approved run_column)
        try:
            # Get the tool by name
            tool = tools_by_name[tool_call["name"]]
            
            # Execute the tool with the provided arguments
            tool_result = tool.invoke(tool_call["args"])
            
            # Create a tool message with the result
            outputs.append(
                ToolMessage(
                    content=str(tool_result),  # Convert to string to ensure JSON serialization
                    name=tool_call["name"],
                    tool_call_id=tool_call["id"],
                )
            )
        except Exception as e:
            # Handle any errors that occur during tool invocation
            error_message = f"Error executing tool '{tool_call['name']}': {str(e)}"
            
            # Create a tool message with the error
            outputs.append(
                ToolMessage(
                    content=error_message,
                    name=tool_call["name"],
                    tool_call_id=tool_call["id"],
                )
            )
    
    # Return the tool outputs to be added to messages
    return {"messages": outputs}


def should_continue(state: AgentState):
    """Determine whether to continue with tool execution or end the conversation."""
    messages = state["messages"]
    last_message = messages[-1]
    
    # If we're in chat mode, always end (no tools should be used)
    is_chat_mode = state.get("mode") == "chat"
    if is_chat_mode:
        return "end"
    
    # If there are tool calls, continue to the tool node
    if isinstance(last_message, AIMessage) and last_message.tool_calls:
        return "continue"
    
    # Otherwise, end the conversation
    return "end"


# Define the graph
def create_graph():
    """Create and return the ReAct agent graph."""
    # Define a new graph with our state
    workflow = StateGraph(AgentState, config_schema=Configuration)
    
    # Add nodes to the graph
    workflow.add_node("table_indexing", table_indexing_node)
    workflow.add_node("agent", call_model)
    workflow.add_node("tools", tool_node)
    
    # Set the entry point to table_indexing (runs first)
    workflow.set_entry_point("table_indexing")
    
    # Add edge from table_indexing to agent
    workflow.add_edge("table_indexing", "agent")
    
    # Add conditional edges from agent node
    workflow.add_conditional_edges(
        "agent",
        should_continue,
        {
            "continue": "tools",
            "end": END,
        },
    )
    
    # Add edge from tools back to agent
    workflow.add_edge("tools", "agent")
    
    # Compile the graph (LangGraph Platform handles persistence automatically)
    graph = workflow.compile()
    graph.name = "Research Agent"  # Custom name for LangSmith
    
    return graph


# Create the graph
graph = create_graph()
