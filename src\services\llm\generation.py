import asyncio
from src.db.utils import (
    Cell,
    CellDetails,
    CellStatus,
    RealtimeEvent,
    send_realtime_broadcast,
    update_cell,
    reimburse_tokens,
    upsert_cell_details,
    CellId,
    get_cell,
)
from src.schemas.requests import ServiceRequest
from src.schemas.llm import RunOnlyIfResponse
from src.agent.utils import load_chat_model
from src.agent.prompts import RUN_ONLY_IF_PROMPT, FORMULA_PROMPT
from langchain_core.messages import AIMessage, HumanMessage, ToolMessage, SystemMessage
from typing import Dict, Any
import logging
from supabase import AsyncClient
from src.agent.graph import graph
from src.schemas.requests import ServiceRequest

# Import utility functions
from src.services.llm.utils import (
    extract_domain,
    extract_tool_arg,
)

# Import centralized configuration
from src.core.config import get_settings

# Get application settings
settings = get_settings()

# Access settings as attributes
RUN_ONLY_IF_MODEL = settings.RUN_ONLY_IF_MODEL
AI_COLUMN_MODEL = settings.AI_COLUMN_MODEL
AI_FORMULA_MODEL = settings.AI_FORMULA_MODEL

logger = logging.getLogger(__name__)


async def run_only_run_if(formula: str) -> RunOnlyIfResponse:
    """
    Evaluates a formula using an LLM and returns a serializable dictionary.

    Args:
        formula: The formula to evaluate

    Returns:
        Dict[str, Any]: A dictionary containing 'is_valid' boolean and 'status' string fields
    """
    llm = load_chat_model(RUN_ONLY_IF_MODEL).with_structured_output(RunOnlyIfResponse)
    prompt = RUN_ONLY_IF_PROMPT.format(formula=formula)
    response = llm.invoke(prompt)
    return response


async def _handle_formula_condition(
    request: ServiceRequest,
    cell: Cell,
    supabase: AsyncClient,
) -> bool:
    """
    Handle formula condition evaluation.

    Args:
        request: The ServiceRequest containing all parameters for the AI operation
        cell: The cell data
        supabase: Supabase client

    Returns:
        bool: True if processing should continue, False otherwise
    """
    if not request.formula:
        return True

    try:
        cell.run_status = CellStatus(
            run="validating", message="Validating condition..."
        )
        await send_realtime_broadcast(
            supabase,
            cell.table_id,
            RealtimeEvent.CELL_UPDATE,
            cell.model_dump(),
        )
        await update_cell(supabase, cell)
        if settings.IS_STRESS_TESTING:
            # wait for 30 seconds
            await asyncio.sleep(30)
            return True
        result = await run_only_run_if(request.formula)

        if not result.is_valid:
            cell.run_status = CellStatus(
                run="condition_not_met", message="Condition not met!"
            )
            await send_realtime_broadcast(
                supabase,
                cell.table_id,
                RealtimeEvent.CELL_UPDATE,
                cell.model_dump(),
            )
            await update_cell(supabase, cell)

            # Reimburse tokens if credits are available
            if request.credits > 0:
                await reimburse_tokens(
                    supabase, request.organization_id, request.credits
                )
            return False

    except Exception as e:
        logger.error(f"Error evaluating formula condition: {str(e)}")
        error_msg = f"Formula evaluation error: {str(e)}"
        cell.run_status = CellStatus(run="failed", message=error_msg)
        await send_realtime_broadcast(
            supabase,
            cell.table_id,
            RealtimeEvent.CELL_UPDATE,
            cell.model_dump(),
        )
        await update_cell(supabase, cell)
        if request.credits > 0:
            await reimburse_tokens(supabase, request.organization_id, request.credits)
        return False

    return True


async def run_ai_column(
    input: ServiceRequest,
    supabase: AsyncClient,
) -> None:
    """
    Process an AI column request, evaluating any formula conditions and generating content.

    This function handles the entire lifecycle of an AI column request:
    1. Evaluates any conditional formula to determine if processing should continue
    2. If condition fails, updates cell status and reimburses tokens
    3. If condition passes, generates AI content and updates the cell
    4. Stores detailed results in the cell_details table

    Args:
        request: The ServiceRequest containing all parameters for the AI operation
        supabase: An authenticated Supabase client for database operations

    Returns:
        None: This function operates asynchronously and doesn't return a value
    """
    try:
        # Get cell data
        cell = await get_cell(
            supabase,
            CellId(
                column_id=input.column_id,
                row_id=input.row_id,
                table_id=input.table_id,
            ),
        )

        # Check formula condition
        if not await _handle_formula_condition(input, cell, supabase):
            return

        # Generate AI formula content
        try:
            cell.run_status = CellStatus(
                run="generating", message="Generating content..."
            )
            await send_realtime_broadcast(
                supabase,
                input.table_id,
                RealtimeEvent.CELL_UPDATE,
                cell.model_dump(),
            )
            await update_cell(supabase, cell)

            if settings.IS_STRESS_TESTING:
                # wait for 30 seconds
                await asyncio.sleep(30)
                response = AIMessage(content="Outbond rocks!")
            else:
                llm = load_chat_model(AI_COLUMN_MODEL)
                messages = [
                    SystemMessage(content=input.value.system_prompt or ""),
                    HumanMessage(content=input.value.user_prompt),
                ]
                response = llm.invoke(messages)

            # Update cell with result
            cell.run_status = CellStatus(run="completed", message="Completed")
            cell.value = response.content
            await send_realtime_broadcast(
                supabase,
                input.table_id,
                RealtimeEvent.CELL_UPDATE,
                cell.model_dump(),
            )
            await update_cell(supabase, cell)
            await upsert_cell_details(
                supabase,
                CellDetails(
                    table_id=input.table_id,
                    column_id=input.column_id,
                    row_id=input.row_id,
                    value={
                        "result": response.content,
                    },
                ),
            )

        except Exception as e:
            logger.error(f"Error generating or storing AI content: {str(e)}")
            error_msg = f"AI processing error: {str(e)}"
            cell.run_status = CellStatus(run="failed", message=error_msg)
            await send_realtime_broadcast(
                supabase,
                input.table_id,
                RealtimeEvent.CELL_UPDATE,
                cell.model_dump(),
            )
            await update_cell(supabase, cell)

            await reimburse_tokens(supabase, input.organization_id, input.credits)

    except Exception as e:
        # Catch-all for any unexpected errors
        logger.error(f"Unexpected error in run_ai_column: {str(e)}")

    return


async def run_perplexity_column(
    input: ServiceRequest,
    supabase: AsyncClient,
) -> None:
    """
    Process a Perplexity column request, evaluating any formula conditions and generating content.

    This function handles the entire lifecycle of a Perplexity column request:
    1. Evaluates any conditional formula to determine if processing should continue
    2. If condition fails, updates cell status and reimburses tokens
    3. If condition passes, generates Perplexity content and updates the cell
    4. Stores detailed results in the cell_details table

    Args:
        request: The ServiceRequest containing all parameters for the Perplexity operation
        supabase: An authenticated Supabase client for database operations

    Returns:
        None: This function operates asynchronously and doesn't return a value
    """
    try:
        # Get cell data
        cell = await get_cell(
            supabase,
            CellId(
                column_id=input.column_id,
                row_id=input.row_id,
                table_id=input.table_id,
            ),
        )

        # Check formula condition
        if not await _handle_formula_condition(input, cell, supabase):
            return

        # Generate Perplexity content
        try:
            cell.run_status = CellStatus(
                run="generating", message="Researching with Perplexity..."
            )
            await send_realtime_broadcast(
                supabase,
                input.table_id,
                RealtimeEvent.CELL_UPDATE,
                cell.model_dump(),
            )
            await update_cell(supabase, cell)

            if settings.IS_STRESS_TESTING:
                # wait for 30 seconds
                await asyncio.sleep(30)
                response = AIMessage(content="Perplexity research completed!")
            else:
                llm = load_chat_model(input.value.model)
                messages = [
                    SystemMessage(content=input.value.system_prompt or ""),
                    HumanMessage(content=input.value.user_prompt),
                ]
                
                # Build extra_body with non-None parameters
                extra_body = {}
                if input.value.search_mode is not None:
                    extra_body["search_mode"] = input.value.search_mode
                if input.value.reasoning_effort is not None:
                    extra_body["reasoning_effort"] = input.value.reasoning_effort
                if input.value.max_tokens is not None:
                    extra_body["max_tokens"] = input.value.max_tokens
                if input.value.temperature is not None:
                    extra_body["temperature"] = input.value.temperature
                if input.value.top_p is not None:
                    extra_body["top_p"] = input.value.top_p
                if input.value.search_domain_filter is not None:
                    extra_body["search_domain_filter"] = input.value.search_domain_filter
                if input.value.return_images is not None:
                    extra_body["return_images"] = input.value.return_images
                if input.value.return_related_questions is not None:
                    extra_body["return_related_questions"] = input.value.return_related_questions
                if input.value.search_recency_filter is not None:
                    extra_body["search_recency_filter"] = input.value.search_recency_filter
                if input.value.search_after_date_filter is not None:
                    extra_body["search_after_date_filter"] = input.value.search_after_date_filter
                if input.value.search_before_date_filter is not None:
                    extra_body["search_before_date_filter"] = input.value.search_before_date_filter
                if input.value.last_updated_after_filter is not None:
                    extra_body["last_updated_after_filter"] = input.value.last_updated_after_filter
                if input.value.last_updated_before_filter is not None:
                    extra_body["last_updated_before_filter"] = input.value.last_updated_before_filter
                if input.value.top_k is not None:
                    extra_body["top_k"] = input.value.top_k
                if input.value.presence_penalty is not None:
                    extra_body["presence_penalty"] = input.value.presence_penalty
                if input.value.frequency_penalty is not None:
                    extra_body["frequency_penalty"] = input.value.frequency_penalty
                if input.value.disable_search is not None:
                    extra_body["disable_search"] = input.value.disable_search
                if input.value.enable_search_classifier is not None:
                    extra_body["enable_search_classifier"] = input.value.enable_search_classifier
                
                response = llm.invoke(messages, extra_body=extra_body)

            # Update cell with result
            cell.run_status = CellStatus(run="completed", message="Completed")
            cell.value = response.content
            await send_realtime_broadcast(
                supabase,
                input.table_id,
                RealtimeEvent.CELL_UPDATE,
                cell.model_dump(),
            )
            await update_cell(supabase, cell)
            
            # Build result with search metadata
            result_value = {
                "result": response.content,
            }
            
            # Add search results if available
            if hasattr(response, 'additional_kwargs') and "search_results" in response.additional_kwargs:
                result_value["search_results"] = response.additional_kwargs["search_results"]
            
            await upsert_cell_details(
                supabase,
                CellDetails(
                    table_id=input.table_id,
                    column_id=input.column_id,
                    row_id=input.row_id,
                    value=result_value,
                ),
            )

        except Exception as e:
            logger.error(f"Error generating or storing Perplexity content: {str(e)}")
            error_msg = f"Perplexity processing error: {str(e)}"
            cell.run_status = CellStatus(run="failed", message=error_msg)
            await send_realtime_broadcast(
                supabase,
                input.table_id,
                RealtimeEvent.CELL_UPDATE,
                cell.model_dump(),
            )
            await update_cell(supabase, cell)

            await reimburse_tokens(supabase, input.organization_id, input.credits)

    except Exception as e:
        # Catch-all for any unexpected errors
        logger.error(f"Unexpected error in run_perplexity_column: {str(e)}")

    return


async def run_ai_formula(
    input: ServiceRequest,
    supabase: AsyncClient,
) -> None:
    """
    Process an AI formula request, evaluating any formula conditions and generating formula content.

    This function handles the entire lifecycle of an AI formula request:
    1. Evaluates any conditional formula to determine if processing should continue
    2. If condition fails, updates cell status and reimburses tokens
    3. If condition passes, generates AI formula content and updates the cell
    4. Stores detailed results in the cell_details table

    Args:
        request: The ServiceRequest containing all parameters for the AI operation
        supabase: An authenticated Supabase client for database operations

    Returns:
        None: This function operates asynchronously and doesn't return a value
    """
    try:
        # Get cell data
        cell = await get_cell(
            supabase,
            CellId(
                column_id=input.column_id,
                row_id=input.row_id,
                table_id=input.table_id,
            ),
        )

        # Check formula condition
        if not await _handle_formula_condition(input, cell, supabase):
            return

        # Generate AI formula content
        try:
            cell.run_status = CellStatus(
                run="generating", message="Generating content..."
            )
            await send_realtime_broadcast(
                supabase,
                input.table_id,
                RealtimeEvent.CELL_UPDATE,
                cell.model_dump(),
            )
            await update_cell(supabase, cell)

            if settings.IS_STRESS_TESTING:
                # wait for 30 seconds
                await asyncio.sleep(30)
                response = AIMessage(content="Outbond rocks!")
            else:
                llm = load_chat_model(AI_FORMULA_MODEL)
                messages = [
                    SystemMessage(content=FORMULA_PROMPT),
                    HumanMessage(content=input.value.user_prompt),
                ]
                response = llm.invoke(messages)

            # Update cell with result
            cell.run_status = CellStatus(run="completed", message="Completed")
            cell.value = response.content
            await send_realtime_broadcast(
                supabase,
                input.table_id,
                RealtimeEvent.CELL_UPDATE,
                cell.model_dump(),
            )
            await update_cell(supabase, cell)
            await upsert_cell_details(
                supabase,
                CellDetails(
                    table_id=input.table_id,
                    column_id=input.column_id,
                    row_id=input.row_id,
                    value={"result": response.content},
                ),
            )

        except Exception as e:
            logger.error(f"Error generating or storing AI formula: {str(e)}")
            error_msg = f"AI formula generation error: {str(e)}"
            cell.run_status = CellStatus(run="failed", message=error_msg)
            await send_realtime_broadcast(
                supabase,
                input.table_id,
                RealtimeEvent.CELL_UPDATE,
                cell.model_dump(),
            )
            await update_cell(supabase, cell)

            await reimburse_tokens(supabase, input.organization_id, input.credits)

    except Exception as e:
        # Catch-all for any unexpected errors
        logger.error(f"Unexpected error in run_ai_formula: {str(e)}")

    return


async def run_research_agent(
    input: ServiceRequest,
    supabase: AsyncClient,
) -> None:
    """
    Process a research agent request, evaluating any formula conditions and running the research agent.

    This function handles the entire lifecycle of a research agent request:
    1. Evaluates any conditional formula to determine if processing should continue
    2. If condition fails, updates cell status and reimburses tokens
    3. If condition passes, runs the research agent and updates the cell with results
    4. Stores detailed results in the cell_details table

    Args:
        request: The ResearchAgentRequest containing all parameters for the research operation
        supabase: An authenticated Supabase client for database operations

    Returns:
        None: This function operates asynchronously and doesn't return a value
    """
    try:
        cell = await get_cell(
            supabase,
            CellId(
                column_id=input.column_id,
                row_id=input.row_id,
                table_id=input.table_id,
            ),
        )
        # Check formula condition
        if not await _handle_formula_condition(input, cell, supabase):
            return

        # Run research agent
        try:
            cell.run_status = CellStatus(
                run="generating", message="Starting research..."
            )
            await send_realtime_broadcast(
                supabase,
                input.table_id,
                RealtimeEvent.CELL_UPDATE,
                cell.model_dump(),
            )
            await update_cell(supabase, cell)

            inputs = {"messages": [HumanMessage(content=input.value.user_prompt)]}
            stream = graph.stream(inputs, stream_mode="values")
            final_output = None

            for s in stream:
                message = s["messages"][-1]

                # Handle tool messages (search, website reading, etc.)
                if isinstance(message, ToolMessage):
                    tool_name = message.name

                    # Find the corresponding tool call in previous messages
                    for prev_msg in s["messages"]:
                        if hasattr(prev_msg, "tool_calls"):
                            for tool_call in prev_msg.tool_calls:
                                if tool_call["name"] == tool_name:
                                    if tool_name == "search":
                                        query = extract_tool_arg(
                                            tool_call, ["__arg1", "query"]
                                        )
                                        cell.run_status = CellStatus(
                                            run="processing",
                                            message=f"🔍 Searching for: '{query}'",
                                        )
                                        await send_realtime_broadcast(
                                            supabase,
                                            input.table_id,
                                            RealtimeEvent.CELL_UPDATE,
                                            cell.model_dump(),
                                        )
                                        await update_cell(supabase, cell)

                                    elif tool_name == "scrape_website":
                                        url = extract_tool_arg(
                                            tool_call, ["__arg1", "url"]
                                        )
                                        domain = extract_domain(url)
                                        cell.run_status = CellStatus(
                                            run="processing",
                                            message=f"📄 Reading website: {domain}",
                                        )
                                        await send_realtime_broadcast(
                                            supabase,
                                            input.table_id,
                                            RealtimeEvent.CELL_UPDATE,
                                            cell.model_dump(),
                                        )
                                        await update_cell(supabase, cell)

                                    elif tool_name == "crawl_website":
                                        url = extract_tool_arg(
                                            tool_call, ["__arg1", "url"]
                                        )
                                        domain = extract_domain(url)
                                        cell.run_status = CellStatus(
                                            run="processing",
                                            message=f"🔬 Browsing website: {domain}",
                                        )
                                        await send_realtime_broadcast(
                                            supabase,
                                            input.table_id,
                                            RealtimeEvent.CELL_UPDATE,
                                            cell.model_dump(),
                                        )
                                        await update_cell(supabase, cell)

                                    break

                # Handle AI messages (thinking or final answer)
                elif isinstance(message, AIMessage):
                    if not hasattr(message, "tool_calls") or not message.tool_calls:
                        final_output = message.content
                        cell.run_status = CellStatus(
                            run="completed", message="Completed"
                        )
                        await send_realtime_broadcast(
                            supabase,
                            input.table_id,
                            RealtimeEvent.CELL_UPDATE,
                            cell.model_dump(),
                        )
                        await update_cell(supabase, cell)
                    else:
                        cell.run_status = CellStatus(
                            run="processing", message="🤔 Thinking..."
                        )
                        await send_realtime_broadcast(
                            supabase,
                            input.table_id,
                            RealtimeEvent.CELL_UPDATE,
                            cell.model_dump(),
                        )
                        await update_cell(supabase, cell)

            # Update with final output
            if final_output:
                cell.run_status = CellStatus(run="completed", message="Completed")
                cell.value = final_output
                await send_realtime_broadcast(
                    supabase,
                    input.table_id,
                    RealtimeEvent.CELL_UPDATE,
                    cell.model_dump(),
                )
                await update_cell(supabase, cell)
                await upsert_cell_details(
                    supabase,
                    CellDetails(
                        table_id=input.table_id,
                        column_id=input.column_id,
                        row_id=input.row_id,
                        value={
                            "result": final_output,
                        },
                    ),
                )

        except Exception as e:
            logger.error(f"Error generating or storing AI research: {str(e)}")
            error_msg = f"AI research generation error: {str(e)}"

            cell.run_status = CellStatus(run="failed", message=error_msg)
            await send_realtime_broadcast(
                supabase,
                input.table_id,
                RealtimeEvent.CELL_UPDATE,
                cell.model_dump(),
            )
            await update_cell(supabase, cell)

            await reimburse_tokens(supabase, input.organization_id, input.credits)

    except Exception as e:
        # Catch-all for any unexpected errors
        logger.error(f"Unexpected error in run_research_agent: {str(e)}")

    return
