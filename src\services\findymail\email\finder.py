from src.db.utils import get_type_id, get_type_name
import httpx
from src.core.config import get_settings
import asyncio
import logging
import os
from typing import Dict, Any

settings = get_settings()

logger = logging.getLogger(__name__)


def transform_email(src: Dict[str, Any]) -> list[Dict[str, Any]]:
    """Transform email data into a structured format"""

    result = [
        {
            "name": "Email",
            "type_id": get_type_id("email"),
            "type": get_type_name("email"),
            "is_brand": True,
            "value": src["email"],
        },
        {
            "name": "Source",
            "type_id": get_type_id("text"),
            "type": get_type_name("text"),
            "is_brand": False,
            "value": "Findymail",
        },
        {
            "name": "Findymail Email Status",
            "type_id": get_type_id("text"),
            "type": get_type_name("text"),
            "is_brand": False,
            "value": src["email_status"],
        },
    ]

    return result


async def get_email(
    input: Dict[str, Any],
) -> Dict[str, Any]:
    """Fetch phone number from the Prospeo API

    Returns:
        tuple: (PersonProfileOutput, credit_cost)s
    """
    try:
        if settings.IS_STRESS_TESTING:
            # wait for 30 seconds
            await asyncio.sleep(30)
            return {"success": True, "data": {"email": "<EMAIL>", "email_status": "valid"}, "credit_cost": 0}
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://app.findymail.com/api/search/name",
                json={
                    "name": input["full_name"],
                    "domain": input["company_domain"],
                },
                headers={
                    "Authorization": f"Bearer {settings.FINDYMAIL_API_KEY.get_secret_value()}",
                    "content-type": "application/json",
                    "accept": "application/json",
                },
                timeout=60.0,  # 60 second timeout
            )
            response.raise_for_status()

            # Extract credit cost from headers
            output = response.json()

            # Check if raw_format exists in the response
            if "contact" not in output:
                return {
                    "success": False,
                    "message": "Email not found",
                }

            return {
                "success": True,
                "data": {
                    "email": output["contact"]["email"],
                    "email_status": "Valid",
                },
                "credit_cost": 5,
            }
    except httpx.HTTPStatusError as e:
        if (
            e.response.status_code == 404
            or e.response.status_code == 400
            or e.response.status_code == 402
            or e.response.status_code == 423
        ):
            logger.info("Findymail Email Finder Not Found")
            return {"success": False, "message": "Email not found"}
        elif e.response.status_code == 429:
            logger.info("Findymail Email Finder Rate Limit")
            # Extract retry-after header if available
            retry_after = e.response.headers.get("Retry-After")
            return {
                "success": False,
                "message": "Rate limit exceeded. Try again later.",
                "rate-limit": True,
            }
        else:
            logger.error(
                f"Findymail Email Finder HTTP unhandled Error: {e.response.status_code}"
            )
            return {
                "success": False,
                "error": f"HTTP Error: {e.response.status_code}",
            }
    except Exception as e:
        logger.error(f"Findymail Email Finder Exception: {str(e)}")
        return {"success": False, "error": str(e)}
