from typing import Optional, Union, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, HttpUrl
from src.db.utils import Cell, CellId
from src.schemas.linkedin import LinkedinPersonProfileUrl, LinkedinCompanyProfileUrl
from src.schemas.perplexity import (
    PerplexityModel,
    PerplexitySearchMode,
    PerplexityReasoningEffort,
    PerplexitySearchContextSize,
)
from src.services.http.models import HTTPRequest


class ValidateColumnsInput(BaseModel):
    table_id: str


class LinkedinProfileRequest(BaseModel):
    linkedin_profile_url: LinkedinPersonProfileUrl


class LookupPersonProfileRequest(BaseModel):
    company_domain: str
    full_name: str


class LinkedinCompanyRequest(BaseModel):
    linkedin_company_url: LinkedinCompanyProfileUrl


class LookupCompanyRequest(BaseModel):
    company_domain: str


class AIColumnRequest(BaseModel):
    user_prompt: str
    system_prompt: Optional[str | None] = None


class PerplexityColumnRequest(BaseModel):
    model: PerplexityModel
    search_mode: Optional[PerplexitySearchMode | None] = PerplexitySearchMode.WEB
    reasoning_effort: Optional[PerplexityReasoningEffort | None] = (
        PerplexityReasoningEffort.LOW
    )
    max_tokens: Optional[int | None] = None
    temperature: Optional[float | None] = None
    top_p: Optional[float | None] = None
    search_domain_filter: Optional[list[str] | None] = None
    return_images: Optional[bool | None] = False
    return_related_questions: Optional[bool | None] = False
    search_recency_filter: Optional[str | None] = None
    search_after_date_filter: Optional[str | None] = None
    search_before_date_filter: Optional[str | None] = None
    last_updated_after_filter: Optional[str | None] = None
    last_updated_before_filter: Optional[str | None] = None
    top_k: Optional[float | None] = 0
    presence_penalty: Optional[float | None] = 0
    frequency_penalty: Optional[float | None] = 0
    disable_search: Optional[bool | None] = False
    enable_search_classifier: Optional[bool | None] = False
    user_prompt: str
    system_prompt: Optional[str | None] = None


class MobileFinderRequest(BaseModel):
    linkedin_profile_url: LinkedinPersonProfileUrl


class OutbondInvitationRequest(BaseModel):
    email: str
    display_name: str


class ServiceRequest(CellId):
    organization_id: str
    run_id: int
    service_id: int
    credits: float
    formula: Optional[str] = None
    providers: Optional[Dict[str, Any]] = None
    value: Union[
        LinkedinProfileRequest,
        LookupPersonProfileRequest,
        LinkedinCompanyRequest,
        LookupCompanyRequest,
        AIColumnRequest,
        PerplexityColumnRequest,
        MobileFinderRequest,
        OutbondInvitationRequest,
        HTTPRequest,
    ]


class ImportCSVInput(BaseModel):
    organization_id: str
    path: str


class ExportTableCSVInput(BaseModel):
    table_id: str
    organization_id: str
    download_id: int
    filters: Optional[Dict[str, Any]] = None
    search: Optional[str] = None
    sorts: Optional[list] = None


class NotifyFEInput(BaseModel):
    cell: Cell
