from src.core.config import get_settings
from src.db.optimised_utils import (
    get_cell,
    get_supabase,
    get_supabase_client,
    update_cell_and_notify,
    upsert_cell_details,
)
from src.schemas.requests import ServiceRequest
import src.services.leadmagic.mobile.finder as leadmagic
from src.services.leadmagic.mobile.schemas import (
    PhoneFinderResponse,
    PhoneFinderInput,
)
import src.services.leadmagic.email.finder as leadmagic_email_finder
from ..celery_app import app
import logging
from supabase import Client
import asyncio
from typing import Dict, Any, Optional, List, Union
from src.services.llm.generation import _handle_formula_condition

logger = logging.getLogger("src.worker.tasks.csv")

import os
import tempfile
import csv
import io
import json
from datetime import datetime
import pandas as pd
import logging
import psycopg2
from psycopg2.extras import execute_values, RealDictCursor
from supabase import AsyncClient
import aiofiles
import asyncio

# Configure logging
logger = logging.getLogger(__name__)

settings = get_settings()


# Direct PostgreSQL connection for COPY operations
DB_CONFIG = {
    "dbname": os.environ.get("POSTGRES_DB"),
    "user": os.environ.get("POSTGRES_USER"),
    "password": os.environ.get("POSTGRES_PASSWORD"),
    "host": os.environ.get("POSTGRES_HOST"),
    "port": os.environ.get("POSTGRES_PORT", 5432),
}


def process_table(headers: List[List]) -> Dict:
    """
    Process table headers to determine column types and table name.
    Same as in previous implementation.
    """
    table_name = f"imported_table_{pd.Timestamp.now().strftime('%Y%m%d%H%M%S')}"

    columns = []
    for i in range(len(headers[0])):
        # Simple type detection
        column_type = 1  # Default to string type
        columns.append({"type_id": column_type})

    return {"table_name": table_name, "columns": columns}


@app.task(
    name="src.worker.tasks.csv.run_insert_csv_file_task",
    bind=True,
    max_retries=1,
    time_limit=300,
)
def run_insert_csv_file_task(self, payload: Dict[str, str]) -> Dict[str, Any]:
    """
    Main task to process and insert CSV file data using PostgreSQL COPY.

    Args:
        payload: Dictionary containing file path and organization ID

    Returns:
        Dictionary with success status and message
    """

    async def _run_insert_csv_file_task(payload: Dict[str, str]) -> Dict[str, Any]:
        logger.info(f"Starting CSV file processing with pg_copy..., payload={payload}")

        supabase = await get_supabase_client()
        file_path = payload["path"]
        organization_id = payload["organization_id"]

        try:
            # Download the file from Supabase Storage
            response = await supabase.storage.from_(organization_id).download(file_path)
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_file_path = temp_file.name
                temp_file.write(response)  # Note: No .data attribute needed

            try:
                # Read header and first few rows for table processing
                headers_df = pd.read_csv(temp_file_path, nrows=6)
                headers = headers_df.values.tolist()
                headers.insert(0, headers_df.columns.tolist())

                # Get table metadata from headers
                table_metadata = process_table(headers)

                # Read the CSV file with pandas to validate
                df = pd.read_csv(temp_file_path)
                column_names = list(df.columns)

                # Validation
                if len(column_names) > 75:
                    raise Exception("CSV file has more than 75 columns.")
                if len(df) > 50000:
                    raise Exception("CSV file has more than 50,000 rows.")

                # Connect to PostgreSQL directly for COPY operations
                with psycopg2.connect(settings.POSTGRES_URL.get_secret_value()) as conn:
                    with conn.cursor() as cursor:
                        # Begin transaction
                        conn.autocommit = False

                        # 1. Insert table entry
                        cursor.execute(
                            """
                            INSERT INTO tables (organization_id, name)
                            VALUES (%s, %s) RETURNING id
                            """,
                            (organization_id, table_metadata["table_name"]),
                        )
                        table_id = cursor.fetchone()[0]

                        # 2. Insert columns in bulk
                        column_data = [
                            (
                                table_id,
                                table_metadata["columns"][i]["type_id"],
                                i + 1,
                                col,
                                i + 1,
                            )
                            for i, col in enumerate(column_names)
                        ]

                        execute_values(
                            cursor,
                            """
                            INSERT INTO columns (table_id, type_id, index, name, id)
                            VALUES %s
                            """,
                            column_data,
                        )

                        # 3. Create a temporary table matching the structure of 'rows' table
                        cursor.execute(
                            """
                            CREATE TEMP TABLE temp_rows (
                                id SERIAL,
                                table_id TEXT NOT NULL,
                                PRIMARY KEY (table_id, id)
                            ) ON COMMIT DROP
                            """
                        )

                        # 4. Insert rows using COPY
                        num_rows = len(df)
                        row_data = io.StringIO(
                            "\n".join([f"{i+1}\t{table_id}" for i in range(num_rows)])
                        )
                        cursor.copy_expert(
                            """
                            COPY temp_rows (id, table_id) FROM STDIN WITH DELIMITER E'\\t'
                            """,
                            row_data,
                        )

                        # 5. Insert from temp table to actual rows table
                        cursor.execute(
                            """
                            INSERT INTO rows (id, table_id)
                            SELECT id, table_id::text FROM temp_rows
                            """
                        )

                        # 6. Create a temp table for cells
                        cursor.execute(
                            """
                            CREATE TEMP TABLE temp_cells (
                                table_id TEXT NOT NULL,
                                row_id INTEGER NOT NULL,
                                column_id INTEGER NOT NULL,
                                value TEXT,
                                PRIMARY KEY (table_id, row_id, column_id)
                            ) ON COMMIT DROP
                            """
                        )

                        # 7. Prepare data for cells table using CSV writer to handle special characters
                        cell_data = io.StringIO()
                        csv_writer = csv.writer(
                            cell_data, 
                            delimiter='\t',
                            quotechar='"', 
                            quoting=csv.QUOTE_MINIMAL,
                            escapechar='\\',
                            lineterminator='\n'
                        )
                        
                        for row_idx, row in df.iterrows():
                            row_id = row_idx + 1
                            for col_idx, col_name in enumerate(column_names):
                                column_id = col_idx + 1
                                value = (
                                    str(row[col_name])
                                    if pd.notna(row[col_name])
                                    else ""
                                )
                                # Let the csv writer handle escaping
                                csv_writer.writerow([table_id, row_id, column_id, value])

                        cell_data.seek(0)

                        # 8. Copy cells data
                        cursor.copy_expert(
                            """
                            COPY temp_cells (table_id, row_id, column_id, value) 
                            FROM STDIN WITH (FORMAT CSV, DELIMITER E'\\t', QUOTE '"', ESCAPE '\\', NULL '')
                            """,
                            cell_data,
                        )

                        # 9. Insert from temp cells to actual cells table
                        cursor.execute(
                            """
                            INSERT INTO cells (table_id, row_id, column_id, value)
                            SELECT table_id, row_id, column_id, value FROM temp_cells
                            """
                        )

                        # 10. Create notification
                        cursor.execute(
                            """
                            INSERT INTO notifications (organization_id, table_id, message)
                            VALUES (%s, %s, %s)
                            """,
                            (
                                organization_id,
                                table_id,
                                f'Table "{table_metadata["table_name"]}" has been created from CSV, <a href="{settings.APP_URL}/private/workspace/{table_id}" target="_blank">Click to Open.',
                            ),
                        )

                        # Commit the transaction
                        conn.commit()

                        logger.info(
                            f"Successfully loaded {num_rows} rows and {num_rows * len(column_names)} cells using COPY."
                        )

                return {
                    "success": True,
                    "message": "File processed successfully with pg_copy.",
                    "table_id": table_id,
                }

            finally:
                # Clean up temp file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)

        except Exception as e:
            logger.error(f"Error processing file: {str(e)}")
            self.retry(exc=e, countdown=5, max_retries=3)
            return {"success": False, "message": str(e)}

    return asyncio.run(_run_insert_csv_file_task(payload))


@app.task(
    name="src.worker.tasks.csv.run_export_table_to_csv_task",
    bind=True,
    max_retries=1,
    time_limit=300,
)
def run_export_table_to_csv_task(self, payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Task to export table data to CSV and upload it to Supabase storage.

    Args:
        payload: Dictionary containing:
            - table_id: ID of the table to export
            - organization_id: Organization ID for storage bucket
            - download_id: ID of the download record to update
            - filters: Optional filters to apply to the data
            - search: Optional search term
            - sorts: Optional sort specifications

    Returns:
        Dictionary with success status, file path, download URL, and row count
    """

    async def _run_export_table_to_csv_task(payload: Dict[str, Any]) -> Dict[str, Any]:
        logger.info(f"Starting table export to CSV task, payload={payload}")

        table_id = payload["table_id"]
        organization_id = payload["organization_id"]
        download_id = payload["download_id"]
        filters = payload.get("filters")
        search = payload.get("search")
        sorts = payload.get("sorts")

        try:
            # Connect to PostgreSQL to get table data
            with psycopg2.connect(
                settings.POSTGRES_URL.get_secret_value(), cursor_factory=RealDictCursor
            ) as conn:
                with conn.cursor() as cursor:
                    # Get table name for the file
                    cursor.execute("SELECT name FROM tables WHERE id = %s", (table_id,))
                    table_info = cursor.fetchone()
                    if not table_info:
                        raise Exception(f"Table with ID {table_id} not found")

                    table_name = table_info["name"]
                    logger.info(f"Table name: {table_name}")

                    # Call the new RPC to get table data for export
                    cursor.execute(
                        "SELECT * FROM get_table_for_export(%s, %s, %s, %s)",
                        (
                            table_id,
                            json.dumps(filters) if filters else None,
                            search,
                            json.dumps(sorts) if sorts else None,
                        ),
                    )

                    export_result = cursor.fetchone()
                    if not export_result or not export_result.get(
                        "get_table_for_export"
                    ):
                        raise Exception("No data found for export")
                    logger.info(f"Export result: {export_result}")
                    table_data = export_result["get_table_for_export"]
                    logger.info(f"Table data: {table_data}")
                    logger.info(
                        f"Retrieved table data for export with {len(table_data)} rows (including header)"
                    )

                    if len(table_data) < 1:
                        raise Exception("No header row found in export data")

                    # First row is the header - ensure all values are properly converted to strings
                    headers = [str(h) if h is not None else "" for h in table_data[0]]

                    # Remaining rows are the data - ensure all cell values are properly handled
                    data_rows = []
                    for row in table_data[1:] if len(table_data) > 1 else []:
                        # Convert any None values to empty strings and ensure all values are strings
                        processed_row = [
                            str(cell) if cell is not None else "" for cell in row
                        ]
                        data_rows.append(processed_row)

                    logger.info(f"Found {len(data_rows)} data rows to export")

                    # Create CSV directly using the csv module instead of pandas
                    csv_buffer = io.StringIO()
                    csv_writer = csv.writer(csv_buffer, quoting=csv.QUOTE_MINIMAL)
                    # Write headers and data rows
                    csv_writer.writerow(headers)
                    csv_writer.writerows(data_rows)

                    # Get the CSV content
                    csv_data = csv_buffer.getvalue()

            # Generate filename with timestamp
            timestamp = datetime.now().isoformat().replace(":", "-").replace(".", "-")
            sanitized_name = table_name.replace(" ", "_")
            filename = f"{table_id}/{sanitized_name}-{timestamp}.csv"

            # Upload to Supabase Storage
            supabase = await get_supabase_client()
            response = await supabase.storage.from_(organization_id).upload(
                filename,
                csv_data.encode("utf-8"),
                {"content-type": "text/csv", "cacheControl": "3600", "upsert": "true"},
            )

            if not response or not hasattr(response, "path"):
                raise Exception("Failed to upload CSV file to storage")

            path = response.path

            # Generate download URL
            encoded_path = path.replace("/", "%2F")
            download_url = f"{settings.APP_URL}/private/download?path={encoded_path}&bucket={organization_id}"

            # Update download URL in table_download
            await supabase.table("table_download").update({"url": download_url}).eq(
                "table_id", table_id
            ).eq("id", download_id).execute()

            logger.info(f"Successfully exported table {table_id} to CSV: {filename}")

            return {
                "success": True,
                "filename": filename,
                "path": path,
                "downloadUrl": download_url,
                "rowCount": len(data_rows),
            }

        except Exception as e:
            logger.error(f"Error exporting table to CSV: {str(e)}")
            # self.retry(exc=e, countdown=5, max_retries=3)
            return {"success": False, "message": str(e)}

    return asyncio.run(_run_export_table_to_csv_task(payload))
