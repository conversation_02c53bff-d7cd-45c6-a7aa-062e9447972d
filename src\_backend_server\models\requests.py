import re
from typing import Optional
from pydantic import BaseModel, Field, validator


class ServiceRequest(BaseModel):
    organization_id: str
    table_id: str
    row_id: int
    column_id: int
    run_id: int
    service_id: int
    credits: int
    formula: str | None = None


class AIColumnRequest(ServiceRequest):
    user_prompt: str
    system_prompt: Optional[str | None] = None


class FormulaRequest(ServiceRequest):
    user_prompt: str
    system_prompt: Optional[str | None] = None


class ResearchAgentRequest(ServiceRequest):
    user_prompt: str


class LinkedinProfileRequest(ServiceRequest):
    linkedin_profile_url: str = Field(..., description="LinkedIn profile URL")

    @validator("linkedin_profile_url")
    def validate_linkedin_url(cls, v):
        """Validate and normalize LinkedIn profile URL."""
        # Basic validation
        if not v or not isinstance(v, str):
            raise ValueError("LinkedIn profile URL must be a non-empty string")

        # Strip whitespace
        v = v.strip()

        # Remove protocol if present
        v = re.sub(r"^https?://", "", v)

        # Define the pattern for valid LinkedIn profile URLs
        # Format: [subdomain.]linkedin.com/in/username
        linkedin_pattern = r"^(?:[a-z0-9-]+\.)?linkedin\.com/in/[a-zA-Z0-9_-]+$"

        # Extract username if URL is not in the standard format
        if not re.match(linkedin_pattern, v):
            # Check if it's a LinkedIn domain at least
            if not ("linkedin.com" in v):
                raise ValueError("URL must be from linkedin.com domain")

            # Try to extract username from non-standard format
            username_match = re.search(r"linkedin\.com/.*?([\w-]+)/?$", v)
            if username_match:
                username = username_match.group(1)
                v = f"linkedin.com/in/{username}"
            else:
                raise ValueError("Could not determine username from URL")

        # Normalize to remove any subdomains
        v = re.sub(r"^[^.]*\.linkedin\.com", "linkedin.com", v)

        # Remove trailing slash
        v = v.rstrip("/")

        # No protocol addition as requested

        return v
