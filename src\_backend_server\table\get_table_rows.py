import json
import time
import logging
import base64
import asyncio
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, BackgroundTasks
from typing import Dict, Any, List, Optional

# Configure logging
logger = logging.getLogger("backend_api.server")

async def get_table_rows_handler(
    request: Dict[str, Any],
    redis: Any,
    pg_pool: Any,
    background_tasks: BackgroundTasks
):
    try:
        start_time = time.time()

        # Define TTL in seconds (15 minutes)
        CACHE_TTL = 15 * 60
        
        # Extract request parameters
        table_id = request["target_table_id"]
        filter_json = request.get("filter_json")
        search_text = request.get("search_text")
        sort_array = request.get("sort_array")
        limit = request.get("p_limit", 100)
        offset = request.get("p_offset", 0)
        
        # Create a simpler cache key to reduce overhead
        page_key = f"p:{table_id}:{offset}:{limit}"
        
        # Try to get the entire page result from cache first (fastest path)
        t0 = time.time()
        try:
            # Use a pipeline to reduce network round-trips
            pipe = redis.pipeline()
            pipe.exists(page_key)
            pipe.get(page_key)
            pipe.expire(page_key, CACHE_TTL)
            results = await pipe.execute()
            
            exists = results[0]
            if exists:
                page_cache = results[1]
                
                if page_cache:
                    # Decode the JSON string (base64 encoded to avoid UTF-8 issues)
                    json_str = base64.b64decode(page_cache).decode('utf-8')
                    result = json.loads(json_str)
                    
                    cache_time = (time.time() - t0) * 1000
                    logger.info(
                        f"Cache HIT in {cache_time:.1f}ms",
                        extra={
                            "cache_hit": True,
                            "table_id": table_id,
                            "latency_ms": cache_time
                        }
                    )
                    
                    # Preload next pages in the background
                    background_tasks.add_task(
                        preload_future_pages,
                        redis=redis,
                        pg_pool=pg_pool,
                        table_id=table_id,
                        filter_json=filter_json,
                        search_text=search_text,
                        sort_array=sort_array,
                        current_offset=offset,
                        limit=limit,
                        cache_ttl=CACHE_TTL,
                        pages_ahead=2  # Cache 2 pages ahead
                    )
                    
                    return {
                        "data": result["data"],
                        "total_count": result["total_count"],
                        "from_cache": True
                    }
        except Exception as e:
            logger.error(f"Cache error: {str(e)}")
            try:
                await redis.delete(page_key)
            except:
                pass
        
        # Cache miss - fetch from database
        logger.info(f"Cache MISS for {page_key}")
        
        # Fetch from database
        t0 = time.time()
        async with pg_pool.acquire() as conn:
            rows = await conn.fetch(
                """
                SELECT * FROM get_table_rows(
                    target_table_id => $1::text,
                    filter_json => $2::jsonb,
                    search_text => $3::text,
                    sort_array => $4::jsonb,
                    p_limit => $5::int,
                    p_offset => $6::int
                );
                """,
                table_id,
                filter_json and json.dumps(filter_json, separators=(',', ':')),
                search_text,
                sort_array and json.dumps(sort_array, separators=(',', ':')),
                limit,
                offset
            )
            
            if not rows:
                return {
                    "data": [],
                    "total_count": 0,
                    "from_cache": False
                }
            
            # Process the result
            result = rows[0]['get_table_rows']
            if isinstance(result, str):
                result = json.loads(result)
            
            db_time = (time.time() - t0) * 1000
            logger.info(f"DB fetch completed in {db_time:.1f}ms")
            
            # Format the result
            formatted_result = {
                "data": result.get("data", []),
                "total_count": result.get("total_count", 0),
                "from_cache": False
            }
            
            # Store in cache immediately (not in background)
            try:
                await store_in_cache(
                    redis=redis,
                    page_key=page_key,
                    data=formatted_result["data"],
                    total_count=formatted_result["total_count"],
                    cache_ttl=CACHE_TTL
                )
            except Exception as e:
                logger.error(f"Error storing in cache: {str(e)}")
            
            # Preload next pages in the background
            background_tasks.add_task(
                preload_future_pages,
                redis=redis,
                pg_pool=pg_pool,
                table_id=table_id,
                filter_json=filter_json,
                search_text=search_text,
                sort_array=sort_array,
                current_offset=offset,
                limit=limit,
                cache_ttl=CACHE_TTL,
                pages_ahead=2  # Cache 2 pages ahead
            )
            
            # Log request completion
            end_time = time.time()
            latency_ms = (end_time - start_time) * 1000
            logger.info(
                f"Request completed in {latency_ms:.0f}ms",
                extra={"latency_ms": latency_ms}
            )
            
            return formatted_result

    except Exception as e:
        logger.error(f"Error in get_table_rows: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

async def store_in_cache(
    redis: Any,
    page_key: str,
    data: List[Dict],
    total_count: int,
    cache_ttl: int
):
    """Store a page result in Redis cache using base64 encoding to avoid UTF-8 issues"""
    try:
        # Create the page result
        page_result = {
            "data": data,
            "total_count": total_count
        }
        
        # Convert to JSON and encode as base64 to avoid UTF-8 issues
        # Use a more compact JSON representation to reduce size
        json_str = json.dumps(page_result, separators=(',', ':'))
        encoded_data = base64.b64encode(json_str.encode('utf-8'))
        
        # Store in Redis as binary data
        await redis.set(page_key, encoded_data, ex=cache_ttl)
        
        logger.info(
            f"Stored in cache ({len(encoded_data)} bytes)",
            extra={"page_key": page_key}
        )
        return True
    except Exception as e:
        logger.error(f"Error storing in cache: {str(e)}")
        return False

async def preload_future_pages(
    redis: Any,
    pg_pool: Any,
    table_id: str,
    filter_json: Optional[Dict],
    search_text: Optional[str],
    sort_array: Optional[List],
    current_offset: int,
    limit: int,
    cache_ttl: int,
    pages_ahead: int = 2
):
    """Preload future pages into cache to improve user experience"""
    try:
        # Create a list of offsets for pages to preload
        offsets_to_preload = [current_offset + (limit * i) for i in range(1, pages_ahead + 1)]
        
        for next_offset in offsets_to_preload:
            # Create the page key
            next_page_key = f"p:{table_id}:{next_offset}:{limit}"
            
            # Check if already cached
            exists = await redis.exists(next_page_key)
            if exists:
                # Already cached, just extend TTL
                await redis.expire(next_page_key, cache_ttl)
                logger.info(f"Extended TTL for cached page: {next_page_key}")
                continue
            
            # Not cached, fetch from database and cache
            logger.info(f"Preloading page: {next_page_key}")
            
            try:
                async with pg_pool.acquire() as conn:
                    rows = await conn.fetch(
                        """
                        SELECT * FROM get_table_rows(
                            target_table_id => $1::text,
                            filter_json => $2::jsonb,
                            search_text => $3::text,
                            sort_array => $4::jsonb,
                            p_limit => $5::int,
                            p_offset => $6::int
                        );
                        """,
                        table_id,
                        filter_json and json.dumps(filter_json, separators=(',', ':')),
                        search_text,
                        sort_array and json.dumps(sort_array, separators=(',', ':')),
                        limit,
                        next_offset
                    )
                    
                    if rows:
                        # Process the result
                        result = rows[0]['get_table_rows']
                        if isinstance(result, str):
                            result = json.loads(result)
                        
                        # Store in cache
                        await store_in_cache(
                            redis=redis,
                            page_key=next_page_key,
                            data=result.get("data", []),
                            total_count=result.get("total_count", 0),
                            cache_ttl=cache_ttl
                        )
                        
                        logger.info(f"Successfully preloaded page: {next_page_key}")
            except Exception as e:
                logger.error(f"Error preloading page {next_page_key}: {str(e)}")
            
            # Add a small delay between preloads to avoid overwhelming the DB
            await asyncio.sleep(0.1)
            
    except Exception as e:
        logger.error(f"Error in preload_future_pages: {str(e)}") 