from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException
import logging
import asyncio
from functools import partial

from src.security.auth import get_current_user
from src.backend_server.models.requests import (
    AIColumnRequest,
    FormulaRequest,
    ResearchAgentRequest,
    LinkedinProfileRequest,
)
from src.backend_server.services.llm import run_ai_column
from src.backend_server.tasks.openai import run_openai_task
from src.backend_server.services.proxycurl import enrich_linkedin_profile

# Configure logging
logger = logging.getLogger("backend_api.services")

# Create router with dependency injection for user authentication
router = APIRouter(
    prefix="/services",
    tags=["services"],
)


@router.post("/research")
def research(
    request: ResearchAgentRequest,
    background_tasks: BackgroundTasks,
    user: dict = Depends(get_current_user),
):
    """Process a research request using AI"""
    try:
        # Convert the request to a dictionary for Celery task

        # Run the Celery task in a separate thread to avoid blocking
        run_openai_task.delay(request)

        return {
            "status": "queued",
            "message": f"Research request queued for processing",
        }
    except Exception as e:
        logger.error(f"Error in research endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ai-column")
def ai_column(
    request: AIColumnRequest,
    background_tasks: BackgroundTasks,
    user: dict = Depends(get_current_user),
):
    """Process a research request using AI"""
    try:

        # Run the Celery task in a separate thread to avoid blocking
        run_openai_task.delay(request)

        return {
            "status": "queued",
            "message": f"Research request queued for processing",
        }
    except Exception as e:
        logger.error(f"Error in research endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ai-formula")
def ai_formula(
    request: FormulaRequest,
    background_tasks: BackgroundTasks,
    user: dict = Depends(get_current_user),
):
    """Process a formula request using AI"""
    try:

        # Run the Celery task in a separate thread to avoid blocking
        run_openai_task.delay(request)

        return {
            "status": "queued",
            "message": f"Formula request queued for processing",
        }
    except Exception as e:
        logger.error(f"Error in formula endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/linkedin-profile")
async def linkedin_profile(
    request: LinkedinProfileRequest,
    background_tasks: BackgroundTasks,
    user: dict = Depends(get_current_user),
):
    """Process a LinkedIn profile request using ProxyCurl API"""
    try:
        await enrich_linkedin_profile(request)

        return {
            "status": "queued",
            "message": f"LinkedIn profile request queued for processing",
        }
    except Exception as e:
        logger.error(f"Error in LinkedIn profile endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
