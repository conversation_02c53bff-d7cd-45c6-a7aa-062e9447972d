

from typing import List
from bond_ai.state import BondAIWorkflowState
from langchain_core.messages import SystemMessage, HumanMessage, RemoveMessage
from langchain_core.runnables import RunnableConfig
from langchain_openai import ChatOpenAI
from langchain_core.messages import AIMessage
import uuid



## TODO. 
# We can introduce pinned messages, that we don't summarize neither delete. Decision should be based on use cases and outcome quality.
# Trigger when total (non-pinned, non-summary) messages ≥ THRESHOLD_TOTAL (e.g., 40)
# Summarize the oldest CHUNK_TO_SUMMARIZE_N (e.g., 20)
# Insert/replace a single running summary SystemMessage
# Delete just those N messages you summarized
# Preserve any pinned messages (e.g., your global system/preamble)

# This is a first basic implementation


async def summarize_conversation(state: BondAIWorkflowState):
    """Create a summary of older nodes based on the THRESHOLD value. After remove the old messages. """
    
    
    
    summarization_model = ChatOpenAI(model="gpt-4o-mini").bind(max_tokens=128)
    #TODO we have two options:
    # 1) Use the summary in the state, then need to review the prompt templates
    # 2) use a system message to inject the summary, then it's injected as messages based on the prompt template
    
    messages = state["messages"]
    message_count = len(messages)

    print(f"[DEBUG] summarize_conversation: {message_count} messages" )
    THRESHOLD = 400000
    if message_count < THRESHOLD:
        id = f"do-not-render-{str(uuid.uuid4())}"
        
        return {
            "messages": [AIMessage(content="[do-not-render-] No messages to summarize.", id=id)],
        }


    # Pull prior summary (either stored in state or as a SystemMessage)
    prev_summary_msg = next(
        (m for m in messages
         if isinstance(m, SystemMessage)
         and getattr(m, "additional_kwargs", {}).get("is_summary")),
        None
    )
    prev_summary_text = state.get("summary", "")
    running_summary = prev_summary_msg.content if prev_summary_msg else prev_summary_text

    ## put all in the system prompt when we load on Langsmith
    if running_summary:
        prompt = HumanMessage(
            content=(
                f"Running summary so far:\n{running_summary}\n\n"
                "Update the summary to include the new messages above. "
                "Be concise; capture key facts, decisions, open TODOs."
            )
        )
    else:
        prompt = HumanMessage(
            content="Create a concise summary of the conversation above: key facts, decisions, open TODOs."
        )



     # Call your summarizer
    response = summarization_model.ainvoke(messages + [prompt])
    new_summary = response.content.strip()
    print(f"[DEBUG] Summary: {new_summary}")
    
   # Prepare a fresh SystemMessage with the summary
    summary_msg = SystemMessage(
        content=f"<conversation_summary>\n{new_summary}\n</conversation_summary>",
        additional_kwargs={"is_summary": True}
    )
    
    
    # We don't delete, just create the summary
    # Deletions: remove old summary + all but last 2 non-summary messages
    # deletions: List[RemoveMessage] = []
    # # Remove any previous summary message(s)
    # for m in messages:
    #     if getattr(m, "additional_kwargs", {}).get("is_summary"):
    #         deletions.append(RemoveMessage(id=m.id))
    # # Keep last 2 conversational messages (ignore summary messages)
    # non_summary = [m for m in messages if not getattr(m, "additional_kwargs", {}).get("is_summary")]
    # deletions.extend(RemoveMessage(id=m.id) for m in non_summary[:-2])
    
    
    return {
        "summary": new_summary,
        "messages": [*deletions],
    }
