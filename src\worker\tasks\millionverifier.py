from src.db.optimised_utils import (
    get_cell,
    get_supabase_client,
    update_cell_and_notify,
    upsert_cell_details,
    append_cell_details,
)
import src.services.millionverifier.email.verify.single as millionverifier
from ..celery_app import app
import logging
from supabase import AsyncClient
import asyncio
from typing import Dict, Any, Optional

logger = logging.getLogger("src.worker.tasks.millionverifier")


async def run_millionverifier_email_finder(
    previous: Optional[Dict[str, Any]], input: Dict[str, Any]
):
    # Check if previous exists and has success=True OR if it has only_if=False
    if previous and (
        previous.get("success", False) is False
        or previous.get("only_if", True) is False
        or previous.get("validated", False) is True
    ):
        return previous

    supabase_client: AsyncClient = await get_supabase_client()
    cell = await get_cell(
        supabase_client,
        input,
    )
    cell["run_status"] = {
        "run": "processing",
        "message": "Verifying Email...",
    }
    # Initialize extras if it doesn't exist
    if "extras" not in cell or cell["extras"] is None:
        cell["extras"] = {}
    cell["extras"].update({
        "emoji": "🔍",
        "tooltip": "Verifying Email...",
    })
    await update_cell_and_notify(
        supabase_client,
        cell,
    )
    response = await millionverifier.verify_email(previous["data"])
    if response["success"]:
        # Transform the data into the structured format
        cell_details = response["data"]
        # Add email_valid to extras
        cell["extras"]["email_valid"] = response["success"]
        cell["run_status"] = {"run": "completed", "message": "Completed"}
        cell["extras"].update({
            "emoji": "✅",
            "tooltip": "Email Valid!",
        })
        await update_cell_and_notify(supabase_client, cell)
        await append_cell_details(supabase_client, input, cell_details)
        
    else:
        cell["run_status"] = {"run": "completed", "message": "Completed"}
        cell["extras"].update({
            "emoji": "❌",
            "tooltip": "Email Invalid!",
        })
        await update_cell_and_notify(supabase_client, cell)
    if previous.get("result_found", False) is True:
        response["result_found"] = True
    return response


@app.task(
    name="src.worker.tasks.millionverifier.run_millionverifier_task",
    max_retries=1,
    ignore_result=False,
    queue="enrichment",
    rate_limit="400/m",
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_kwargs={"max_retries": 1},
)
def run_millionverifier_task(previous: Optional[Dict[str, Any]], input: Dict[str, Any]):
    """
    Celery task that runs the async function using asyncio.

    Args:
        self: The task instance (provided by bind=True)
        input_dict: Dictionary representation of the ServiceRequest
    """
    return asyncio.run(run_millionverifier_email_finder(previous, input))
