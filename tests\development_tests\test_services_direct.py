"""
Direct test script for the services routes in the backend server.

This script directly tests the /services/research and /services/formula endpoints
by making HTTP requests to a running FastAPI server.
"""

import os
import json
import httpx
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Base URL for the API - using the running server on port 2024
BASE_URL = "http://localhost:2024"

# Test data
RESEARCH_REQUEST = {
    "organization_id": "org_64bd7f51c1450d62",
    "table_id": "tbl_3655e99e74169376",
    "row_id": 16,
    "column_id": 7,
    "run_id": 1,
    "service_id": 13,  # Research service ID
    "credits": 10,
    "formula": "x > 10",
    "user_prompt": "Research the latest trends in AI",
    "system_prompt": "You are a helpful research assistant",
}

FORMULA_REQUEST = {
    "organization_id": "org_64bd7f51c1450d62",
    "table_id": "tbl_3655e99e74169376",
    "row_id": 16,
    "column_id": 7,
    "run_id": 1,
    "service_id": 14,  # Formula service ID
    "credits": 10,
    "formula": "10 > 5",
    "user_prompt": "Calculate the ROI for this investment",
    "system_prompt": "You are a helpful financial assistant",
}

# Get the API token from environment variables or set it manually
# You can replace this with your actual token
API_TOKEN = os.getenv("SUPABASE_SERVICE_ROLE_KEY", "your-api-token")

# Headers for the requests
HEADERS = {"Authorization": f"Bearer {API_TOKEN}", "Content-Type": "application/json"}


async def test_research_endpoint():
    """Test the /services/research endpoint."""
    print("\n=== Testing Research Endpoint ===")

    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{BASE_URL}/services/research",
                json=RESEARCH_REQUEST,
                headers=HEADERS,
                timeout=10.0,
            )

            print(f"Status Code: {response.status_code}")
            print(f"Response: {response.json()}")

            if response.status_code == 200:
                print("✅ Research endpoint test passed!")
            else:
                print("❌ Research endpoint test failed!")

        except Exception as e:
            print(f"❌ Error testing research endpoint: {str(e)}")


async def test_formula_endpoint():
    """Test the /services/formula endpoint."""
    print("\n=== Testing Formula Endpoint ===")

    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{BASE_URL}/services/formula",
                json=FORMULA_REQUEST,
                headers=HEADERS,
                timeout=10.0,
            )

            print(f"Status Code: {response.status_code}")
            print(f"Response: {response.json()}")

            if response.status_code == 200:
                print("✅ Formula endpoint test passed!")
            else:
                print("❌ Formula endpoint test failed!")

        except Exception as e:
            print(f"❌ Error testing formula endpoint: {str(e)}")


async def main():
    """Run all tests."""
    print("Starting services routes tests...")

    # Test the research endpoint
    # await test_research_endpoint()

    # Test the formula endpoint
    await test_formula_endpoint()

    print("\nAll tests completed!")


if __name__ == "__main__":
    # Run the tests
    asyncio.run(main())
