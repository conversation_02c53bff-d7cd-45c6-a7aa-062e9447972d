from typing import List
import os
import sys
import importlib.util
import pytest

# Require real dependencies instead of mocks; skip if they aren't installed
pytest.importorskip("langchain_core.tools")
pytest.importorskip("langgraph.config")

# Load the module directly to avoid importing the entire tools package
TESTS_DIR = os.path.dirname(__file__)
print(f"TESTS_DIR: {TESTS_DIR}")
MODULE_PATH = os.path.abspath(os.path.join(
    TESTS_DIR, "..",  "tools", "vector_stores.py"
))

spec = importlib.util.spec_from_file_location("vector_store_semantic_search", MODULE_PATH)
module = importlib.util.module_from_spec(spec)
assert spec and spec.loader
# Ensure module is discoverable during dataclass processing
sys.modules[spec.name] = module  # type: ignore[arg-type]
spec.loader.exec_module(module)  # type: ignore[union-attr]

# Import both the vector stores and the new unified components
SearchInput = module.SearchInput  # type: ignore[attr-defined]
vector_store_semantic_search = module.vector_store_semantic_search  # type: ignore[attr-defined]


## HOW TO EXECUTE AND DISPLAY THE PRINT STATEMENTS

## pytest src\agents\bond_ai\tests\test_semantic_search_integration.py -v -s


class TestUnifiedSearchTool:
    """Test the new unified search tool."""
    
    
    def test_vector_store_returns_job_titles_for_job_queries(self):
   
        queries: List[str] = [
            "software engineer",
            "data scientist",
            "product manager",
            "marketing specialist",
            "sales representative",
            "graphic designer",
            "project manager",
            "business analyst"
        ]

        for q in queries:
            search_input = {
                "source": "job_title",
                "query": q,
                "top_k": 3
            }
            results = vector_store_semantic_search.invoke(search_input)
            assert len(results) == 3
            print(f"Query job title: {q} -> {results}")
            
    def test_vector_store_returns_industries_for_industry_queries(self):
        """Test unified search with Pydantic model for domain industries."""
        queries: List[str] = [
            "tech companies",
            "technology",
            "software companies",
            "saas industry",
            "cloud computing"
        ]

        for query in queries:
            # Test with Pydantic model
            search_input = {
                "source": "industry",
                "query": query,
                "top_k": 3
            }
             
            results = vector_store_semantic_search.invoke(search_input)
            
            assert len(results) == 3
            assert all(isinstance(result, str) for result in results)
            print(f" Industry Query: {query} -> {results}")

    def test_unified_search_with_pydantic_input_job_titles(self):
        """Test unified search with Pydantic model for job titles."""
        try:
            queries: List[str] = [
                "software engineer",
                "data scientist", 
                "product manager",
                "marketing director"
            ]

            for query in queries:
                # Test with Pydantic model
                search_input ={
                    "source": "job_title",
                    "query": query,
                    "top_k": 5
                }
                    
                results = vector_store_semantic_search.invoke(search_input)
                
                assert len(results) == 5
                assert all(isinstance(result, str) for result in results)
                print(f"Job Title Query: {query} -> {results}")

        except FileNotFoundError:
            pytest.skip("Job titles CSV file not found")

    def test_search_input_validation(self):
        """Test that SearchInput validates inputs correctly."""
        
        # Test valid inputs
        valid_input = {
            "source": "industry",
            "query": "technology",
            "top_k": 3
        }
            
        assert valid_input["source"] == "industry"
        assert valid_input["query"] == "technology"
        assert valid_input["top_k"] == 3

        # Test invalid source
        with pytest.raises(ValueError):
            vector_store_semantic_search.invoke({
                "source": "invalid_source",
                "query": "test",
                "top_k": 3
            })

        # Test empty query
        with pytest.raises(ValueError, match="Query cannot be empty or just whitespace"):
            vector_store_semantic_search.invoke({
                "source": "industry",
                "query": "   ",
                "top_k": 3
            })

        # Test invalid top_k (too low)
        with pytest.raises(ValueError):
            vector_store_semantic_search.invoke({
                "source": "industry",
                "query": "test",
                "top_k": 0
            })

        # Test invalid top_k (too high)
        with pytest.raises(ValueError):
            vector_store_semantic_search.invoke({
                "source": "industry",
                "query": "test",
                "top_k": 25
            })

        # Test query whitespace stripping
        input_with_whitespace = {
            "source": "industry",
            "query": "  technology  ",
            "top_k": 3
        }
            
        # Note: whitespace stripping happens during validation, so we can't test it this way
        # This test would need to actually invoke the tool to see the stripped result

    def test_unified_search_error_handling(self):
        """Test error handling in unified search."""
        
        # This should never happen due to Pydantic validation, but test the function directly
        # by creating an invalid SearchInput manually (bypassing validation)
        class MockSearchInput:
            def __init__(self, source, query, top_k):
                self.source = source
                self.query = query
                self.top_k = top_k

        invalid_input = MockSearchInput("invalid_source", "test query", 3)
        
        with pytest.raises(ValueError, match="Input should be 'industry' or 'job_title'"):
            vector_store_semantic_search.invoke({"source": invalid_input.source, "query": invalid_input.query, "top_k": invalid_input.top_k})  # type: ignore


# Performance and integration tests
class TestIntegration:
    """Integration tests for the complete system."""
    
    def test_different_top_k_values(self):
        """Test that different top_k values work correctly."""
        query = "technology"
        
        for k in [1, 3, 5, 10]:
            search_input ={
                "source": "industry",
                "query": query,
                "top_k": k
            }  
            results = vector_store_semantic_search.invoke(search_input)
            assert len(results) == k
            print(f"Top-{k} results for '{query}': {len(results)} items")

    def test_mixed_search_workflow(self):
        """Test a realistic workflow using both search types."""
        # First, search for a business industry
        industry_input = {
            
            "source": "industry",
            "query": "technology startup",
            "top_k": 2
        }
            
        industries = vector_store_semantic_search.invoke(industry_input)
        assert len(industries) == 2
        print(f"Found categories: {industries}")
        
        # Then search for relevant job titles
        try:
            job_input = {
                "source": "job_title",
                "query": "software engineer",
                "top_k": 3
            }
            
            job_titles = vector_store_semantic_search.invoke(job_input)
            assert len(job_titles) == 3
            print(f"Found job titles: {job_titles}")
            
            # Verify we got results for both searches
            assert industries and job_titles
            
        except FileNotFoundError:
            pytest.skip("Job titles file not available for mixed workflow test")






