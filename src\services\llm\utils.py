"""
Utility functions for LLM services.
"""

import json
from typing import Dict, Any, List, Optional
from urllib.parse import urlparse


def extract_domain(url: str) -> str:
    """
    Extract just the domain from a URL.

    Args:
        url: The URL to extract domain from

    Returns:
        str: The domain or original URL if parsing fails
    """
    try:
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
        return domain if domain else url
    except:
        return url


def extract_tool_arg(
    tool_call: Dict[str, Any], arg_names: Optional[List[str]] = None
) -> str:
    """
    Extract argument from a tool call with flexible argument naming.

    Args:
        tool_call: The tool call dictionary
        arg_names: List of possible argument names to check

    Returns:
        str: The extracted argument value or "unknown" if not found
    """
    if arg_names is None:
        arg_names = ["__arg1", "query", "url"]

    args = tool_call["args"]

    # Handle dictionary arguments
    if isinstance(args, dict):
        for name in arg_names:
            if name in args:
                return args[name]

    # Handle string arguments (JSON)
    elif isinstance(args, str):
        try:
            args_dict = json.loads(args)
            for name in arg_names:
                if name in args_dict:
                    return args_dict[name]
        except:
            return args

    return "unknown"
