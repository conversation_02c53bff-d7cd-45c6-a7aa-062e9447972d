
import logging
from bond_ai.state import BondAIWorkflowState
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import AIMessage,SystemMessage
import json
import re
from datetime import datetime
import uuid
from bond_ai.configuration import Configuration
from bond_ai.prompts_v1 import SUPERVISOR_AGENT_PROMPT
from bond_ai.utils import clean_thinking_blocks_for_bedrock, load_chat_model_non_thinking
from bond_ai.registry import supervisor_sub_agent_registry
from bond_ai.memory.vector_store import memory_store
from bond_ai.utilities.llm_factory import llm_thinking, llm_no_thinking
from bond_ai.registry import build_workers_directory_xml,SupervisorSubAgents

async def supervisor_node(state: BondAIWorkflowState, config: RunnableConfig) -> BondAIWorkflowState:
    """Async supervisor with concurrent task execution"""
    configuration = Configuration.from_runnable_config(config)
    print("\n[DEBUG] INVOKED ASYNC SUPERVISOR\n")
    
    # Check for errors first TODO Handle with rules to collect and define
    if state.get("last_error_message"):
        return {"next": "error_response"}
    
    current_members = ["FINISH"] + ["error_response"] + supervisor_sub_agent_registry.get_agent_names()
    plan_tasks = state.get("plan_tasks", [])
    active_task_id = state.get("active_task_id")
    
    # Async task-based dispatching temporarily disabled
    # if len(plan_tasks) > 0:
    #     return {
    #         "messages": [AIMessage(content="Task-based dispatching temporarily disabled.")],
    #         "plan_tasks": plan_tasks,
    #         "active_task_id": active_task_id,
    #         "next": "FINISH"
    #     }
    if len(plan_tasks) > 0:
        result = await _supervisor_handle_plan_task_based_dispatching_async(
            state, plan_tasks, active_task_id, current_members, configuration.table_id
        )
        # Ensure messages are included
        if "messages" not in result:
            result["messages"] = [AIMessage(content="[DEBUG MESSAGE TO REMOVE] Messages  not in result. Task dispatching completed", name="supervisor")]
    else:
        result = await _supervisor_handle_general_routing_async(state, current_members, configuration.table_id, configuration.model)
        # Ensure messages are included
        if "messages" not in result:
            result["messages"] = [AIMessage(content="[DEBUG MESSAGE TO REMOVE] Messages  not in result. Routing decision made", name="supervisor")]

    # Temporarily disable memory snippet injection to simplify control flow
    return result

async def _supervisor_handle_general_routing_async(state: BondAIWorkflowState, current_members, table_id: str, model_name: str):
    """Async version of general routing when no execution plan exists."""
    
    # Check if there's an error message in the state
    if state.get("last_error_message"):
        return {"next": "error_response"}

    
  
##################################################################
###          Build the agent directory section    ################
##################################################################    
    
    # 1) Get context variables (with defaults for missing values)
    today_date, current_filters, table_summary, selected_row_ids, selected_column_ids, mode = _build_context(state)
    
    # 2) Build the agent workers available section
    #    Get the agent names and descriptions from the registry
    agent_directory_entries = build_workers_directory_xml()
    agent_workers_available=  f"""**AVAILABLE SPECIALIZED AGENTS:** 
    Your task is to decide which agent to dispatch based on the conversation history and the user's request to fulfill.
    {agent_directory_entries}"""
    
    print("[DEBUG] SUPERVISOR agent_workers_available:\n", agent_workers_available  ,"\n"  )
    # 3) Build the agent routing valid option section
    routing_options = f"""**ROUTING OPTIONS:**
Valid choices: {', '.join(current_members)}
• Use "FINISH" when the user's request has been completely fulfilled
• Choose the team member whose expertise best matches the current need"""

    
    from src.agents.shared.langsmith_factory import get_langsmith_prompt_async
    # Use the LangSmith prompt's model directly with structured output and LLM Model
    prompt = await get_langsmith_prompt_async("supervisor_agent")
    
    # Check and fix guardrails if needed
    if hasattr(prompt, 'model') and hasattr(prompt.model, 'guardrails'):
        print("[DEBUG] SUPERVISOR prompt.model.guardrails:", prompt.model.guardrails)
        if prompt.model.guardrails == {}:
            prompt.model.guardrails = None
            print("[DEBUG] SUPERVISOR prompt.model.guardrails set to None:", prompt.model.guardrails)



    # Clean thinking blocks from messages to remove thinking metadata if any
    cleaned_messages = clean_thinking_blocks_for_bedrock(list(state["messages"]))

    # Normalize SystemMessages for Anthropic Bedrock: extract any SystemMessage
    # that may have been injected by previous nodes (e.g., memory snippets) and
    # fold their text into the single system prompt so the system message appears
    # first, as required by Anthropic models.
    from bond_ai.utils import split_out_system_messages_for_bedrock
    non_system_messages, extra_system_text = split_out_system_messages_for_bedrock(cleaned_messages)
    
    ## TODO DO we need to pass ? theorically this was becuase bedrock
    # if extra_system_text:
    #     system_content = f"{system_content}\n\n{extra_system_text}"
     # Extract the latest user message as user_request
    user_request = ""
    if non_system_messages:
        # Get the last human message
        for msg in reversed(non_system_messages):
            if hasattr(msg, 'type') and msg.type == 'human':
                user_request = msg.content
                break
    
    # Prepare variables for the LangSmith prompt
    prompt_variables = {
        # Injected Context variables
        "table_id": table_id,
        "today_date": today_date,
        "current_filters": current_filters,
        "table_summary": table_summary,
        "selected_row_ids": selected_row_ids,
        "selected_column_ids": selected_column_ids,
        "mode": mode,
        # Injected sections
        "agent_workers_available": agent_workers_available,
        "planner_agent_name": SupervisorSubAgents.planner_agent.value.name,
        "run_column_agent_name": SupervisorSubAgents.run_column_cell_agent.value.name,
        "build_list_agent_name": SupervisorSubAgents.build_list_agent.value.name,
        "linkedin_enrichment_agent_name": SupervisorSubAgents.linkedin_enrichment_agent.value.name,
        "routing_options": routing_options,
        # Add the user request for template variables
        "user_request": user_request
    }

     # Invoke with both template variables AND conversation messages
    response = await prompt.ainvoke(prompt_variables, messages=non_system_messages)
    print("[DEBUG] SUPERVISOR RESPONSE TYPE:", type(response))
    
    # Handle dictionary response from LangSmith
    delegation = response.get("delegation")
    message = response.get("message", "")
    next_step = response.get("next_step","") ## todo this is not used, but should be passed to the next subagent
    next_agent = "FINISH"  # Default

    out_messages = []
    if delegation:
        print("[DEBUG] SUPERVISOR DELEGATION:", delegation)
        # Get delegate_to from the delegation object
        next_agent = delegation.get("delegate_to") if delegation else None
        # Default fallback
        if not next_agent or next_agent not in current_members:
            logging.warning("[DEBUG] SUPERVISOR next_agent not in current_members. Setting to FINISH")
            next_agent = "FINISH"
            return {
                "next": next_agent,
                "messages": [AIMessage(content=message, name="supervisor")]
            }
        id = f"do-not-render-{str(uuid.uuid4())}"
        out_messages.append(AIMessage(content=f"[DEBUG TO REMOVE: ROUTING JSON] {next_agent}", name="supervisor", id=id))
    
    out_messages.append(AIMessage(content=message, name="supervisor"))
    return {"next": next_agent, "messages": out_messages}
   

async def _supervisor_handle_plan_task_based_dispatching_async(
    state, plan_tasks, active_task_id, current_members, table_id
):
    """Async version of task dispatching with potential parallelization"""
    # Find independent tasks that can run in parallel
    parallel_tasks = _find_parallel_executable_tasks(plan_tasks)

    if len(parallel_tasks) > 1:
        # Execute multiple tasks concurrently
        task_results = await _execute_parallel_tasks(parallel_tasks, state, current_members)
        return _merge_parallel_results(task_results)
    else:
        # Single task execution (existing logic)
        return _dispatch_single_task(plan_tasks, active_task_id, current_members)

def _convert_to_task_objects(tasks_list):
    """Convert list of task dicts back to Task objects."""
    from bond_ai.models.planner_model import Task

    task_objects = []
    for task_dict in tasks_list:
        try:
            task_obj = Task(**task_dict)
            task_objects.append(task_obj)
        except Exception as e:
            logging.warning(f"Failed to convert task dict to Task object: {e}")
            # Keep as dict if conversion fails
            task_objects.append(task_dict)

    return task_objects

def _find_parallel_executable_tasks(plan_tasks):
    """Find tasks that can be executed in parallel (no dependencies)."""
    if not plan_tasks:
        return []

    # Convert to list of dicts if needed
    tasks_list = []
    for task in plan_tasks:
        if hasattr(task, 'model_dump'):
            tasks_list.append(task.model_dump())
        elif isinstance(task, dict):
            tasks_list.append(task)
        else:
            task_dict = {
                'id': getattr(task, 'id', ''),
                'order': getattr(task, 'order', 0),
                'action': getattr(task, 'action', ''),
                'agent': getattr(task, 'agent', ''),
                'tool': getattr(task, 'tool', ''),
                'why': getattr(task, 'why', ''),
                'status': getattr(task, 'status', 'pending'),
                'error': getattr(task, 'error', None)
            }
            tasks_list.append(task_dict)

    # For now, return only the first pending task (simple implementation)
    # In a more sophisticated version, you could analyze dependencies
    pending_tasks = [t for t in tasks_list if t['status'] == 'pending']
    return pending_tasks[:1]  # Return max 1 task for now

async def _execute_parallel_tasks(parallel_tasks, state, current_members):
    """Execute multiple tasks concurrently."""
    from langchain_core.messages import AIMessage

    if not parallel_tasks:
        return []

    # For now, just execute the first task (fallback to single execution)
    # In a full implementation, you would create concurrent executions
    task = parallel_tasks[0]

    # Check if agent is available
    if task['agent'] not in current_members:
        task['status'] = 'failed'
        task['error'] = f"Agent '{task['agent']}' not available"
        return [task]

    # Mark as in progress and return dispatch info
    task['status'] = 'in_progress'

    return [{
        'task': task,
        'result': {
            "messages": [AIMessage(content=f"🎯 **TASK ASSIGNMENT**\n\n**Task ID:** {task['id']}\n**Action:** {task['action']}\n**Tool Required:** {task['tool']}\n**Objective:** {task['why']}\n\n**Instructions:** Please execute this task using the specified tool. Focus on the action described and provide clear results.")],
            "plan_tasks": [task],
            "active_task_id": task['id'],
            "next": task['agent']
        }
    }]

def _merge_parallel_results(task_results):
    """Merge results from parallel task execution."""
    if not task_results:
        return {"next": "FINISH"}

    # For single task execution, return the first result
    if len(task_results) == 1:
        return task_results[0]['result']

    # For multiple tasks, merge messages and return to supervisor

    all_messages = []
    all_tasks = []

    for result in task_results:
        if 'result' in result:
            result_data = result['result']
            if 'messages' in result_data:
                all_messages.extend(result_data['messages'])
            if 'plan_tasks' in result_data:
                all_tasks.extend(result_data['plan_tasks'])

    return {
        "messages": all_messages,
        "plan_tasks": all_tasks,
        "next": "supervisor"
    }

def _dispatch_single_task(plan_tasks, active_task_id, current_members):
    """Dispatch a single task (fallback from async to sync execution)."""
    # Use the existing sync implementation
    return _supervisor_handle_plan_task_based_dispatching(
        {"plan_tasks": plan_tasks, "active_task_id": active_task_id},
        plan_tasks,
        active_task_id,
        current_members,
        ""  # table_id not used in this context
    )

def _supervisor_handle_plan_task_based_dispatching(state, plan_tasks, active_task_id, current_members, table_id):
    """Handle task-based dispatching when execution plan exists."""
    from langchain_core.messages import AIMessage

    # Check if there's an error message in the state
    if state.get("last_error_message"):
        return {"next": "error_response"}
    
     
    # Find the next task to execute
    next_task = None
     # Convert plan_tasks to list of dicts if they're Task objects
    tasks_list = []
    for task in plan_tasks:
        if hasattr(task, 'model_dump'):
            tasks_list.append(task.model_dump())
        elif isinstance(task, dict):
            tasks_list.append(task)
        else:
            # Try to convert to dict
            task_dict = {
                'id': getattr(task, 'id', ''),
                'order': getattr(task, 'order', 0),
                'action': getattr(task, 'action', ''),
                'agent': getattr(task, 'agent', ''),
                'tool': getattr(task, 'tool', ''),
                'why': getattr(task, 'why', ''),
                'status': getattr(task, 'status', 'pending'),
                'error': getattr(task, 'error', None)
            }
            tasks_list.append(task_dict)

    # Sort tasks by order
    tasks_list.sort(key=lambda x: x.get('order', 0))

    # Mark current active task as completed if it exists
    if active_task_id:
        for task in tasks_list:
            if task['id'] == active_task_id and task['status'] == 'in_progress':
                task['status'] = 'completed'
                logging.info(f"✓ Completed task: {task['id']} - {task['action']}")
                break

    # Find next pending task
    for task in tasks_list:
        if task['status'] == 'pending':
            # Check if this agent is available
            if task['agent'] in current_members:
                next_task = task
                break
            else:
                # Agent not available, mark as failed
                task['status'] = 'failed'
                task['error'] = f"Agent '{task['agent']}' not available"
                logging.warning(f"⚠ Task {task['id']} failed: Agent '{task['agent']}' not available")

    # If no next task found, check if all tasks are completed
    if not next_task:
        pending_tasks = [t for t in tasks_list if t['status'] == 'pending']
        failed_tasks = [t for t in tasks_list if t['status'] == 'failed']

        if not pending_tasks:
            # All tasks completed or failed
            completed_tasks = [t for t in tasks_list if t['status'] == 'completed']

            if failed_tasks:
                failure_summary = "\n".join([f"- {t['action']}: {t.get('error', 'Unknown error')}" for t in failed_tasks])
                message = f"Execution plan completed with {len(completed_tasks)} successful tasks and {len(failed_tasks)} failed tasks.\n\nFailed tasks:\n{failure_summary}"
            else:
                message = f"✅ Execution plan completed successfully! All {len(completed_tasks)} tasks have been executed."

            return {
                "messages": [AIMessage(content=message)],
                "plan_tasks": _convert_to_task_objects(tasks_list),
                "active_task_id": None,
                "next": "FINISH"
            }

    # Dispatch next task
    if next_task:
        # Mark task as in progress
        next_task['status'] = 'in_progress'

        # Create task delegation message
        delegation_message = f"""
🎯 **TASK ASSIGNMENT**

**Task ID:** {next_task['id']}
**Action:** {next_task['action']}
**Tool Required:** {next_task['tool']}
**Objective:** {next_task['why']}

**Instructions:** Please execute this task using the specified tool. Focus on the action described and provide clear results.
"""

        #logging.info(f"🚀 Dispatching task {next_task['id']} to agent '{next_task['agent']}'")

        return {
            "messages": [AIMessage(content=delegation_message)],
            "plan_tasks": _convert_to_task_objects(tasks_list),
            "active_task_id": next_task['id'],
            "next": next_task['agent']
        }

    # Fallback - no executable tasks
    return {
        "messages": [AIMessage(content="No executable tasks found in the current plan.")],
        "plan_tasks": _convert_to_task_objects(tasks_list),
        "active_task_id": None,
        "next": "FINISH"
    }

def _supervisor_handle_plan_task_based_dispatching_deprecated(state, plan_tasks, active_task_id, current_members, table_id):
    """Handle task-based dispatching when execution plan exists."""
    from langchain_core.messages import AIMessage

    # Check if there's an error message in the state
    if state.get("last_error_message"):
        return {"next": "error_response"}
    
    # Use the more aggressive cleaning function
      
    # Find the next task to execute
    next_task = None
    updated_tasks = []

    # Convert plan_tasks to list of dicts if they're Task objects
    tasks_list = []
    for task in plan_tasks:
        if hasattr(task, 'model_dump'):
            tasks_list.append(task.model_dump())
        elif isinstance(task, dict):
            tasks_list.append(task)
        else:
            # Try to convert to dict
            task_dict = {
                'id': getattr(task, 'id', ''),
                'order': getattr(task, 'order', 0),
                'action': getattr(task, 'action', ''),
                'agent': getattr(task, 'agent', ''),
                'tool': getattr(task, 'tool', ''),
                'why': getattr(task, 'why', ''),
                'status': getattr(task, 'status', 'pending'),
                'error': getattr(task, 'error', None)
            }
            tasks_list.append(task_dict)

    # Sort tasks by order
    tasks_list.sort(key=lambda x: x.get('order', 0))

    # Mark current active task as completed if it exists
    if active_task_id:
        for task in tasks_list:
            if task['id'] == active_task_id and task['status'] == 'in_progress':
                task['status'] = 'completed'
                logging.info(f"✓ Completed task: {task['id']} - {task['action']}")
                break

    # Find next pending task
    for task in tasks_list:
        if task['status'] == 'pending':
            # Check if this agent is available
            if task['agent'] in current_members:
                next_task = task
                break
            else:
                # Agent not available, mark as failed
                task['status'] = 'failed'
                task['error'] = f"Agent '{task['agent']}' not available"
                logging.warning(f"⚠ Task {task['id']} failed: Agent '{task['agent']}' not available")

    # If no next task found, check if all tasks are completed
    if not next_task:
        pending_tasks = [t for t in tasks_list if t['status'] == 'pending']
        failed_tasks = [t for t in tasks_list if t['status'] == 'failed']

        if not pending_tasks:
            # All tasks completed or failed
            completed_tasks = [t for t in tasks_list if t['status'] == 'completed']

            if failed_tasks:
                failure_summary = "\n".join([f"- {t['action']}: {t.get('error', 'Unknown error')}" for t in failed_tasks])
                message = f"Execution plan completed with {len(completed_tasks)} successful tasks and {len(failed_tasks)} failed tasks.\n\nFailed tasks:\n{failure_summary}"
            else:
                message = f"✅ Execution plan completed successfully! All {len(completed_tasks)} tasks have been executed."

            return {
                "messages": [AIMessage(content=message)],
                "plan_tasks": _convert_to_task_objects(tasks_list),
                "active_task_id": None,
                "next": "FINISH"
            }

    # Dispatch next task
    if next_task:
        # Mark task as in progress
        next_task['status'] = 'in_progress'

        # Create task delegation message
        delegation_message = f"""
🎯 **TASK ASSIGNMENT**

**Task ID:** {next_task['id']}
**Action:** {next_task['action']}
**Tool Required:** {next_task['tool']}
**Objective:** {next_task['why']}

**Instructions:** Please execute this task using the specified tool. Focus on the action described and provide clear results.
"""
        logging.info(f"🚀 Dispatching task {next_task['id']} to agent '{next_task['agent']}'")
        return {
            "messages": [AIMessage(content=delegation_message, id = f"do-not-render-{str(uuid.uuid4())}")],
            "plan_tasks": _convert_to_task_objects(tasks_list),
            "active_task_id": next_task['id'],
            "next": next_task['agent']
        }

    # Fallback - no executable tasks
    return {
        "messages": [AIMessage(content="No executable tasks found in the current plan.")],
        "plan_tasks": _convert_to_task_objects(tasks_list),
        "active_task_id": None,
        "next": "FINISH"
    }


def _build_context(state):
    """Build context variables for the prompt."""
    today_date = datetime.now().strftime('%Y-%m-%d')
    current_filters = getattr(state, 'current_filters', 'Not available')
    table_summary = state.get('table_summary', 'Not available')
    selected_row_ids = state.get('selected_row_ids', 'None')
    selected_column_ids = state.get('selected_column_ids', 'None')
    mode = state.get('mode', 'Not available')
    return today_date,current_filters,table_summary,selected_row_ids,selected_column_ids,mode
