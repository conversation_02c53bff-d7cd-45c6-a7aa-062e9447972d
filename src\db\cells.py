import asyncio
import httpx
import json
import os
import uuid
import mimetypes
from pathlib import Path
from dotenv import load_dotenv
import time
from typing import List, Dict, Any, Optional, Union
from supabase import create_async_client, AsyncClient
import logging
from fastapi import HTTPException, BackgroundTasks

# Load environment variables at the start
load_dotenv()

# Configure logging
logger = logging.getLogger("backend_api.server")


# Initialize Supabase client
def get_supabase_client() -> AsyncClient:
    """
    Initialize and return a Supabase client instance.
    Uses environment variables for configuration.
    """
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

    if not supabase_url or not supabase_key:
        logger.error(
            "Supabase configuration missing. Please check environment variables."
        )
        raise ValueError("Supabase configuration missing")

    try:
        return create_client(supabase_url, supabase_key)
    except Exception as e:
        logger.error(f"Failed to initialize Supabase client: {str(e)}")
        raise


# Cell type definition
class CellData(Dict[str, Any]):
    """
    Represents a cell in the cells table with the following structure:
    - column_id: int - The ID of the column
    - row_id: int - The ID of the row
    - table_id: str - The ID of the table
    - value: Optional[str] - The text value of the cell, can be null
    - run_status: Optional[Dict] - JSON with run status information {run?: str, error?: str}
    """

    pass


# CRUD Operations for cells table


async def get_cell(
    column_id: int, row_id: int, table_id: str
) -> Optional[Dict[str, Any]]:
    """
    Get a cell by its ID.

    Args:
        cell_id: The ID of the cell to retrieve

    Returns:
        The cell data or None if not found

    Raises:
        HTTPException: If retrieval fails
    """
    try:
        supabase = get_supabase_client()

        # Query the cell by composite key
        response = (
            supabase.table("cells")
            .select("*")
            .eq("column_id", column_id)
            .eq("row_id", row_id)
            .eq("table_id", table_id)
            .execute()
        )

        # Check for errors
        if hasattr(response, "error") and response.error:
            logger.error(
                f"Error retrieving cell (column_id={column_id}, row_id={row_id}, table_id={table_id}): {response.error}"
            )
            raise HTTPException(
                status_code=400, detail=f"Failed to retrieve cell: {response.error}"
            )

        # Return the cell if found
        if response.data and len(response.data) > 0:
            return response.data[0]
        else:
            logger.warning(
                f"Cell with (column_id={column_id}, row_id={row_id}, table_id={table_id}) not found"
            )
            return None

    except Exception as e:
        logger.error(f"Unexpected error retrieving cell {cell_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


async def update_cell(
    supabase: AsyncClient,
    column_id: int,
    row_id: int,
    table_id: str,
    value: Optional[str] = None,
    run_status: Optional[Dict[str, str]] = None,
) -> bool:
    """
    Update an existing cell.

    Args:
        cell_id: The ID of the cell to update
        cell_data: Dictionary containing updated cell data

    Returns:
        The updated cell data

    Raises:
        HTTPException: If update fails
    """
    try:

        # Prepare update data
        update_data = {}
        if value is not None:
            update_data["value"] = value
        if run_status is not None:
            update_data["run_status"] = run_status

        if not update_data:
            logger.warning("No data provided for update")
            raise HTTPException(status_code=400, detail="No data provided for update")

        # Perform the update operation
        response = (
            await supabase.table("cells")
            .update(update_data)
            .eq("column_id", column_id)
            .eq("row_id", row_id)
            .eq("table_id", table_id)
            .execute()
        )

        # Check for errors
        if hasattr(response, "error") and response.error:
            logger.error(
                f"Error updating cell (column_id={column_id}, row_id={row_id}, table_id={table_id}): {response.error}"
            )
            raise HTTPException(
                status_code=400, detail=f"Failed to update cell: {response.error}"
            )

        # Check if any rows were affected
        if not response.data or len(response.data) == 0:
            logger.warning(
                f"Cell with (column_id={column_id}, row_id={row_id}, table_id={table_id}) not found for update"
            )
            raise HTTPException(status_code=404, detail=f"Cell not found")

        # Just log success and return True
        logger.info(
            f"Cell (column_id={column_id}, row_id={row_id}, table_id={table_id}) updated successfully"
        )
        return True

    except HTTPException:
        # Re-raise HTTP exceptions
        raise

    except Exception as e:
        logger.error(f"Unexpected error updating cell : {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


async def batch_update_cells(updates: List[Dict[str, Any]]) -> bool:
    """
    Update multiple cells in separate transactions.

    Args:
        updates: List of dictionaries containing cell identifiers and update data
                Each dict should have 'column_id', 'row_id', 'table_id' and optionally 'value' and/or 'run_status'

    Returns:
        List of updated cells

    Raises:
        HTTPException: If batch update fails
    """
    success_count = 0
    errors = []

    for update_item in updates:
        column_id = update_item.get("column_id")
        row_id = update_item.get("row_id")
        table_id = update_item.get("table_id")
        value = update_item.get("value")
        run_status = update_item.get("run_status")

        if not all([column_id is not None, row_id is not None, table_id]):
            errors.append(
                f"Invalid update item, missing required identifiers: {update_item}"
            )
            continue

        if value is None and run_status is None:
            errors.append(f"Invalid update item, no data to update: {update_item}")
            continue

        try:
            success = await update_cell(column_id, row_id, table_id, value, run_status)
            if success:
                success_count += 1
        except Exception as e:
            errors.append(
                f"Failed to update cell (column_id={column_id}, row_id={row_id}, table_id={table_id}): {str(e)}"
            )

    if errors:
        logger.error(f"Errors in batch update: {errors}")
        if success_count == 0:
            # If no updates were successful, return False
            return False

    logger.info(f"Batch updated {success_count} cells successfully")
    return True


async def upsert_cell_details(
    supabase: AsyncClient,
    table_id: str,
    column_id: int,
    row_id: int,
    cell_details: list[Dict[str, Any]],
) -> None:
    """
    Upsert cell details into the cell_details table.

    Args:
        table_id: The ID of the table
        row_id: The ID of the row
        column_id: The ID of the column
        cell_details: List[Dict[str, Any]]

    Returns:
        True if the operation was successful, False otherwise

    Raises:
        HTTPException: If the upsert operation fails
    """
    try:

        # Prepare the data for upsert
        data = {
            "table_id": table_id,
            "row_id": row_id,
            "column_id": column_id,
            "value": cell_details,
        }

        # Perform the upsert operation
        response = (
            await supabase.table("cell_details")
            .upsert(data, on_conflict="table_id,column_id,row_id")
            .execute()
        )

        # Check for errors
        if hasattr(response, "error") and response.error:
            error_message = f"Error upserting cell details: {response.error}"
            logger.error(error_message)
            raise HTTPException(status_code=400, detail=error_message)

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        error_message = f"Unexpected error upserting cell details: {str(e)}"
        logger.error(error_message)
        raise HTTPException(status_code=500, detail=error_message)


async def reimburse_tokens(
    organization_id: str, tokens: int, supabase: AsyncClient
) -> bool:
    """
    Increase tokens for an organization using Supabase RPC.

    Args:
        organization_id: The ID of the organization
        tokens: The number of tokens to add

    Returns:
        True if the operation was successful, False otherwise

    Raises:
        HTTPException: If the RPC call fails
    """
    try:

        # Call the RPC function
        response = await supabase.rpc(
            "modify_tokens",
            {
                "p_operation": "increase",
                "p_organization_id": organization_id,
                "p_tokens": tokens,
            },
        ).execute()

        # Check for errors
        if hasattr(response, "error") and response.error:
            error_message = f"Error modifying organization tokens: {response.error}"
            logger.error(error_message)
            raise HTTPException(status_code=400, detail=error_message)

        logger.info(
            f"Successfully increased {tokens} tokens for organization {organization_id}"
        )
        return True

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        error_message = f"Unexpected error modifying organization tokens: {str(e)}"
        logger.error(error_message)
        raise HTTPException(status_code=500, detail=error_message)


async def download_and_upload_image(
    supabase: AsyncClient,
    image_url: str,
    bucket: str,
    storage_path: str,
    image_name: str = None,
) -> str:
    """
    Download an image from a URL and upload it to Supabase storage.

    Args:
        supabase: An authenticated Supabase client
        image_url: URL of the image to download
        bucket: Storage bucket name (usually organization_id)
        storage_path: Path within the bucket to store the image
        image_name: Optional custom name for the image file

    Returns:
        Dictionary with:
            - ok: Boolean indicating success or failure
            - output: Dictionary with path to the uploaded image if successful
                     (including storage_path within the bucket)
            - error: Error message if failed
    """
    try:
        # Download the image
        async with httpx.AsyncClient() as client:
            response = await client.get(image_url, timeout=30.0)
            response.raise_for_status()  # Raise exception for HTTP errors
            image_data = response.content

        # Determine MIME type from response headers or infer from URL
        content_type = response.headers.get("content-type")
        extension = ""

        if content_type:
            # Get extension from MIME type
            guessed_extension = mimetypes.guess_extension(content_type)
            if guessed_extension:
                extension = guessed_extension

        # If no extension from MIME type, try to get from URL
        if not extension:
            url_path = Path(image_url.split("?")[0])
            url_extension = url_path.suffix
            if url_extension:
                extension = url_extension
            else:
                # Default to .jpg if we can't determine extension
                extension = ".jpg"
                content_type = "image/jpeg"

        # Generate a UUID for the filename
        if image_name:
            # Use provided name but ensure it has the right extension
            base_filename = image_name
            if "." not in base_filename:
                filename = f"{base_filename}{extension}"
            else:
                filename = base_filename
        else:
            # Generate a UUID-based filename
            filename = f"{uuid.uuid4()}{extension}"

        # Full path in storage
        full_path = f"{storage_path}/{filename}"

        # Upload to Supabase Storage with cache control
        upload_response = supabase.storage.from_(bucket).upload(
            full_path,
            image_data,
            {
                "content-type": content_type or "application/octet-stream",
                "cache-control": "3600",
                "upsert": True,
            },
        )

        return upload_response.path

    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error downloading image: {str(e)}")
        return {"ok": False, "error": f"HTTP error downloading image: {str(e)}"}
    except Exception as e:
        logger.error(f"Error downloading or uploading image: {str(e)}")
        return {"ok": False, "error": f"Error downloading or uploading image: {str(e)}"}
