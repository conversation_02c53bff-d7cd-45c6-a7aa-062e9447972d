from celery import Celery
import os

# Import centralized configuration
from src.config import get_settings

# Get application settings
settings = get_settings()

# Extract the actual string value from SecretStr
redis_url = settings.REDIS_URL.get_secret_value() if settings.REDIS_URL else None

# Celery setup
celery_app = Celery(
    "src.backend_server.tasks",
    broker=redis_url,
    backend=redis_url,
    include=["src.backend_server.tasks.openai"],
)

celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],  # Ignore other content
    result_serializer="json",
    timezone="America/New_York",
    enable_utc=True,
)


# Task routing configuration
celery_app.conf.task_routes = {
    "src.backend_server.tasks.openai.run_openai_task": {"queue": "openai"}
}

# Auto-discover tasks
celery_app.autodiscover_tasks(["src.backend_server.tasks"])
