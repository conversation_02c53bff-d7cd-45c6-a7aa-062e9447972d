#!/usr/bin/env python3
"""Simple test script for job title functionality."""

import os
import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_basic_functionality():
    """Test basic job title vector store functionality."""
    print("Testing JobTitleVectorStore...")
    
    # Check if OpenAI key is available
    print(f"OPENAI_API_KEY exists: {bool(os.environ.get('OPENAI_API_KEY'))}")
    
    try:
        from agents.bond_ai.tools.vector_stores import JobTitleVectorStore, DEFAULT_JOB_TITLES_CSV_FILE
        
        print(f"Job titles file exists: {DEFAULT_JOB_TITLES_CSV_FILE.exists()}")
        
        if not DEFAULT_JOB_TITLES_CSV_FILE.exists():
            print("Lima titles file not found, skipping test")
            return
        
        # Create store
        print("Creating JobTitleVectorStore...")
        store = JobTitleVectorStore()
        
        print(f"Using OpenAI: {store._use_openai}")
        print(f"Loaded {len(store.job_titles)} job titles")
        print(f"Embeddings shape: {store.embeddings.shape}")
        
        # Test query
        print("\nTesting query...")
        results = store.query("software engineer", top_k=3)
        
        print("Results:")
        for job_title, score in results:
            print(f"  {job_title.title} (score: {score:.3f})")
            
        print("\nTest completed successfully!")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

def test_search_tool():
    """Test the search tool."""
    print("\nTesting search_job_titles tool...")
    
    try:
        from agents.bond_ai.tools.vector_stores import search_job_titles
        
        # Test the tool function
        results = search_job_titles.invoke({"query": "data scientist", "top_k": 3})
        
        print("Tool results:")
        for i, title in enumerate(results, 1):
            print(f"  {i}. {title}")
            
        print("Tool test completed successfully!")
        
    except Exception as e:
        print(f"Tool test error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_basic_functionality()
    test_search_tool()
