"""
This is the state definition for the AI.
It defines the state of the agent and the state of the conversation.
"""

from typing import List, TypedDict, Optional
from bond_ai.state import BondAIWorkflowState

class Step(TypedDict):
    """
    Represents a step taken in the research process.
    """
    id: str
    description: str
    status: str
    type: str
    search_result: Optional[str]
    result: Optional[str]
    updates: Optional[List[str]]

class AgentState(BondAIWorkflowState):
    """Perplexity agent state - extends BondAI state with research-specific fields"""
    steps: List[Step]
    answer: Optional[str]
