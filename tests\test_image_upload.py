import asyncio
import os
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.db.utils import download_and_upload_image, get_supabase_client
from src.core.config import get_settings

# Test image URL - use a reliable public image
TEST_IMAGE_URL = "https://images.dog.ceo/breeds/terrier-toy/n02087046_3439.jpg"

# Get settings
settings = get_settings()


async def test_image_upload():
    """Test the download_and_upload_image2 function"""
    try:
        print(f"Testing image upload with URL: {TEST_IMAGE_URL}")

        # Get Supabase client
        print("Getting Supabase client...")
        supabase = await get_supabase_client()

        # Generate test bucket and path
        bucket = "org_64bd7f51c1450d62"
        storage_path = f"tbl_3655e99e74169376"

        print(f"Uploading to bucket: {bucket}, path: {storage_path}")

        # Call the function
        result = await download_and_upload_image(
            supabase=supabase,
            image_url=TEST_IMAGE_URL,
            bucket=bucket,
            storage_path=storage_path,
        )

        print(f"Upload successful! Image path: {result}")
        return result

    except Exception as e:
        print(f"Error in test_image_upload: {str(e)}")
        import traceback

        traceback.print_exc()
        raise


if __name__ == "__main__":
    # Run the async function using asyncio
    try:
        result = asyncio.run(test_image_upload())
        print(f"Test completed successfully with result: {result}")
    except Exception as e:
        print(f"Test failed with error: {str(e)}")
