from langchain_aws import ChatBedrock


# Model configurations
SUPERVISOR_MODEL_ID = "us.anthropic.claude-3-7-sonnet-********-v1:0"
SUB_AGENT_MODEL_ID = "us.anthropic.claude-3-7-sonnet-********-v1:0"

# Model parameters
MAX_TOKENS = 30000
TEMPERATURE = 0.1

# Available agents
AGENTS = ["search_agent"]

# Agent descriptions for supervisor routing
AGENT_DESCRIPTIONS = {
    "search_agent": "Specialized in web search and information retrieval tasks. Use this agent for finding current information, research, and answering questions that require web search."
}


def get_supervisor_model() -> ChatBedrock:
    """Get the supervisor model instance."""
    return ChatBedrock(
        model=SUPERVISOR_MODEL_ID,
        model_kwargs={
            "max_tokens": MAX_TOKENS,
            "temperature": TEMPERATURE
        }
    )


def get_sub_agent_model() -> ChatBedrock:
    """Get the sub-agent model instance."""
    return ChatBedrock(
        model=SUB_AGENT_MODEL_ID,
        model_kwargs={
            "max_tokens": MAX_TOKENS,
            "temperature": TEMPERATURE,
        }
    )

