"""HTTP service schemas following Lima patterns."""
from typing import Optional, Dict, Any
from pydantic import BaseModel, ConfigDict
from src.services.http.models import HTTPRequest, HTTPResponse


class HTTPServiceInput(BaseModel):
    """Input model for HTTP service requests."""
    model_config = ConfigDict(extra="ignore")
    
    request: HTTPRequest


class HTTPServiceOutput(BaseModel):
    """Output model for HTTP service responses."""
    model_config = ConfigDict(extra="ignore")
    
    response: HTTPResponse
    credit_cost: int = 1  # Default credit cost like Lima service


class HTTPServiceResponse(BaseModel):
    """Response wrapper following Lima service patterns."""
    model_config = ConfigDict(extra="ignore")
    
    data: HTTPServiceOutput
    credit_cost: int


# Convenience input models for different HTTP methods
class GetRequestInput(BaseModel):
    """Input model for GET requests."""
    model_config = ConfigDict(extra="ignore")
    
    url: str
    headers: Optional[Dict[str, str]] = None
    params: Optional[Dict[str, Any]] = None


class PostRequestInput(BaseModel):
    """Input model for POST requests.""" 
    model_config = ConfigDict(extra="ignore")
    
    url: str
    body: Optional[Dict[str, Any]] = None
    headers: Optional[Dict[str, str]] = None


class PutRequestInput(BaseModel):
    """Input model for PUT requests."""
    model_config = ConfigDict(extra="ignore")
    
    url: str
    body: Optional[Dict[str, Any]] = None
    headers: Optional[Dict[str, str]] = None


class DeleteRequestInput(BaseModel):
    """Input model for DELETE requests."""
    model_config = ConfigDict(extra="ignore")
    
    url: str
    headers: Optional[Dict[str, str]] = None