"""
Load test script for the LinkedIn profile enrichment endpoint.
Submits concurrent LinkedIn profile requests to test system performance.
"""

import os
import asyncio
import httpx
import time
import uuid
from dotenv import load_dotenv
import random

# Load environment variables
load_dotenv()

# Base URL for the API
BASE_URL = "http://localhost:2024"

# Get the API token from environment variables or set it manually
API_TOKEN = os.getenv("SUPABASE_SERVICE_ROLE_KEY", "your-api-token")

# Headers for the requests
HEADERS = {"Authorization": f"Bearer {API_TOKEN}", "Content-Type": "application/json"}

# Template for LinkedIn profile request
LINKEDIN_REQUEST_TEMPLATE = {
    "organization_id": "org_64bd7f51c1450d62",
    "table_id": "tbl_3655e99e74169376",
    "column_id": 7,
    "service_id": 15,  # LinkedIn profile service ID
    "credits": 10,
    "formula": None,
    "linkedin_profile_url": "{profile_url}",
}

# Sample LinkedIn profile URLs to test with
# Note: These are example URLs and should be replaced with real profiles for actual testing
LINKEDIN_PROFILES = [
    "https://www.linkedin.com/in/satyanadella/",
    "https://www.linkedin.com/in/williamhgates/",
    "https://www.linkedin.com/in/jeffweiner08/",
    "https://www.linkedin.com/in/andrewyng/",
    "https://www.linkedin.com/in/elonmusk/",
    "https://www.linkedin.com/in/sundar-pichai-profile/",
    "https://www.linkedin.com/in/tim-cook-profile/",
    "https://www.linkedin.com/in/sheryl-sandberg/",
    "https://www.linkedin.com/in/jack-dorsey/",
    "https://www.linkedin.com/in/reid-hoffman/",
]


async def send_linkedin_request(session, request_id):
    """Send a single LinkedIn profile request."""
    # Create a unique request by varying the row_id and LinkedIn profile
    request_data = LINKEDIN_REQUEST_TEMPLATE.copy()
    request_data["row_id"] = 1  # Use request_id as row_id for uniqueness
    request_data["run_id"] = str(uuid.uuid4())

    # Select a random LinkedIn profile
    profile_url = random.choice(LINKEDIN_PROFILES)
    request_data["linkedin_profile_url"] = (
        "https://de.linkedin.com/in/rina-gracic-9b86952a4"
    )

    start_time = time.time()
    try:
        response = await session.post(
            f"{BASE_URL}/services/linkedin-profile",
            json=request_data,
            headers=HEADERS,
            timeout=60.0,  # LinkedIn profile enrichment might take longer
        )

        elapsed = time.time() - start_time
        status = response.status_code

        if status == 200:
            result = "✅"
        else:
            result = "❌"

        print(
            f"Request {request_id:03d}: {result} Status: {status} Time: {elapsed:.2f}s Profile: {profile_url}"
        )
        return {
            "request_id": request_id,
            "status_code": status,
            "elapsed_time": elapsed,
            "success": status == 200,
            "profile_url": profile_url,
            "response": response.json() if status == 200 else None,
        }
    except Exception as e:
        elapsed = time.time() - start_time
        print(
            f"Request {request_id:03d}: ❌ Error: {str(e)} Time: {elapsed:.2f}s Profile: {profile_url}"
        )
        return {
            "request_id": request_id,
            "status_code": 0,
            "elapsed_time": elapsed,
            "success": False,
            "profile_url": profile_url,
            "error": str(e),
        }


async def run_load_test(num_requests=1, concurrency=10):
    """
    Run a load test with the specified number of requests and concurrency.

    Note: LinkedIn profile enrichment is rate-limited by the ProxyCurl API,
    so we use lower default values for num_requests and concurrency.
    """
    print(
        f"\n=== Starting LinkedIn Profile Load Test: {num_requests} requests with concurrency {concurrency} ===\n"
    )

    start_time = time.time()

    # Create a client session for all requests
    async with httpx.AsyncClient() as session:
        # Create a list of tasks
        tasks = []
        for i in range(1, num_requests + 1):
            tasks.append(send_linkedin_request(session, i))

        # Run tasks with limited concurrency
        # We use asyncio.Semaphore to limit the number of concurrent requests
        semaphore = asyncio.Semaphore(concurrency)

        async def bounded_fetch(coro):
            async with semaphore:
                return await coro

        # Execute all requests with the semaphore to limit concurrency
        results = await asyncio.gather(*(bounded_fetch(task) for task in tasks))

    # Calculate statistics
    total_time = time.time() - start_time
    successful_requests = sum(1 for r in results if r["success"])
    failed_requests = num_requests - successful_requests

    # Calculate rate limit statistics
    rate_limited_requests = sum(
        1 for r in results if not r["success"] and r.get("status_code") == 429
    )

    # Print summary
    print(f"\n=== LinkedIn Profile Load Test Complete ===")
    print(f"Total time: {total_time:.2f} seconds")
    print(f"Requests per second: {num_requests / total_time:.2f}")
    print(
        f"Successful requests: {successful_requests} ({successful_requests / num_requests * 100:.1f}%)"
    )
    print(
        f"Failed requests: {failed_requests} ({failed_requests / num_requests * 100:.1f}%)"
    )
    if rate_limited_requests > 0:
        print(
            f"Rate limited requests: {rate_limited_requests} ({rate_limited_requests / num_requests * 100:.1f}%)"
        )

    # Return results for further analysis if needed
    return {
        "total_time": total_time,
        "requests_per_second": num_requests / total_time,
        "successful_requests": successful_requests,
        "failed_requests": failed_requests,
        "rate_limited_requests": rate_limited_requests,
        "results": results,
    }


if __name__ == "__main__":
    # Number of requests and concurrency level
    # Using lower values for LinkedIn profile enrichment due to API rate limits
    NUM_REQUESTS = 1
    CONCURRENCY = 10

    # Run the load test
    asyncio.run(run_load_test(NUM_REQUESTS, CONCURRENCY))
