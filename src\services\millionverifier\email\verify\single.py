from src.db.utils import get_type_id, get_type_name
import httpx
from src.core.config import get_settings
import asyncio
import logging
import os
from typing import Dict, Any

settings = get_settings()

logger = logging.getLogger(__name__)


def transform_email(src: Dict[str, Any]) -> list[Dict[str, Any]]:
    """Transform email data into a structured format"""

    result = [
        {
            "name": "Validation",
            "type_id": get_type_id("object"),
            "type": get_type_name("object"),
            "is_brand": False,
            "value": [
                {
                    "name": "Source",
                    "type_id": get_type_id("text"),
                    "type": get_type_name("text"),
                    "is_brand": False,
                    "value": "Millionverifier",
                },
                {
                    "name": "Quality",
                    "type_id": get_type_id("text"),
                    "type": get_type_name("text"),
                    "is_brand": False,
                    "value": src["quality"],
                },
            ],
        },
    ]

    return result


async def verify_email(
    input: Dict[str, Any],
) -> Dict[str, Any]:
    """Fetch Email Verify from the Millionverifier API

    Returns:
        tuple: (PersonProfileOutput, credit_cost)s
    """
    try:
        if settings.IS_STRESS_TESTING:
            # wait for 30 seconds
            await asyncio.sleep(30)
            return {"success": False, "validated": False, "data": {"email": "<EMAIL>", "quality": "valid", "result": "ok"}, "credit_cost": 0}
        async with httpx.AsyncClient() as client:
            response = await client.get(
                "https://api.millionverifier.com/api/v3/",
                params={
                    "api": settings.MILLIONVERIFIER_API_KEY.get_secret_value(),
                    "email": input["email"],
                },
                headers={
                    "accept": "application/json",
                },
                timeout=60.0,  # 60 second timeout
            )
            response.raise_for_status()

            # Extract credit cost from headers
            output = response.json()

            # Check if raw_format exists in the response

            return {
                "success": output["result"] == "ok",
                "validated": True,
                "data": {
                    "email": output["email"],
                    "quality": output["quality"],
                    "result": output["result"],
                },
                "credit_cost": output["credits"],
            }
    except httpx.HTTPStatusError as e:
        if (
            e.response.status_code == 404
            or e.response.status_code == 400
            or e.response.status_code == 402
            or e.response.status_code == 423
        ):
            logger.info("Millionverifier Email Verifier Not Found")
            return {"success": False, "message": "Email not found"}
        elif e.response.status_code == 429:
            logger.info("Millionverifier Email Verifier Rate Limit")
            # Extract retry-after header if available
            retry_after = e.response.headers.get("Retry-After")
            return {
                "success": False,
                "message": "Rate limit exceeded. Try again later.",
                "rate-limit": True,
            }
        else:
            logger.error(
                f"Millionverifier Email Verifier HTTP unhandled Error: {e.response.status_code}"
            )
            return {
                "success": False,
                "error": f"HTTP Error: {e.response.status_code}",
            }
    except Exception as e:
        logger.error(f"Millionverifier Email Verifier Exception: {str(e)}")
        return {"success": False, "error": str(e)}
