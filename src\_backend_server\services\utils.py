from datetime import datetime


def format_date(date_str: str) -> str:
    if not date_str:
        return ""
    try:
        return datetime.strptime(date_str, "%Y-%m-%d").strftime("%Y-%m-%d")
    except ValueError:
        return date_str


column_type_map = {
    "text": 1,
    "date": 2,
    "number": 3,
    "object": 4,
    "array": 5,
    "boolean": 6,
    "linkedin_profile_url": 9,
    "linkedin_company_url": 10,
    "company_domain": 11,
    "full_name": 12,
    "first_name": 13,
    "last_name": 14,
    "linkedin_profile": 15,
    "linkedin_company": 16,
    "company_headcount": 17,
    "company_summary": 18,
    "phone": 7,
    "email": 8,
    "post": 20,
    "interaction": 21,
    "AI": 19,
    "AI_formula": 22,
    "formula": 23,
    "company_name": 24,
    "job_title": 25,
}


def get_type_id(type_name: str) -> int:
    return column_type_map.get(type_name, 1)  # Default to text if not found


def get_type_name(input_value: str | int) -> str:
    # If the input is a string, convert it to a type ID first
    type_id = get_type_id(input_value) if isinstance(input_value, str) else input_value

    # Look up the type name from the dictionary
    for key, value in column_type_map.items():
        if value == type_id:
            return key
    return "text"  # Default if not found
