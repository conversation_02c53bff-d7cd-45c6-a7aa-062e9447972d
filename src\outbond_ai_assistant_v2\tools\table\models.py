"""Pydantic models for LinkedIn profile column creation and editing operations."""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, ConfigDict, field_validator

from src.outbond_ai_assistant_v2.supabase.table.models import FilterGroup, Filter, GroupOperator, Operator, Sort, SortDirection, TableBaseModel


class CreateTableRequest(BaseModel):
    """Request model for creating a new row.
    
    This model defines the structure for creating a new row with comprehensive
    validation and documentation following Google style guidelines.
    """
    model_config = ConfigDict(use_enum_values=True)

    organization_id: str = Field(
        ...,
        description="The unique identifier of the organization owning the table",
        min_length=1,
        examples=["org_c85117eb4106d464"]
    )
    
    name: str = Field(
        ...,
        description="Name of the table",
        min_length=1,
        max_length=255,
        examples=["Lead Generation Table", "Emails Table", "Phone Table", "Company Table", "Title Table", "Notes Table"]
    )

    description: Optional[str] = Field(
        default=None,
        description="Description of the table",
        min_length=1,
        max_length=255,
        examples=["This is a table for storing customer information"]
    )

    

class CreateTableResponse(BaseModel):
    """Response model for row creation operations.
    
    This model defines the structure of successful row creation responses,
    providing clear typing and documentation.
    """
    model_config = ConfigDict(use_enum_values=True)
    
    table: Optional[TableBaseModel] = Field(
        default=None,
        description="The complete table data when operation is successful"
    )

    
    success: bool = Field(
        default=True,
        description="Indicates if the operation was successful"
    )

    error_message: Optional[str] = Field(
        default=None,
        description="Detailed error message explaining what went wrong"
    )



class UpdateTableRequest(BaseModel):
    """Request model for updating a table.
    
    This model defines the structure for updating a table with basic
    properties like name, and optional description.
    """
    model_config = ConfigDict(use_enum_values=True)



    name: Optional[str] = Field(
        default=None,
        description="Name of the table",
        min_length=1,
        max_length=255,
        examples=["Lead Generation Table", "Emails Table", "Phone Table", "Company Table"]
    )
    
    description: Optional[str] = Field(
        default=None,
        description="Description of the table",
        min_length=1,
        max_length=255,
        examples=["This is a table for storing customer information"]
    )
    
    filters: Optional[FilterGroup] = Field(
        default=None,
        description="Filters to apply to the table",
        examples=[{
            "operator": "GroupOperator.AND", "rules": [
                {
                    "column_name": "name",
                    "operator": "Operator.EQ",
                    "value": "John Doe"
                }
            ]
        }]
    )
    
    sorts: Optional[List[Sort]] = Field(
        default=None,
        description="Sorts to apply to the table",
        examples=[{"name": "John Doe", "direction": "SortDirection.ASC"}]
    )


class UpdateTableRequestWithNotifyFE(UpdateTableRequest):
    """Request model for updating a table with notify FE.
    
    This model defines the structure for updating a table with basic
    properties like name, and optional description.
    """
    model_config = ConfigDict(use_enum_values=True)


    table: UpdateTableRequest = Field(
        ...,
        description="Table request to update",
        examples=[UpdateTableRequest(
            name="Lead Generation Table",
            description="This is a table for storing customer information",
            filters=FilterGroup(
                operator=GroupOperator.AND,
                rules=[Filter(column_name="name", operator=Operator.EQ, value="John Doe")]
            ),
            sorts=[Sort(column_name="name", direction=SortDirection.ASC)]
        )]
    )

    is_notify_fe: bool = Field(
        default=False,
        description="Indicates if the notification should be sent to the frontend after the table is updated",
        examples=[True, False]
    )


class UpdateTableResponse(BaseModel):
    """Response model for updating a table.
    
    This model defines the structure for updating a table with basic
    properties like name, and optional description.
    """
    model_config = ConfigDict(use_enum_values=True)
    
    table: Optional[TableBaseModel] = Field(
        default=None,
        description="The complete table data when operation is successful"
    )

    success: bool = Field(
        default=True,
        description="Indicates if the operation was successful"
    )

    error_message: Optional[str] = Field(
        default=None,
        description="Detailed error message explaining what went wrong"
    )