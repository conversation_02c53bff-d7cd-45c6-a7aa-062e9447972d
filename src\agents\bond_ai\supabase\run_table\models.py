"""Pydantic models for the run_table RPC."""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, ConfigDict


class RunTableRequest(BaseModel):
    """Request model for the `run_table` RPC.

    Mirrors the RPC parameters and follows the same conventions as other
    models under `supabase/`.
    """

    model_config = ConfigDict(use_enum_values=True)

    table_id: str = Field(
        ..., description="The ID of the table to run", examples=["tbl_example"]
    )
    column_names: Optional[List[str]] = Field(
        default=None,
        description="Array of specific column names to run. If None, uses all runnable columns.",
    )
    row_ids: Optional[List[int]] = Field(
        default=None, description="Array of specific row IDs to run. If None, runs all rows."
    )
    apply_table_view: bool = Field(
        default=False, description="Whether to apply table's saved filters and sorts."
    )
    rows_limit: Optional[int] = Field(
        default=None, description="Maximum number of rows to process."
    )
    auto_run: bool = Field(
        default=False, description="Whether to create/use auto-run mode."
    )
    add_to_existing_run: bool = Field(
        default=False, description="Legacy parameter (no effect on logic)."
    )


class RunTableResponse(BaseModel):
    """Response model for the `run_table` RPC.

    This model reflects the documented response fields. All fields are optional
    since the RPC may return different shapes for success vs error cases.
    """

    status: Optional[str] = Field(default=None, description="'success' or 'error'")
    message: Optional[str] = Field(default=None, description="Descriptive message")
    run_id: Optional[int] = Field(default=None, description="Run identifier")
    cells_count: Optional[int] = Field(default=None, description="Queued cells count")
    columns_processed: Optional[List[str]] = Field(
        default=None, description="List of processed column names"
    )
    table_id: Optional[str] = Field(default=None, description="Table ID")
    auto_run: Optional[bool] = Field(default=None, description="Auto-run flag")
    added_to_existing: Optional[bool] = Field(
        default=None, description="Was added to an existing auto run"
    )
    apply_table_view: Optional[bool] = Field(
        default=None, description="Applied table view filters/sorts"
    )
    rows_limit_applied: Optional[int] = Field(
        default=None, description="Effective rows limit applied"
    )
    target_rows_count: Optional[int] = Field(
        default=None, description="Number of target rows considered"
    )
    missing_columns: Optional[List[str]] = Field(
        default=None, description="Missing columns when validation fails"
    )
    found_columns: Optional[List[str]] = Field(
        default=None, description="Found columns when validation fails"
    )


