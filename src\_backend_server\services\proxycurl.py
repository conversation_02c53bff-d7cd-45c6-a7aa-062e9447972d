import os
import httpx
import logging
from typing import Dict, Any, Optional
from dotenv import load_dotenv
from src.backend_server.models.requests import LinkedinProfileRequest
from src.config import get_settings
from src.backend_server.services.llm_utils import update_cell_status
from src.backend_server.services.llm import _handle_formula_condition
from supabase import Client
from src.backend_server.services.proxycur_utils import transform_linkedin_profile
from src.backend_server.db.cells import (
    download_and_upload_image,
    reimburse_tokens,
    update_cell,
    upsert_cell_details,
)
from src.backend_server.db.cells import get_supabase_client

logger = logging.getLogger("backend_api.services.proxycurl")

settings = get_settings()


class RateLimitException(Exception):
    """Exception raised when the ProxyCurl API rate limit is reached."""

    def __init__(
        self, retry_after: Optional[int] = None, message: str = "Rate limit exceeded"
    ):
        self.retry_after = retry_after
        self.message = message
        super().__init__(self.message)


async def _handle_token_reimbursement(
    request: LinkedinProfileRequest, supabase_client: Client
) -> None:
    """
    Reimburse tokens if credits are available.

    Args:
        request: The LinkedIn profile request
        supabase_client: Supabase client
    """
    if request.credits > 0:
        await reimburse_tokens(
            organization_id=request.organization_id,
            tokens=request.credits,
            supabase=supabase_client,
        )


async def enrich_linkedin_profile(
    request: LinkedinProfileRequest,
    supabase_client: Client = get_supabase_client(),
):
    """
    Fetch LinkedIn profile data using the ProxyCurl API.

    Args:
        request: A LinkedinProfileRequest containing the LinkedIn profile URL

    Returns:
        Dictionary containing the LinkedIn profile data or error information
    """

    try:

        # Get cell data
        cell = (
            supabase_client.table("cells")
            .select("*")
            .eq("table_id", request.table_id)
            .eq("row_id", request.row_id)
            .eq("column_id", request.column_id)
            .execute()
            .data[0]
        )

        # Initialize processing status
        await update_cell_status(cell, "processing", "Processing...")

        # Check formula condition
        if not await _handle_formula_condition(request, cell, supabase_client):
            return

        api_endpoint = "https://nubela.co/proxycurl/api/v2/linkedin"
        headers = {
            "Authorization": f"Bearer {settings.PROXYCURL_API_KEY.get_secret_value()}"
        }

        params = {
            "linkedin_profile_url": request.linkedin_profile_url,
            "use_cache": "if-recent",
            "fallback_to_cache": "on-error",
        }
        await update_cell_status(cell, "processing", "Scraping profile...")
        async with httpx.AsyncClient() as client:
            response = await client.get(
                api_endpoint,
                headers=headers,
                params=params,
                timeout=60.0,  # 60 second timeout
            )

            if response.status_code == 404:
                await update_cell_status(cell, "failed", "Profile not found")
                await update_cell(
                    supabase_client,
                    request.column_id,
                    request.row_id,
                    request.table_id,
                    None,
                    {"run": "failed", "status": "Profile not found"},
                )
                # Reimburse tokens for profile not found
                await _handle_token_reimbursement(request, supabase_client)

            elif response.status_code == 429:
                # Handle rate limiting
                retry_after = response.headers.get("Retry-After")
                retry_seconds = (
                    int(retry_after) if retry_after and retry_after.isdigit() else None
                )
                rate_limit_message = f"Service unavailable. Try again later"

                logger.warning(
                    f"ProxyCurl API rate limit exceeded: {response.text}. Retry after: {retry_after}"
                )

                # Update cell status
                await update_cell_status(cell, "failed", rate_limit_message)
                await update_cell(
                    supabase_client,
                    request.column_id,
                    request.row_id,
                    request.table_id,
                    None,
                    {"run": "failed", "status": rate_limit_message},
                )

                # Reimburse tokens
                await _handle_token_reimbursement(request, supabase_client)

                # Raise the custom exception
                raise RateLimitException(
                    retry_after=retry_seconds, message=rate_limit_message
                )

            elif response.status_code == 200:
                data = response.json()

                if data.get("profile_pic_url"):
                    avatar_path = await download_and_upload_image(
                        supabase_client,
                        data["profile_pic_url"],
                        request.organization_id,
                        request.table_id,
                    )
                    cell["extras"] = {**cell["extras"], "avatar_url": avatar_path}
                data = transform_linkedin_profile(data)
                await update_cell_status(
                    cell, "completed", value=data[0]["value"], completed=True
                )

                await update_cell(
                    supabase_client,
                    request.column_id,
                    request.row_id,
                    request.table_id,
                    data[0]["value"],
                    {"run": "completed", "status": None},
                )
                await upsert_cell_details(
                    supabase_client,
                    request.table_id,
                    request.column_id,
                    request.row_id,
                    data,
                )

            else:
                logger.error(
                    f"ProxyCurl API error: {response.status_code} - {response.text}"
                )
                await update_cell_status(cell, "failed", "Failed to fetch profile data")
                await update_cell(
                    supabase_client,
                    request.column_id,
                    request.row_id,
                    request.table_id,
                    None,
                    {
                        "run": "failed",
                        "status": f"Failed to fetch profile data: {response.status_code}",
                    },
                )

                # Reimburse tokens for any error
                await _handle_token_reimbursement(request, supabase_client)
    except Exception as e:
        logger.error(f"Error fetching LinkedIn profile: {str(e)}")
        error_msg = f"AI processing error: {str(e)}"
        await update_cell_status(cell, "failed", error_msg)

        await update_cell(
            supabase_client,
            request.column_id,
            request.row_id,
            request.table_id,
            None,
            {"run": "failed", "status": error_msg},
        )

        await _handle_token_reimbursement(request, supabase_client)
