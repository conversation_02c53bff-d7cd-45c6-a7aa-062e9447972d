"""
This node is responsible for creating the steps for the research process.
"""

# pylint: disable=line-too-long

from typing import List
from datetime import datetime
from langchain_core.messages import HumanMessage
from langchain_core.runnables import RunnableConfig
from langchain.tools import tool

from pydantic import BaseModel, Field
from bond_ai.perplexity.state import AgentState
from bond_ai.perplexity.model import get_model

class SearchStep(BaseModel):
    """Model for a search step"""

    id: str = Field(description="The id of the step. This is used to identify the step in the state. Just make sure it is unique.")
    description: str = Field(description='The description of the step, i.e. "search for information about the latest AI news"')
    status: str = Field(description='The status of the step. Default to "pending".', enum=['pending','in_progress','complete'])
    type: str = Field(description='The type of step.', enum=['search'])


@tool
def SearchTool(steps: List[SearchStep]): # pylint: disable=invalid-name,unused-argument
    """
    Break the user's query into smaller steps.
    Use step type "search" to search the web for information.
    Make sure to add all the steps needed to answer the user's query.
    """


async def steps_node(state: AgentState, config: RunnableConfig):
    """
    The steps node is responsible for building the steps in the research process.
    """

    instructions = f"""
You are the sales intelligence research planner for Bond AI. Your task is to break down complex research queries into strategic steps that will enrich Sales Research Documents (SRD) with actionable sales insights.

RESEARCH PLANNING FOCUS:
- ICP (Ideal Customer Profile) enrichment: firmographics, market positioning, growth indicators
- Buyer persona development: decision-maker profiles, pain points, buying triggers
- Market signal detection: funding rounds, expansions, technology adoptions, hiring patterns
- Competitive intelligence: market share, positioning, competitive advantages
- Industry analysis: trends, challenges, regulatory changes, market opportunities

STEP CREATION GUIDELINES:
1. Prioritize searches that reveal sales-relevant insights
2. Focus on information that helps qualify prospects and identify opportunities
3. Structure steps to build comprehensive market and prospect intelligence
4. Consider temporal relevance - recent news, trends, and developments

Each step should contribute to building a complete sales intelligence picture that enables:
- Better prospect qualification (ICP alignment)
- Deeper buyer understanding (persona insights)
- Opportunity identification (market signals)
- Competitive positioning (market intelligence)

Break down the user's query into targeted research steps that will gather actionable sales intelligence.

The current date is {datetime.now().strftime("%Y-%m-%d")}.
"""

    response = await get_model(state).bind_tools(
        [SearchTool],
        tool_choice="SearchTool"
    ).ainvoke([
        state["messages"][0],
        HumanMessage(
            content=instructions
        ),
    ], config)

    if len(response.tool_calls) == 0:
        steps = []
    else:
        steps = response.tool_calls[0]["args"]["steps"]

    if len(steps) != 0:
        steps[0]["updates"] = ["Searching the web..."]

    return {
        "steps": steps,
    }
