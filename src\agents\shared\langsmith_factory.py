import os
import asyncio
from langsmith import Client

from src.core.config import get_settings

# Singleton client instance
_langsmith_client = None

def _get_langsmith_client() -> Client:
    """Get configured LangSmith client (singleton).
    
    Returns:
        Configured LangSmith client.
        
    Raises:
        ValueError: If LangSmith API key is not available.
    """
    global _langsmith_client
    
    if _langsmith_client is None:
        settings = get_settings()
        
        if not settings.LANGSMITH_API_KEY:
            raise ValueError("LANGSMITH_API_KEY not found in settings")
        
        _langsmith_client = Client(api_key=settings.LANGSMITH_API_KEY.get_secret_value())
    
    return _langsmith_client

def _pull_prompt_sync(prompt_name: str):
    """Synchronous helper to pull prompt from LangSmith."""
    client = _get_langsmith_client()
    return client.pull_prompt(prompt_name, include_model=True)

async def get_langsmith_prompt_async(prompt_name: str):
    """Get a prompt from LangSmith.
    
    Args:
        prompt_name: Name of the prompt to retrieve
        
    Returns:
        The prompt object from Lang<PERSON><PERSON>
        
    Raises:
        ValueError: If LangSmith API key is not available.
    """
    prompt = await asyncio.to_thread(_pull_prompt_sync, prompt_name)
    return prompt


