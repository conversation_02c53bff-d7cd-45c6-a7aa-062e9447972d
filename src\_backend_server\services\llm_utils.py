"""
Utility functions for LLM services.
"""

import json
import logging
from typing import Dict, Any, List, Optional
from urllib.parse import urlparse

import httpx
from supabase import Client

# Import centralized configuration
from src.core.config import get_settings
from src.db.utils import Cell

# Get application settings
settings = get_settings()
logger = logging.getLogger("backend_api.services.llm_utils")


async def send_supabase_broadcast(topic: str, payload: Dict[str, Any]) -> None:
    """
    Send a broadcast message to Supabase Realtime using httpx.

    Args:
        topic: The topic/channel to broadcast to
        payload: The payload to send in the broadcast

    Returns:
        None
    """
    if not settings.SUPABASE_URL or not settings.SUPABASE_KEY:
        logger.error("Missing Supabase URL or API key for realtime broadcast")
        return

    url = f"{settings.SUPABASE_URL}/realtime/v1/api/broadcast"

    # Convert SecretStr to regular string if needed
    api_key = settings.SUPABASE_KEY
    if hasattr(api_key, "get_secret_value"):
        api_key = api_key.get_secret_value()

    headers = {"apikey": api_key, "Content-Type": "application/json"}
    data = {
        "messages": [
            {
                "topic": topic,
                "event": "CELL_UPDATE",
                "payload": payload,
            }
        ]
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, json=data)
            response.raise_for_status()
            logger.info(f"Broadcast sent successfully to {topic}")
    except Exception as e:
        logger.error(f"Error sending broadcast to Supabase Realtime: {str(e)}")


def extract_domain(url: str) -> str:
    """
    Extract just the domain from a URL.

    Args:
        url: The URL to extract domain from

    Returns:
        str: The domain or original URL if parsing fails
    """
    try:
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
        return domain if domain else url
    except:
        return url


def extract_tool_arg(
    tool_call: Dict[str, Any], arg_names: Optional[List[str]] = None
) -> str:
    """
    Extract argument from a tool call with flexible argument naming.

    Args:
        tool_call: The tool call dictionary
        arg_names: List of possible argument names to check

    Returns:
        str: The extracted argument value or "unknown" if not found
    """
    if arg_names is None:
        arg_names = ["__arg1", "query", "url"]

    args = tool_call["args"]

    # Handle dictionary arguments
    if isinstance(args, dict):
        for name in arg_names:
            if name in args:
                return args[name]

    # Handle string arguments (JSON)
    elif isinstance(args, str):
        try:
            args_dict = json.loads(args)
            for name in arg_names:
                if name in args_dict:
                    return args_dict[name]
        except:
            return args

    return "unknown"


async def update_cell_status(
    cell: Cell,
    status: str,
    message: Optional[str] = None,
    value: Optional[str] = None,
    completed: bool = False,
) -> Cell:
    """
    Update cell status and broadcast the change.

    Args:
        cell: The cell dictionary to update
        status: Status code (processing, completed, failed, etc.)
        message: Optional status message
        value: Optional cell value to update
        completed: Whether this is a completion status

    Returns:
        Cell: The updated cell
    """
    if completed:
        cell.run_status = {"run": status, "status": None}
    else:
        cell.run_status = {"run": status, "status": message}

    if value is not None:
        cell.value = value

    await send_supabase_broadcast(cell.table_id, cell.model_dump())
    return cell
