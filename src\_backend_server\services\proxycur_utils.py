from src.backend_server.services.utils import get_type_id, get_type_name, format_date


def transform_linkedin_profile(src: dict) -> list:
    return [
        {
            "name": "LinkedIn Profile",
            "type_id": get_type_id("linkedin_profile"),
            "type": get_type_name("linkedin_profile"),
            "is_brand": True,
            "value": f"{src.get('first_name', '')} {src.get('last_name', '')}",
        },
        {
            "name": "profile_picrture",
            "type_id": get_type_id("linkedin_profile_url"),
            "type": get_type_name("linkedin_profile_url"),
            "is_brand": False,
            "value": src.get("profile_pic_url", ""),
        },
        {
            "name": "Personal Info",
            "type_id": get_type_id("object"),
            "type": get_type_name("object"),
            "is_brand": False,
            "injection_sequence": "1",
            "value": [
                {
                    "name": "Full Name",
                    "type_id": get_type_id("full_name"),
                    "type": get_type_name("full_name"),
                    "is_brand": False,
                    "value": f"{src.get('first_name', '')} {src.get('last_name', '')}",
                    "injection_sequence": "1.0",
                },
                {
                    "name": "First Name",
                    "type_id": get_type_id("first_name"),
                    "type": get_type_name("first_name"),
                    "is_brand": False,
                    "value": src.get("first_name", ""),
                    "injection_sequence": "1.1",
                },
                {
                    "name": "Last Name",
                    "type_id": get_type_id("last_name"),
                    "type": get_type_name("last_name"),
                    "is_brand": False,
                    "value": src.get("last_name", ""),
                    "injection_sequence": "1.2",
                },
                {
                    "name": "Headline",
                    "type_id": get_type_id("job_title"),
                    "type": get_type_name("job_title"),
                    "is_brand": False,
                    "value": src.get("headline", ""),
                    "injection_sequence": "1.3",
                },
                {
                    "name": "Summary",
                    "type_id": get_type_id("text"),
                    "type": get_type_name("text"),
                    "is_brand": False,
                    "value": src.get("summary", ""),
                    "injection_sequence": "1.4",
                },
                {
                    "name": "Country",
                    "type_id": get_type_id("text"),
                    "type": get_type_name("text"),
                    "is_brand": False,
                    "value": src.get("country_full_name", ""),
                    "injection_sequence": "1.5",
                },
                {
                    "name": "City",
                    "type_id": get_type_id("text"),
                    "type": get_type_name("text"),
                    "is_brand": False,
                    "value": src.get("city", ""),
                    "injection_sequence": "1.6",
                },
                {
                    "name": "Username",
                    "type_id": get_type_id("text"),
                    "type": get_type_name("text"),
                    "is_brand": False,
                    "value": src.get("public_identifier", ""),
                    "injection_sequence": "1.7",
                },
            ],
        },
        {
            "name": "Experience",
            "type_id": get_type_id("array"),
            "type": get_type_name("array"),
            "is_brand": False,
            "injection_sequence": "2",
            "value": [
                {
                    "name": exp.get("title", ""),
                    "type_id": get_type_id("object"),
                    "type": get_type_name("object"),
                    "is_brand": False,
                    "injection_sequence": f"2.{index}",
                    "value": [
                        {
                            "name": "Job Title",
                            "type_id": get_type_id("job_title"),
                            "type": get_type_name("job_title"),
                            "is_brand": False,
                            "value": exp.get("title", ""),
                            "injection_sequence": f"2.{index}.0",
                        },
                        {
                            "name": "Company Name",
                            "type_id": get_type_id("company_name"),
                            "type": get_type_name("company_name"),
                            "is_brand": False,
                            "value": exp.get("company", ""),
                            "injection_sequence": f"2.{index}.1",
                        },
                        {
                            "name": "Company URL",
                            "type_id": get_type_id("linkedin_company_url"),
                            "type": get_type_name("linkedin_company_url"),
                            "is_brand": False,
                            "value": exp.get("company_linkedin_profile_url", ""),
                            "injection_sequence": f"2.{index}.2",
                        },
                        {
                            "name": "Location",
                            "type_id": get_type_id("text"),
                            "type": get_type_name("text"),
                            "is_brand": False,
                            "value": exp.get("location", ""),
                            "injection_sequence": f"2.{index}.3",
                        },
                        {
                            "name": "Start Date",
                            "type_id": get_type_id("date"),
                            "type": get_type_name("date"),
                            "is_brand": False,
                            "value": format_date(exp.get("starts_at")),
                            "injection_sequence": f"2.{index}.4",
                        },
                        {
                            "name": "End Date",
                            "type_id": get_type_id("date"),
                            "type": get_type_name("date"),
                            "is_brand": False,
                            "value": format_date(exp.get("ends_at")) if exp.get("ends_at") else "current",
                            "injection_sequence": f"2.{index}.5",
                        },
                    ],
                }
                for index, exp in enumerate(src.get("experiences", []) or [])
            ],
        },
        {
            "name": "Education",
            "type_id": get_type_id("array"),
            "type": get_type_name("array"),
            "is_brand": False,
            "injection_sequence": "3",
            "value": [
                {
                    "name": edu.get("school", ""),
                    "type_id": get_type_id("object"),
                    "type": get_type_name("object"),
                    "is_brand": False,
                    "injection_sequence": f"3.{index}",
                    "value": [
                        {
                            "name": "School",
                            "type_id": get_type_id("company_name"),
                            "type": get_type_name("company_name"),
                            "is_brand": False,
                            "value": edu.get("school", ""),
                            "injection_sequence": f"3.{index}.0",
                        },
                        {
                            "name": "Field of Study",
                            "type_id": get_type_id("text"),
                            "type": get_type_name("text"),
                            "is_brand": False,
                            "value": edu.get("field_of_study", ""),
                            "injection_sequence": f"3.{index}.1",
                        },
                        {
                            "name": "Degree",
                            "type_id": get_type_id("text"),
                            "type": get_type_name("text"),
                            "is_brand": False,
                            "value": edu.get("degree_name", ""),
                            "injection_sequence": f"3.{index}.2",
                        },
                        {
                            "name": "Description",
                            "type_id": get_type_id("text"),
                            "type": get_type_name("text"),
                            "is_brand": False,
                            "value": edu.get("description", ""),
                            "injection_sequence": f"3.{index}.3",
                        },
                        {
                            "name": "School URL",
                            "type_id": get_type_id("linkedin_company_url"),
                            "type": get_type_name("linkedin_company_url"),
                            "is_brand": False,
                            "value": edu.get("school_linkedin_profile_url", ""),
                            "injection_sequence": f"3.{index}.4",
                        },
                        {
                            "name": "Start Date",
                            "type_id": get_type_id("date"),
                            "type": get_type_name("date"),
                            "is_brand": False,
                            "value": format_date(edu.get("starts_at")),
                            "injection_sequence": f"3.{index}.5",
                        },
                        {
                            "name": "End Date",
                            "type_id": get_type_id("date"),
                            "type": get_type_name("date"),
                            "is_brand": False,
                            "value": format_date(edu.get("ends_at")) if edu.get("ends_at") else "current",
                            "injection_sequence": f"3.{index}.6",
                        },
                    ],
                }
                for index, edu in enumerate(src.get("education", []) or [])
            ],
        },
        {
            "name": "Certifications",
            "type_id": get_type_id("array"),
            "type": get_type_name("array"),
            "is_brand": False,
            "injection_sequence": "4",
            "value": [
                {
                    "name": cert.get("name", ""),
                    "type_id": get_type_id("object"),
                    "type": get_type_name("object"),
                    "is_brand": False,
                    "injection_sequence": f"4.{index}",
                    "value": [
                        {
                            "name": "Name",
                            "type_id": get_type_id("text"),
                            "type": get_type_name("text"),
                            "is_brand": False,
                            "value": cert.get("name", ""),
                            "injection_sequence": f"4.{index}.0",
                        },
                        {
                            "name": "Authority",
                            "type_id": get_type_id("text"),
                            "type": get_type_name("text"),
                            "is_brand": False,
                            "value": cert.get("authority", ""),
                            "injection_sequence": f"4.{index}.1",
                        },
                        {
                            "name": "Start Date",
                            "type_id": get_type_id("date"),
                            "type": get_type_name("date"),
                            "is_brand": False,
                            "value": format_date(cert.get("starts_at")),
                            "injection_sequence": f"4.{index}.2",
                        },
                        {
                            "name": "End Date",
                            "type_id": get_type_id("date"),
                            "type": get_type_name("date"),
                            "is_brand": False,
                            "value": format_date(cert.get("ends_at")) if cert.get("ends_at") else "current",
                            "injection_sequence": f"4.{index}.3",
                        },
                    ],
                }
                for index, cert in enumerate(src.get("certifications", []) or [])
            ],
        },
    ]
