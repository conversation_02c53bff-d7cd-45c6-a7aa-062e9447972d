from pydantic import BaseModel, ConfigDict, Field
from typing import Optional, List, Tuple, Union, Literal
from src.schemas.linkedin import LinkedinCompanyProfileUrl, LinkedinPersonProfileUrl


# Date Object Model
class DateObject(BaseModel):
    model_config = ConfigDict(extra="ignore")
    day: Optional[int] = None
    month: Optional[int] = None
    year: Optional[int] = None


# Location Model
class CompanyLocation(BaseModel):
    model_config = ConfigDict(extra="ignore")
    city: Optional[str] = None
    country: Optional[str] = None
    postal_code: Optional[str] = None
    line_1: Optional[str] = None
    is_hq: Optional[bool] = None
    state: Optional[str] = None


# Similar Company Model
class SimilarCompany(BaseModel):
    model_config = ConfigDict(extra="ignore")
    name: Optional[str] = None
    link: Optional[str] = None
    industry: Optional[str] = None
    location: Optional[str] = None


# Affiliated Company Model
class AffiliatedCompany(BaseModel):
    model_config = ConfigDict(extra="ignore")
    name: Optional[str] = None
    link: Optional[str] = None
    industry: Optional[str] = None
    location: Optional[str] = None


# Company Update Model
class CompanyUpdate(BaseModel):
    model_config = ConfigDict(extra="ignore")
    article_link: Optional[str] = None
    image: Optional[str] = None
    posted_on: Optional[DateObject] = None
    text: Optional[str] = None
    total_likes: Optional[int] = None


# Acquired Company Model
class AcquiredCompany(BaseModel):
    model_config = ConfigDict(extra="ignore")
    linkedin_profile_url: Optional[LinkedinCompanyProfileUrl] = None
    crunchbase_profile_url: Optional[str] = None
    announced_date: Optional[DateObject] = None
    price: Optional[float] = None


# Acquisitor Model
class Acquisitor(BaseModel):
    model_config = ConfigDict(extra="ignore")
    linkedin_profile_url: Optional[LinkedinCompanyProfileUrl] = None
    crunchbase_profile_url: Optional[str] = None
    announced_date: Optional[DateObject] = None
    price: Optional[float] = None


# Acquisition Model
class Acquisition(BaseModel):
    model_config = ConfigDict(extra="ignore")
    acquired: Optional[List[AcquiredCompany]] = None
    acquired_by: Optional[Acquisitor] = None


# Exit Data Model
class Exit(BaseModel):
    model_config = ConfigDict(extra="ignore")
    linkedin_profile_url: Optional[LinkedinCompanyProfileUrl] = None
    crunchbase_profile_url: Optional[str] = None
    name: Optional[str] = None


# Investor Model
class Investor(BaseModel):
    model_config = ConfigDict(extra="ignore")
    name: Optional[str] = None
    linkedin_profile_url: Optional[LinkedinPersonProfileUrl] = None
    type: Optional[str] = None


# Funding Data Model
class FundingData(BaseModel):
    model_config = ConfigDict(extra="ignore")
    funding_type: Optional[str] = None
    announced_date: Optional[DateObject] = None
    money_raised: Optional[Union[str, float]] = None
    number_of_investor: Optional[int] = None
    investor_list: Optional[List[Investor]] = None


# Extra Company Data
class CompanyExtra(BaseModel):
    model_config = ConfigDict(extra="ignore")
    crunchbase_profile_url: Optional[str] = None
    ipo_status: Optional[str] = None
    crunchbase_rank: Optional[int] = None
    founding_date: Optional[DateObject] = None
    operating_status: Optional[str] = None
    company_type: Optional[str] = None
    contact_email: Optional[str] = None
    phone_number: Optional[str] = None
    facebook_id: Optional[str] = None
    twitter_id: Optional[str] = None
    number_of_funding_rounds: Optional[int] = None
    total_funding_amount: Optional[float] = None
    stock_symbol: Optional[str] = None
    ipo_date: Optional[DateObject] = None
    number_of_lead_investors: Optional[int] = None
    number_of_investors: Optional[int] = None
    total_fund_raised: Optional[float] = None
    number_of_investments: Optional[int] = None
    number_of_lead_investments: Optional[int] = None
    number_of_exits: Optional[int] = None
    number_of_acquisitions: Optional[int] = None


# Company Profile Output Model
class CompanyProfileOutput(BaseModel):
    model_config = ConfigDict(extra="ignore")
    name: Optional[str] = None
    universal_name_id: Optional[str] = None
    linkedin_internal_id: Optional[str] = None
    search_id: Optional[str] = None
    website: Optional[str] = None
    tagline: Optional[Union[str, None]] = None
    description: Optional[str] = None
    company_type: Optional[
        Literal[
            "EDUCATIONAL",
            "GOVERNMENT_AGENCY",
            "NON_PROFIT",
            "PARTNERSHIP",
            "PRIVATELY_HELD",
            "PUBLIC_COMPANY",
            "SELF_EMPLOYED",
            "SELF_OWNED",
        ]
    ] = None
    company_size: Optional[Tuple[int, Optional[int]]] = None
    company_size_on_linkedin: Optional[int] = None
    follower_count: Optional[int] = None
    founded_year: Optional[Union[int, None]] = None
    industry: Optional[str] = None
    profile_pic_url: Optional[str] = None
    background_cover_image_url: Optional[str] = None
    specialities: Optional[List[str]] = None
    hq: Optional[CompanyLocation] = None
    locations: Optional[List[CompanyLocation]] = None
    updates: Optional[List[CompanyUpdate]] = None
    similar_companies: Optional[List[SimilarCompany]] = None
    affiliated_companies: Optional[List[AffiliatedCompany]] = None
    funding_data: Optional[List[FundingData]] = None
    acquisitions: Optional[List[Acquisition]] = None
    exit_data: Optional[List[Exit]] = None
    categories: Optional[List[str]] = None
    extra: Optional[CompanyExtra] = None


# Response model with credit cost
class CompanyProfileResponse(BaseModel):
    profile: CompanyProfileOutput
    credit_cost: int = 0


# Input model
class CompanyProfileInput(BaseModel):
    linkedin_company_url: LinkedinCompanyProfileUrl


class LookupCompanyProfileInput(BaseModel):
    company_domain: str
