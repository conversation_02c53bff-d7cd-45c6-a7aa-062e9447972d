from src.db.optimised_utils import (
    get_cell,
    update_cell_and_notify,
    get_supabase_client,
    reimburse_tokens,
)
from ..celery_app import app
import logging
from supabase import AsyncClient
import asyncio
from typing import Dict, Any, Optional

logger = logging.getLogger("src.worker.tasks.db")


@app.task(name="src.worker.tasks.db.reimburse", ignore_result=False, queue="default")
def reimburse(previous: Optional[Dict[str, Any]], input: Dict[str, Any]):
    async def _reimburse(previous: Optional[Dict[str, Any]], input: Dict[str, Any]):
        if previous and (
            previous.get("success", False) is True
            or previous.get("validated", False) is True
            or previous.get("result_found", False) is True
        ):
            return previous
        else:
            supabase_client: AsyncClient = await get_supabase_client()
            if previous.get("only_if", True) is True:
                cell = await get_cell(
                    supabase_client,
                    input,
                )
                cell["run_status"] = {
                    "run": "failed",
                    "message": "No result",
                }
                cell["value"] = None
                cell["extras"] = None
                await update_cell_and_notify(
                    supabase_client,
                    cell,
                )
            await reimburse_tokens(supabase_client, input)
            return {"success": False, "data": previous}

    return asyncio.run(_reimburse(previous, input))
