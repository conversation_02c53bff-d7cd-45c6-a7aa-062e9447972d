"""Work email column creation tool."""

from typing import <PERSON><PERSON>, <PERSON><PERSON>, Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool, InjectedToolArg
from langgraph.config import get_stream_writer
from bond_ai.configuration import Configuration
import json
import logging

from ..agent_db import upsert_smart_column
from langgraph.types import Command
from langchain_core.messages import ToolMessage
from langchain_core.tools import  InjectedToolCallId
from ..utilities.run_and_store_results_summery import run_and_store_results_summary
from ..supabase.run_table import RunTableRequest
from ..supabase.validate_injection_sequance.validate_injection_sequance import (
    validate_injection_sequence,
)
from ..supabase.validate_injection_sequance.models import (
    ValidateInjectionSequenceRequest,
    TargetDataType,
)

@tool
def upsert_work_email_column(
    tool_call_id: Annotated[str, InjectedToolCallId],
    config: Annotated[RunnableConfig, InjectedToolArg],
    column_name: str,
    full_name: str,
    company_domain: str,
    column_id: Optional[int] = None,
) -> Command:
    """Create a new work email column or update an existing one using name and company domain.
    
    IMPORTANT: ALWAYS provide the column_id if you want to update/edit an existing column. Do NOT duplicate the column by NOT providing the column_id.
    Parameters:
        config: Configuration injected by the system
        column_name: A short descriptive name of the column. Must be unique per table.
        full_name: A SINGLE injection path of the full name data point.
        company_domain: A SINGLE injection path of the company domain data point.
        column_id: Optional ID of the existing column to update/edit. If not provided, a new column will be created.
        
    Returns:
        Command: Updates graph state with a ToolMessage and, on success, appends a memory reference to memory_refs.
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Working on {column_name} column"})
        # Set up metadata
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        service_id = 10
        
        # Normalize injection paths
        full_name = "_".join(full_name.strip().lower().split())
        full_name = f"$${full_name}$$"
        company_domain = "_".join(company_domain.strip().lower().split())
        company_domain = f"$${company_domain}$$"

        logging.info(
            f"\n[Debug] upsert_work_email_column:\n  **Column name: {column_name}\n  **full_name: {full_name}\n  **company_domain: {company_domain}\n  **column_id: {column_id}\n"
        )

        # Validate injection sequences
        full_name_validation = validate_injection_sequence(
            config=config,
            request=ValidateInjectionSequenceRequest(
                injection_sequence=full_name,
                target_data_type=TargetDataType.FULL_NAME,
            ),
        )
        if not full_name_validation.success:
            msg = full_name_validation.error_message or "Validation failed for the provided full_name injection sequence."
            return Command(update={
                "last_error_message": msg,
                "messages": [
                    ToolMessage(
                        content=msg,
                        tool_call_id=tool_call_id,
                    )
                ]
            })

        domain_validation = validate_injection_sequence(
            config=config,
            request=ValidateInjectionSequenceRequest(
                injection_sequence=company_domain,
                target_data_type=TargetDataType.DOMAIN,
            ),
        )
        if not domain_validation.success:
            msg = domain_validation.error_message or "Validation failed for the provided company_domain injection sequence."
            return Command(update={
                "last_error_message": msg,
                "messages": [
                    ToolMessage(
                        content=msg,
                        tool_call_id=tool_call_id,
                    )
                ]
            })
        
        inputs = [
            {"full_name": full_name},
            {"company_domain": company_domain}
        ]
        
        # Set up providers
        providers = [
            {"email_providers": ["leadmagic", "findymail", "prospeo"]},
            {"verify_providers": ["millionverifier"]}
        ]
        
        # Set up parameters (empty dict as in the payload)
        parameters = [{}]
        
        # Call the create_smart_column function
        response, error = upsert_smart_column(
            table_id=table_id,
            column_name=column_name,
            service_id=service_id,
            inputs=inputs,
            parameters=parameters,
            providers=providers,
            column_id=column_id
        )
        
        if error:
            return Command(update={
                "last_error_message": error,
                "messages": [
                    ToolMessage(
                        content=error,
                        tool_call_id=tool_call_id
                    )
                ]
            })
            
            
        # Extract column details from response
        column = response.get('column', {})
        column_id = column.get('id')
        column_type = column.get('type') 
        name = column.get('name')
        
        action = "updated" if column_id is not None else "created"
        
        # Create JSON response
        response_json = {
            "action": action,
            "column_name": name,
            "column_id": column_id,
            "message": f"The work email column was successfully {action}."
        }

        # Late imports to avoid circular imports
        from ..memory.vector_store import memory_store
        from ..registry.registry import SupervisorSubAgents

        memory_content = (
            f"Successfully {action} work email column '{column_name}' using inputs: "
            f"full_name={full_name}, company_domain={company_domain}"
        )
        memory_ref = memory_store.write(
            SupervisorSubAgents.linkedin_enrichment_agent.value.name,
            memory_content,
        )

        # After successful create/update, run first 5 rows for this column and wait for results
        last_error_message: str | None = None
        try:
            run_request = RunTableRequest(
                table_id=table_id,
                column_names=[name] if name else None,
                apply_table_view=True,
            )
            run_data, run_error = run_and_store_results_summary(
                config=config,
                request=run_request,
                poll_interval_seconds=1.0,
                max_attempts=10,
            )
            if run_error:
                last_error_message = f"Run initiation/polling error: {run_error}"
                run_section = {
                    "action": "ran_first_5_rows",
                    "status": "error",
                    "message": last_error_message,
                }
            else:
                run_section = {
                    "action": "ran_first_5_rows",
                    "status": "success",
                    "column_name": name,
                    "message": "Completed polling for first 5 rows.",
                    "data": run_data,
                }
        except Exception as run_exc:
            last_error_message = f"Exception during run & summary: {str(run_exc)}"
            run_section = {
                "action": "ran_first_5_rows",
                "status": "exception",
                "message": last_error_message,
            }

        # Single tool result message combining upsert and run summaries
        combined_payload = {
            "upsert": response_json,
            "run": run_section,
        }

        return Command(update={
            "last_error_message": last_error_message,
            "memory_refs": [memory_ref],
            "messages": [
                ToolMessage(
                    content=json.dumps(combined_payload),
                    tool_call_id=tool_call_id,
                )
            ],
        })
        
    except Exception as e:
         return Command(update={
                "last_error_message": str(e),
                "messages": [
                    ToolMessage(
                        content= str(e),
                        tool_call_id=tool_call_id,
                    )
                ]
            })
