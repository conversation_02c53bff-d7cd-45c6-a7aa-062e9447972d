"""LinkedIn profile column creation and editing functionality."""

from typing import Union
import src.outbond_ai_assistant_v2.supabase.create_rows.models as supabase_models
import src.outbond_ai_assistant_v2.supabase.create_rows.create_rows as supabase_create_rows
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool
from typing import Annotated
from .models import (
    CreateRowsRequest,
    CreateRowsResponse,
)
from src.outbond_ai_assistant_v2.configuration import Configuration
from .models import CreateRowsSingleResponse

@tool
def create_rows_tool(
    config: Annotated[RunnableConfig, InjectedToolArg],
    request: CreateRowsRequest
) -> Union[CreateRowsResponse, supabase_models.CreateRowsResponse]:
    """Create a row with optional cell values.
    
    This function provides a complete workflow for row creation:
    1. Creates the row with optional cell values
    2. Returns detailed success or error information
    
    Args:
        request (CreateRowsRequest): The validated request model containing
                                                           all necessary parameters for rows creation
        
    Returns:
        CreateRowsResponse: Response model containing either
        success data (CreateRowsResponse) or error information (CreateRowsError)
    """
    try:
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id


        
        # Step 4: Create upsert request
        create_rows_request = supabase_models.CreateRowsRequest(
            table_id=table_id,
            data=request.data if request.data and len(request.data) > 0 else [{} for _ in range(request.row_count)],
        )
        
        # Step 5: Execute column creation/update
        create_rows_result = supabase_create_rows.create_rows(config=config, request=create_rows_request)
        
        # If upsert failed, return the upsert response directly
        if not create_rows_result.success:
            return create_rows_result
        
        # Convert supabase response format to tools response format
        converted_rows = None
        if create_rows_result.rows:

            converted_rows = [
                CreateRowsSingleResponse(
                    id=row.id,
                    table_id=row.table_id,
                    cells=row.cells
                )
                for row in create_rows_result.rows
            ]
        
        return CreateRowsResponse(
            rows=converted_rows,
            success=True
        )
        
    except Exception as e:
        error_msg = f"Unexpected error in row creation: {str(e)}"
        return CreateRowsResponse(
            error_message=error_msg,
            success=False
        )