FROM python:3.13-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    postgresql-client \
    netcat-traditional \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy application code
COPY . /app

# Install dependencies from pyproject.toml
RUN pip install --no-cache-dir . langgraph==0.4.3

# Copy and set permissions on entrypoint script
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# Set up environment variables
ENV ENV=${ENV:-production} \
    DEBUG=${DEBUG:-false} \
    LANGSMITH_PROJECT=${LANGSMITH_PROJECT:-outboud-ai} \
    LANGSMITH_API_KEY=${LANGSMITH_API_KEY:-} \
    SUPABASE_URL=${SUPABASE_URL:-} \
    SUPABASE_KEY=${SUPABASE_KEY:-} \
    SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY:-} \
    REDIS_URL=${REDIS_URL:-} \
    CELERY_BROKER_URL=${CELERY_BROKER_URL:-} \
    CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND:-} \
    POSTGRES_URL=${POSTGRES_URL:-} \
    OPENAI_API_KEY=${OPENAI_API_KEY:-} \
    ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-} \
    FIREWORKS_API_KEY=${FIREWORKS_API_KEY:-} \
    PROXYCURL_API_KEY=${PROXYCURL_API_KEY:-} \
    LEADMAGIC_API_KEY=${LEADMAGIC_API_KEY:-} \
    PROSPEO_API_KEY=${PROSPEO_API_KEY:-} \
    FINDYMAIL_API_KEY=${FINDYMAIL_API_KEY:-} \
    MILLIONVERIFIER_API_KEY=${MILLIONVERIFIER_API_KEY:-} \
    RUN_ONLY_IF_MODEL=${RUN_ONLY_IF_MODEL:-Bedrock/us.amazon.nova-micro-v1:0} \
    AI_COLUMN_MODEL=${AI_COLUMN_MODEL:-openai/gpt-4o-mini} \
    AI_FORMULA_MODEL=${AI_FORMULA_MODEL:-Bedrock/us.amazon.nova-micro-v1:0} \
    AI_RESEARCH_MODEL=${AI_RESEARCH_MODEL:-Bedrock/us.amazon.nova-micro-v1:0} \
    TAVILY_API_KEY=${TAVILY_API_KEY:-} \
    FIRECRAWL_API_KEY=${FIRECRAWL_API_KEY:-} \
    AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID:-} \
    AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY:-}

# Create a non-root user
RUN useradd -m appuser
RUN chown -R appuser:appuser /app
USER appuser

ENTRYPOINT ["/entrypoint.sh"] 