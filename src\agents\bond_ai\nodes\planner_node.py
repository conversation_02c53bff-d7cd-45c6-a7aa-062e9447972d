from __future__ import annotations

# Stdlib
from collections import OrderedDict
import json as pyjson
from typing import Any

# Project imports
from bond_ai.models.planner_model import Plan, Task
from bond_ai.utils import load_chat_model
from bond_ai.configuration import Configuration
from bond_ai.state import BondAIWorkflowState
from bond_ai.prompts_v1 import PLANNER_AGENT_PROMPT
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langchain_core.runnables import RunnableConfig

# NOTE: removed unused import `all_tools`
# from bond_ai.tools import all_tools

# -------------------------
# Small helpers
# -------------------------

def _tool_name(t: Any) -> str | None:
    if t is None:
        return None
    if hasattr(t, "name") and isinstance(getattr(t, "name"), str):
        return t.name
    if hasattr(t, "__name__"):
        return t.__name__
    if isinstance(t, dict) and "name" in t:
        return str(t["name"]) or None
    if isinstance(t, str):
        return t.strip() or None
    return str(t)


def _tool_desc(t: Any) -> str:
    if t is None:
        return ""
    if isinstance(t, dict) and "description" in t:
        return t["description"] or ""
    if hasattr(t, "description") and isinstance(getattr(t, "description"), str):
        return t.description or ""
    return ""


def _uniq_sorted(seq: list[str]) -> list[str]:
    seen = set()
    out = []
    for x in seq:
        if x and x not in seen:
            seen.add(x)
            out.append(x)
    return sorted(out)


## TODO transform in async
async def planner_agent(
    state: BondAIWorkflowState,
    config: RunnableConfig,
):
    """Planning node that analyzes user requests and creates comprehensive plans.

    This node:
    1. Analyzes the user's request to understand intent and complexity
    2. Creates a detailed plan with specific, actionable tasks
    3. Estimates task durations and identifies required tools
    4. Sets up dependencies between tasks
    5. Updates the conversation phase to planning
    """
    configuration = Configuration.from_runnable_config(config)
  
    
    
    # Validate state
    if not isinstance(state, dict):
        print(f"ERROR: state is not a dict, it's a {type(state)}: {state}")
        return {
            "messages": [AIMessage(content="Internal error: Invalid state format.")],
            "last_error_message": f"State type error: expected dict, got {type(state)}",
        }

    messages = state.get("messages", [])
    if not messages:
        return {"messages": [AIMessage(content="No messages to plan for.")]}  

    # Find latest AI message from the supervisor
    supervisor_instructions = None
    for msg in reversed(messages):
        if getattr(msg, "type", None) == "ai" and getattr(msg, "name", None) == "supervisor":
            supervisor_instructions = msg
            break
    if not supervisor_instructions:
        return {
            "last_error_message": "No message from Supervisor found to create a plan for.",
            "messages": [AIMessage(content="No message from Supervisor found to create a plan for.")],
            }

    # -------------------------
    # Build dynamic injections
    # -------------------------
    from bond_ai.registry import supervisor_sub_agent_registry

    react_agents = supervisor_sub_agent_registry.get_react_agents() or {}
    custom_nodes = supervisor_sub_agent_registry.get_custom_nodes() or {}

    agent_registry_json: dict[str, Any] = {"react_agents": [], "custom_nodes": []}

    all_tools_by_name: dict[str, dict[str, str]] = {}
    tool_to_agent_map: OrderedDict[str, str] = OrderedDict()
    agent_names: list[str] = []

    # ReAct agents (with tools)
    for agent_name in sorted(react_agents.keys()):
        cfg = react_agents[agent_name] or {}
        desc = cfg.get("description", "ReAct agent")
        tools = cfg.get("tools", []) or []

        tool_names: list[str] = []
        for t in tools:
            name = _tool_name(t)
            if not name:
                continue
            tool_names.append(name)

            # catalog
            if name not in all_tools_by_name:
                all_tools_by_name[name] = {"name": name, "description": _tool_desc(t) or ""}
            elif not all_tools_by_name[name].get("description"):
                all_tools_by_name[name]["description"] = _tool_desc(t) or ""

            # mapping
            tool_to_agent_map[name] = agent_name

        agent_registry_json["react_agents"].append({
            "name": agent_name,
            "description": desc,
            "tools": _uniq_sorted(tool_names),
        })
        agent_names.append(agent_name)

    # Custom nodes
    for node_name in sorted(custom_nodes.keys()):
        cfg = custom_nodes[node_name] or {}
        desc = cfg.get("description", "")
        agent_registry_json["custom_nodes"].append({
            "name": node_name,
            "description": desc,
        })
        agent_names.append(node_name)

    agent_names = _uniq_sorted(agent_names)

    # tool_catalog_json
    tool_catalog_json = {"tools": sorted(all_tools_by_name.values(), key=lambda d: d["name"])}

    # tool_to_agent_mapping_json
    tool_to_agent_sorted: OrderedDict[str, str] = OrderedDict(sorted(tool_to_agent_map.items(), key=lambda kv: kv[0]))

    # Human-readable overviews
    approved_agents_overview = ", ".join(a["name"] for a in agent_registry_json["react_agents"]) \
        + (", " + ", ".join(n["name"] for n in agent_registry_json["custom_nodes"]) if agent_registry_json["custom_nodes"] else "")

    approved_tools_overview = ", ".join(t["name"] for t in tool_catalog_json["tools"]) if tool_catalog_json["tools"] else ""

    approved_tool_to_agent_overview = ", ".join(f"{tool}→{agent}" for tool, agent in tool_to_agent_sorted.items())

    # Enums
    tool_name_enum = [t["name"] for t in tool_catalog_json["tools"]]
    agent_name_enum = agent_names
    tool_to_agent_pairs = [[tool, agent] for tool, agent in tool_to_agent_sorted.items()]

    # Live context
    table_summary = state.get("table_summary", "") if isinstance(state, dict) else ""
    user_view_filters = state.get("user_view_filters", {}) if isinstance(state, dict) else {}

    ###########################################################
    # BUILD PROMPT AND CALL THE LLM  
    ###########################################################

    prompt_variables = {

        # Add the user request for template variables
        "supervisor_request": supervisor_instructions.content,
        # Previously prompt_injections - now in same format
        "agent_registry_json": pyjson.dumps(agent_registry_json, ensure_ascii=False),
        "tool_catalog_json": pyjson.dumps(tool_catalog_json, ensure_ascii=False),
        "tool_to_agent_mapping_json": pyjson.dumps(tool_to_agent_sorted, ensure_ascii=False),
        "user_view_filters": pyjson.dumps(user_view_filters or {}, ensure_ascii=False),
        "approved_agents_overview": approved_agents_overview,
        "approved_tools_overview": approved_tools_overview,
        "approved_tool_to_agent_overview": approved_tool_to_agent_overview,
        "tool_name_enum": pyjson.dumps(tool_name_enum, ensure_ascii=False),
        "agent_name_enum": pyjson.dumps(agent_name_enum, ensure_ascii=False),
        "tool_to_agent_pairs": pyjson.dumps(tool_to_agent_pairs, ensure_ascii=False),
    }
    from src.agents.shared.langsmith_factory import get_langsmith_prompt_async
    # Use the LangSmith prompt's model directly with structured output and LLM Model
    prompt = await get_langsmith_prompt_async("planner_agent")
    
    # Check and fix guardrails if needed
    if hasattr(prompt, 'model') and hasattr(prompt.model, 'guardrails'):
        print("[DEBUG] SUPERVISOR prompt.model.guardrails:", prompt.model.guardrails)
        if prompt.model.guardrails == {}:
            prompt.model.guardrails = None
            print("[DEBUG] SUPERVISOR prompt.model.guardrails set to None:", prompt.model.guardrails)
    
# Create a detailed plan to fulfill this request. Break it down into specific, actionable tasks.
# Each task should be atomic and executable independently where possible.
# Consider tool availability and user context when creating tasks.    
    
    response = await prompt.ainvoke(prompt_variables)
    validation = Plan(**response.model_dump())

    try:
        task_list = validation.tasks
        
        if not task_list:
            return {
                "plan_tasks": [],
                "messages": [AIMessage(content="I couldn't create a specific plan for your request. Let me help you directly.")],
                "last_error_message": "No plan created", ## TODO This is only for testing, remove later
            }
        return {
            "plan_tasks": task_list,
            "messages": [AIMessage(content="Plan created successfully.")],
            }

    except Exception as e:
        return {
            "plan_tasks": [],
            "messages": [AIMessage(content="I encountered an issue while planning. Let me assist you directly.")],
            "last_error_message": f"Planning error: {str(e)}",
        }


### utils for building values 

def _tool_name(t):
    # Accepts: LC tool objects, callables, strings, or dicts
    if t is None:
        return None
    if hasattr(t, "name") and isinstance(getattr(t, "name"), str):
        return t.name
    if hasattr(t, "__name__"):
        return t.__name__
    if isinstance(t, dict) and "name" in t:
        return t["name"]
    if isinstance(t, str):
        return t.strip()
    return str(t)

def _tool_desc(t):
    # Try to pull a short description where available
    if t is None:
        return ""
    if isinstance(t, dict) and "description" in t:
        return t["description"]
    if hasattr(t, "description") and isinstance(getattr(t, "description"), str):
        return t.description
    # Fallback to empty; prompt already carries canonical tool blurbs
    return ""

def _uniq_sorted(seq):
    seen = set()
    out = []
    for x in seq:
        if x not in seen and x is not None and x != "":
            seen.add(x)
            out.append(x)
    return sorted(out)
