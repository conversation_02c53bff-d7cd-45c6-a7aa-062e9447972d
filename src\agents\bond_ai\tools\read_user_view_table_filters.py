"""Table filters management tools."""

from typing import Optional, Dict, <PERSON>, <PERSON><PERSON>, Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool, InjectedToolArg
from langgraph.config import get_stream_writer
from bond_ai.configuration import Configuration

from ..models.models import FilterGroup
from ..agent_db import get_table_filters, update_table_filters
from langgraph.types import Command
from langchain_core.messages import ToolMessage
from langchain_core.tools import InjectedToolCallId

@tool
def read_user_view_table_filters(
    tool_call_id: Annotated[str, InjectedToolCallId],
    config: Annotated[RunnableConfig, InjectedToolArg]
) -> Command:
    """Read the filters field from the current table.
    
    This tool retrieves only the filters field from the current table,
    which contains filter configurations that determine how data is filtered
    in table views.
    
    Parameters:
        config: Configuration injected by the system which contains table_id
        
    Returns:
        Tuple[Optional[Dict[str, Any]], Optional[str]]: Tuple containing (filters_data, error)
        where filters_data is the filters field if successful, None if failed
        and error is the error message if failed, None if successful
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"Reading table filters"})
        # Get the table_id from the configuration
        configuration = Configuration.from_runnable_config(config)
        table_id = configuration.table_id
        
        # Call the database function
        filters, error = get_table_filters(table_id)
        
        if error:
            return Command(update={
                "last_error_message": error,
                "messages": [
                    ToolMessage(
                        content=error,
                        tool_call_id=tool_call_id,
                    )
                ]
            })
            
        # Return the filters data
        return Command(update={
                "last_error_message": None,
                "messages": [
                    ToolMessage(
                        content= filters,
                        tool_call_id=tool_call_id,
                    )
                ]
            })
            
    except Exception as e:
        error_msg = f"Error reading table filters: {str(e)}"
        return Command(update={
                "last_error_message": error_msg,
                "messages": [
                    ToolMessage(
                        content= error_msg,
                        tool_call_id=tool_call_id,
                    )
                ]
            })


