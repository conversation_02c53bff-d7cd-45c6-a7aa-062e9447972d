{"count": 5, "columns": [{"cells": [{"value": "https://www.linkedin.com/in/abudi-mohamed-846b67164/", "cell_details": null, "injection_sequence": "{{1}}"}], "column_id": 1, "column_name": "LinkedIn Profile URL"}, {"cells": [{"value": "<PERSON><PERSON>", "cell_details": [{"name": "LinkedIn Profile", "value": "<PERSON><PERSON>"}, {"name": "Personal Info", "value": [{"name": "Full Name", "value": "<PERSON><PERSON>", "injection_sequence": "{{2.1.0}}"}, {"name": "First Name", "value": "<PERSON><PERSON>", "injection_sequence": "{{2.1.1}}"}, {"name": "Last Name", "value": "<PERSON>", "injection_sequence": "{{2.1.2}}"}, {"name": "Headline", "value": "Founder & Chief AI Officer.", "injection_sequence": "{{2.1.3}}"}, {"name": "Summary", "value": "I play around with AI to create cool tools.", "injection_sequence": "{{2.1.4}}"}, {"name": "Country", "value": "Germany", "injection_sequence": "{{2.1.5}}"}, {"name": "City", "value": "Berlin", "injection_sequence": "{{2.1.6}}"}, {"name": "Username", "value": "abu<PERSON>-mohamed-846b67164", "injection_sequence": "{{2.1.7}}"}], "injection_sequence": "{{2.1}}"}, {"name": "Experience", "value": [{"name": "Co-Founder", "value": [{"name": "Job Title", "value": "Co-Founder", "injection_sequence": "{{2.2.0.0}}"}, {"name": "Company Name", "value": "Outbond", "injection_sequence": "{{2.2.0.1}}"}, {"name": "Company URL", "value": "https://www.linkedin.com/company/outbond", "injection_sequence": "{{2.2.0.2}}"}, {"name": "Location", "value": "United States", "injection_sequence": "{{2.2.0.3}}"}, {"name": "Start Date", "value": "2025-01-01", "injection_sequence": "{{2.2.0.4}}"}, {"name": "End Date", "value": "current", "injection_sequence": "{{2.2.0.5}}"}], "injection_sequence": "{{2.2.0}}"}, {"name": "AI Solution Architect & Technical Project Manager (AWS)", "value": [{"name": "Job Title", "value": "AI Solution Architect & Technical Project Manager (AWS)", "injection_sequence": "{{2.2.1.0}}"}, {"name": "Company Name", "value": "DCI Digital Career Institute", "injection_sequence": "{{2.2.1.1}}"}, {"name": "Company URL", "value": "https://www.linkedin.com/school/dci-digital-career-institute", "injection_sequence": "{{2.2.1.2}}"}, {"name": "Location", "value": "Berlin, Germany", "injection_sequence": "{{2.2.1.3}}"}, {"name": "Start Date", "value": "2021-02-01", "injection_sequence": "{{2.2.1.4}}"}, {"name": "End Date", "value": "2024-08-31", "injection_sequence": "{{2.2.1.5}}"}], "injection_sequence": "{{2.2.1}}"}, {"name": "Automation Engineer", "value": [{"name": "Job Title", "value": "Automation Engineer", "injection_sequence": "{{2.2.2.0}}"}, {"name": "Company Name", "value": "heartbeat medical", "injection_sequence": "{{2.2.2.1}}"}, {"name": "Company URL", "value": "https://www.linkedin.com/company/heartbeat-medical-solutions", "injection_sequence": "{{2.2.2.2}}"}, {"name": "Location", "value": "Berlin, Germany", "injection_sequence": "{{2.2.2.3}}"}, {"name": "Start Date", "value": "2020-02-01", "injection_sequence": "{{2.2.2.4}}"}, {"name": "End Date", "value": "2021-02-28", "injection_sequence": "{{2.2.2.5}}"}], "injection_sequence": "{{2.2.2}}"}, {"name": "Automation Engineer", "value": [{"name": "Job Title", "value": "Automation Engineer", "injection_sequence": "{{2.2.3.0}}"}, {"name": "Company Name", "value": "German Autolabs", "injection_sequence": "{{2.2.3.1}}"}, {"name": "Company URL", "value": "https://www.linkedin.com/company/german-autolabs", "injection_sequence": "{{2.2.3.2}}"}, {"name": "Location", "value": "Berlin Area, Germany", "injection_sequence": "{{2.2.3.3}}"}, {"name": "Start Date", "value": "2019-01-01", "injection_sequence": "{{2.2.3.4}}"}, {"name": "End Date", "value": "2020-01-31", "injection_sequence": "{{2.2.3.5}}"}], "injection_sequence": "{{2.2.3}}"}, {"name": "Working Student Banking Operations", "value": [{"name": "Job Title", "value": "Working Student Banking Operations", "injection_sequence": "{{2.2.4.0}}"}, {"name": "Company Name", "value": "N26", "injection_sequence": "{{2.2.4.1}}"}, {"name": "Company URL", "value": "https://www.linkedin.com/company/n26", "injection_sequence": "{{2.2.4.2}}"}, {"name": "Location", "value": "Berlin Area, Germany", "injection_sequence": "{{2.2.4.3}}"}, {"name": "Start Date", "value": "2018-01-01", "injection_sequence": "{{2.2.4.4}}"}, {"name": "End Date", "value": "2018-12-31", "injection_sequence": "{{*******}}"}], "injection_sequence": "{{2.2.4}}"}], "injection_sequence": "{{2.2}}"}, {"name": "Education", "value": [{"name": "Hochschule für Technik und Wirtschaft Berlin", "value": [{"name": "School", "value": "Hochschule für Technik und Wirtschaft Berlin", "injection_sequence": "{{*******}}"}, {"name": "Field of Study", "value": "Computer Science", "injection_sequence": "{{*******}}"}, {"name": "Degree", "value": "Bachelor's degree", "injection_sequence": "{{*******}}"}, {"name": "Description", "value": null, "injection_sequence": "{{*******}}"}, {"name": "School URL", "value": "https://www.linkedin.com/school/htwberlin", "injection_sequence": "{{*******}}"}, {"name": "Start Date", "value": "2018-01-01", "injection_sequence": "{{*******}}"}, {"name": "End Date", "value": "2021-01-31", "injection_sequence": "{{*******}}"}], "injection_sequence": "{{2.3.0}}"}, {"name": "Nahda University - NUB", "value": [{"name": "School", "value": "Nahda University - NUB", "injection_sequence": "{{*******}}"}, {"name": "Field of Study", "value": "Civil Engineering", "injection_sequence": "{{*******}}"}, {"name": "Degree", "value": "Bachelor's degree", "injection_sequence": "{{*******}}"}, {"name": "Description", "value": null, "injection_sequence": "{{*******}}"}, {"name": "School URL", "value": "https://www.linkedin.com/school/nahda-university---nub", "injection_sequence": "{{*******}}"}, {"name": "Start Date", "value": "2012-01-01", "injection_sequence": "{{*******}}"}, {"name": "End Date", "value": "2015-01-31", "injection_sequence": "{{*******}}"}], "injection_sequence": "{{2.3.1}}"}], "injection_sequence": "{{2.3}}"}, {"name": "Certifications", "value": [{"name": "AWS re/Start Accredited Instructor", "value": [{"name": "Name", "value": "AWS re/Start Accredited Instructor", "injection_sequence": "{{*******}}"}, {"name": "Authority", "value": "Amazon Web Services (AWS)", "injection_sequence": "{{*******}}"}, {"name": "Start Date", "value": "2022-06-01", "injection_sequence": "{{*******}}"}, {"name": "End Date", "value": "2025-05-31", "injection_sequence": "{{*******}}"}], "injection_sequence": "{{2.4.0}}"}, {"name": "AWS Certified Solutions Architect – Associate", "value": [{"name": "Name", "value": "AWS Certified Solutions Architect – Associate", "injection_sequence": "{{2.4.1.0}}"}, {"name": "Authority", "value": "Amazon Web Services (AWS)", "injection_sequence": "{{2.4.1.1}}"}, {"name": "Start Date", "value": "2022-05-01", "injection_sequence": "{{2.4.1.2}}"}, {"name": "End Date", "value": "2025-05-31", "injection_sequence": "{{2.4.1.3}}"}], "injection_sequence": "{{2.4.1}}"}, {"name": "AWS Certified Cloud Practitioner", "value": [{"name": "Name", "value": "AWS Certified Cloud Practitioner", "injection_sequence": "{{2.4.2.0}}"}, {"name": "Authority", "value": "Amazon Web Services (AWS)", "injection_sequence": "{{2.4.2.1}}"}, {"name": "Start Date", "value": "2022-02-01", "injection_sequence": "{{2.4.2.2}}"}, {"name": "End Date", "value": "2025-02-28", "injection_sequence": "{{2.4.2.3}}"}], "injection_sequence": "{{2.4.2}}"}], "injection_sequence": "{{2.4}}"}], "injection_sequence": "{{2}}"}], "column_id": 2, "column_name": "Linkedin Profile"}, {"cells": [{"value": "Outbond", "cell_details": [{"name": "LinkedIn company profile", "value": "Outbond"}, {"name": "General info", "value": [{"name": "Name", "value": "Outbond", "injection_sequence": "{{3.1.0}}"}, {"name": "LinkedIn URL", "value": "https://www.linkedin.com/company/outbond", "injection_sequence": "{{3.1.1}}"}, {"name": "Website", "value": "https://www.outbond.io/", "injection_sequence": "{{3.1.2}}"}, {"name": "Tagline", "value": "Grow Your Revenue with Personalized AI Outreach", "injection_sequence": "{{3.1.3}}"}, {"name": "Description", "value": "Grow Your Revenue With Personalized AI Outreach", "injection_sequence": "{{3.1.4}}"}, {"name": "Type", "value": null, "injection_sequence": "{{3.1.5}}"}, {"name": "Phone", "value": "", "injection_sequence": "{{3.1.6}}"}, {"name": "Staff count", "value": 2, "injection_sequence": "{{3.1.7}}"}, {"name": "Follower count", "value": 38, "injection_sequence": "{{3.1.8}}"}, {"name": "ID", "value": "*********", "injection_sequence": "{{3.1.9}}"}, {"name": "Founded year", "value": null, "injection_sequence": "{{3.1.10}}"}, {"name": "Crunchbase URL", "value": "", "injection_sequence": "{{3.1.11}}"}, {"name": "Logo", "value": "https://s3.us-west-000.backblazeb2.com/proxycurl/company/outbond/profile?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=0004d7f56a0400b0000000001%2F20250421%2Fus-west-000%2Fs3%2Faws4_request&X-Amz-Date=20250421T145828Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=68b355cc5bdc51c1862de32f7259bff220f89c10309a89b5d0aaa96cb998898f", "injection_sequence": "{{3.1.12}}"}], "injection_sequence": "{{3.1}}"}, {"name": "Industry", "value": "Technology, Information and Internet", "injection_sequence": "{{3.2}}"}], "injection_sequence": "{{3}}"}], "column_id": 3, "column_name": "Linkedin Company"}, {"cells": [{"value": "<EMAIL>", "cell_details": [{"name": "Email", "value": "<EMAIL>", "injection_sequence": "{{4.0}}"}, {"name": "Source", "value": "Prospeo"}, {"name": "Prospeo Email Status", "value": "VALID"}, [{"name": "Validation", "value": [{"name": "Source", "value": "Millionverifier"}, {"name": "Quality", "value": "good"}]}]], "injection_sequence": "{{4}}"}], "column_id": 4, "column_name": "Email"}, {"cells": [{"value": null, "cell_details": null, "injection_sequence": "{{5}}"}], "column_id": 5, "column_name": "Phone"}]}